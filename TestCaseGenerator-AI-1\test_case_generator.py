import requests
import json
import os
from typing import Dict, Any, Optional
from pathlib import Path
from retry import retry
from openai import OpenAI
from config import config
import httpx

class TestCaseGenerator:
    def __init__(self):
        self.config = config
        self.cancel_flag = False
        self.update_callback = None
        self._client = None
        self.system_prompt = "你是一个专业的测试工程师，擅长编写测试用例。"
        self.user_prompt = "请根据以下需求文档生成测试用例，使用Markdown格式："

    def set_prompts(self, system_prompt: str, user_prompt: str):
        """设置生成提示词"""
        if system_prompt:
            self.system_prompt = system_prompt
        if user_prompt:
            self.user_prompt = user_prompt

    def set_update_callback(self, callback):
        self.update_callback = callback

    def cancel(self):
        self.cancel_flag = True

    def _update_progress(self, progress: int, status: str):
        if self.update_callback:
            self.update_callback(progress, status)

    def _get_client(self):
        """获取或创建API客户端"""
        if not self._client:
            self._client = httpx.Client(
                base_url=self.config.get("api", "url"),
                headers={
                    "Authorization": f"Bearer {self.config.get('api', 'key')}",
                    "Content-Type": "application/json"
                },
                timeout=10.0
            )
        return self._client

    def generate(self, document_text: str) -> Optional[str]:
        """生成测试用例"""
        if not document_text:
            raise ValueError("需求文档内容不能为空")

        self.cancel_flag = False
        self._update_progress(0, "正在准备生成测试用例...")

        try:
            client = self._get_client()
            
            # 检查API配置
            if not self.config.get("api", "key"):
                raise ValueError("请配置API密钥")
            if not self.config.get("api", "url"):
                raise ValueError("请配置API URL")

            # 准备请求数据
            self._update_progress(10, "正在处理需求文档...")
            
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": f"{self.user_prompt}\n\n{document_text}"}
            ]

            data = {
                "model": self.config.get("api", "model"),
                "messages": messages,
                "temperature": float(self.config.get("api", "temperature", 0.7)),
                "stream": True
            }

            self._update_progress(20, "正在连接API服务器...")

            # 发送请求
            self._update_progress(30, f"正在使用 {self.config.get('api', 'model')} 模型生成测试用例...")
            response = client.post("/chat/completions", json=data, timeout=120.0)
            response.raise_for_status()

            # 处理响应
            result = []
            progress = 30
            total_chunks = 0

            try:
                # 首先尝试非流式响应
                response_json = response.json()
                if response_json.get("choices"):
                    content = response_json["choices"][0].get("message", {}).get("content", "")
                    if content:
                        self._update_progress(100, "测试用例生成完成")
                        return content
            except json.JSONDecodeError:
                # 如果不是JSON响应，则按流式处理
                for line in response.iter_lines():
                    if self.cancel_flag:
                        self._update_progress(100, "已取消生成")
                        return None

                    if line:
                        try:
                            line_str = line.decode('utf-8') if isinstance(line, bytes) else line
                            
                            if line_str.startswith("data: "):
                                chunk_str = line_str[6:].strip()
                                if chunk_str == "[DONE]":
                                    continue
                                    
                                chunk = json.loads(chunk_str)
                                if chunk.get("choices"):
                                    content = chunk["choices"][0].get("delta", {}).get("content", "")
                                    if content:
                                        result.append(content)
                                        total_chunks += 1
                                        # 更新进度，确保进度平滑增加
                                        if total_chunks % 5 == 0:  # 每5个chunk更新一次进度
                                            progress = min(95, 30 + (total_chunks // 5))
                                            self._update_progress(progress, f"正在生成测试用例... ({progress}%)")
                        except Exception as e:
                            print(f"处理响应数据时出错: {str(e)}")
                            continue

            if self.cancel_flag:
                return None

            if not result:
                raise RuntimeError("未能获取到有效的生成结果")

            self._update_progress(100, "测试用例生成完成")
            return "".join(result)

        except Exception as e:
            raise RuntimeError(f"生成测试用例时发生错误: {str(e)}")
        finally:
            if self._client:
                self._client.close()
                self._client = None

    def _upload_file(self, file_path: str) -> str:
        """上传文档到API服务"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
                
            self._update_progress(15, "正在上传文档...")
            file_object = self._client.files.create(
                file=Path(file_path),
                purpose="file-extract"
            )
            
            if not file_object or not hasattr(file_object, 'id'):
                raise ValueError("文件上传失败：未获取到有效的file_id")
                
            self._update_progress(20, "文档上传成功")
            return file_object.id
        except Exception as e:
            self._update_progress(-1, f"文件上传失败: {str(e)}")
            raise

    def _delete_file(self, file_id: str):
        """删除云端文件"""
        try:
            self._client.files.delete(file_id)
        except Exception as e:
            print(f"警告：删除云端文件失败: {str(e)}")

    def _preprocess_json_content(self, content: str) -> str:
        """预处理API返回的内容，确保其符合JSON格式"""
        try:
            # 移除 Markdown 代码块标记
            content = content.strip()
            content = content.replace('```json', '').replace('```', '')
            
            # 尝试直接解析 JSON
            try:
                parsed = json.loads(content)
                return json.dumps(parsed, ensure_ascii=False, indent=2)
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试提取 JSON 部分
                json_match = content
                if content.find('[') >= 0 and content.find(']') >= 0:
                    json_match = content[content.find('['):content.rfind(']')+1]
                elif content.find('{') >= 0 and content.find('}') >= 0:
                    json_match = content[content.find('{'):content.rfind('}')+1]
                else:
                    raise ValueError("未找到有效的 JSON 内容")
                
                parsed = json.loads(json_match)
                return json.dumps(parsed, ensure_ascii=False, indent=2)
                
        except Exception as e:
            raise ValueError(f"JSON预处理失败: {str(e)}\n原始内容: {content[:200]}...") 