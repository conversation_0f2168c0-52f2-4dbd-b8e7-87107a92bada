import pandas as pd
import re

def clean_text(text):
    """
    This function cleans the input text by removing special characters,
    converting to lowercase, and removing extra spaces.
    """
    text = re.sub(r"[^a-zA-Z0-9\s]", "", text)
    text = text.lower()
    text = " ".join(text.split())
    return text

def remove_duplicates(df, column_name):
    """
    This function removes duplicate rows based on a specific column.
    """
    df = df.drop_duplicates(subset=[column_name])
    return df

def remove_missing_values(df):
    """
    This function removes rows with missing values.
    """
    df = df.dropna()
    return df

def main():
    # Load the data
    try:
        df = pd.read_csv("data.csv")  # Replace "data.csv" with your actual file
    except FileNotFoundError:
        print("Error: data.csv not found. Please make sure the file exists.")
        return

    # Clean the text data (assuming you have a text column named "text")
    if "text" in df.columns:
        df["text"] = df.apply(lambda row: clean_text(row["text"]), axis=1)
    else:
        print("Warning: 'text' column not found. Skipping text cleaning.")

    # Remove duplicates based on the text column
    if "text" in df.columns:
        df = remove_duplicates(df, "text")
    else:
        print("Warning: 'text' column not found. Skipping duplicate removal.")

    # Remove rows with missing values
    df = remove_missing_values(df)

    # Save the cleaned data
    df.to_csv("cleaned_data.csv", index=False)
    print("Data cleaning complete. Cleaned data saved to cleaned_data.csv")

if __name__ == "__main__":
    main()