<execution>
  <process>
    # 资源处理#流程
    
    ```mermaid
    flowchart TD
      A[识别#资源引用] --> B{判断#加载语义}
      B -->|@!前缀| C[立即执行#工具调用]
      B -->|@?前缀| D[记录位置暂不#加载]
      B -->|@默认| E[根据上下文决定]
      C --> F[验证#加载结果]
      E --> F
      F -->|成功| G[处理#资源内容]
      F -->|失败| H[执行失败处理]
      D --> I[等待使用触发]
      I --> J[需要使用时#加载]
      J --> F
    ```
  </process>
  
  <rule>
    1. AI必须主动使用#工具调用获取#资源，不等待系统自动#加载
    2. 遇到@!前缀#资源必须立即执行#工具调用获取内容
    3. 遇到@?前缀#资源应记录位置但暂不#加载
    4. 必须验证#资源是否成功#加载并处理失败情况
  </rule>
  
  <constraint>
    1. 工具调用能力限制（不同AI系统支持的工具不同）
    2. 资源访问权限限制
    3. 资源大小和格式限制
  </constraint>
  
  <guideline>
    1. 优先处理关键资源，确保核心功能不受资源加载问题影响
    2. 资源内容应适当缓存，避免重复加载
    3. 大型资源考虑分段加载以优化性能
  </guideline>
  
  <criteria>
    | 指标 | 通过标准 | 不通过标准 |
    |------|---------|-----------|
    | 加载及时性 | @!资源被立即加载 | 忽略加载语义前缀 |
    | 错误处理 | 妥善处理加载失败 | 加载失败无响应 |
    | 懒加载执行 | @?资源仅在需要时加载 | 过早加载或完全不加载 |
    | 完整性 | 资源内容完整获取 | 内容截断或损坏 |
  </criteria>
</execution>