/* package.module.Abc => Abc */
#sideMenu a[href*='module.']:has(wbr) > span:not(:last-child) {
    display: none;
}

#sideMenu .sideMenu {
    display: flex;
    flex-direction: column;
}

#sideMenu .sideMenu > .toc--part:not([data-active]) > .toc--row > .toc--button:not(:hover) {
    background-color: transparent;
}

#sideMenu .sideMenu > .toc--part {
    order: 10;
}

/* User libraries (e.g. stdlib) show on top. */
#sideMenu .sideMenu > .toc--part:has(a[href*='module.']) {
    order: 1;
}

/* Shows a label on top of user libraries. */
.toc--part[data-nesting-level="0"]:has(a[href*='module.']) > .toc--row > a:before {
    content: 'User Library';
    display: block;
    text-transform: uppercase;
    opacity: .5;
}

#sideMenu .sideMenu > .toc--part:has(a[href*='module.']) > .toc--row {
    background-color: rgba(77, 187, 95, .1);
}

/* If a package contains Quarkdown modules, non-module packages are semi-hidden. */
.toc--part:has(a[href*='module.']) > .toc--part:has(a:not([href*='module.'])) > .toc--row {
    opacity: .5;
}

/* Extra parameter properties. */
.table-row dl > ul {
    opacity: .5;
    padding: 0;
    display: inline-flex;
    flex-direction: row;
    list-style-position: inside;
    gap: 0.7em;
}

.table-row dl > ul > li:first-child {
    list-style: none;
}
