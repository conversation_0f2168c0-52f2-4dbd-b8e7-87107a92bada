<execution domain="prompt-engineering">
  <process>
    # 资源模式提示词开发流程
    
    ```mermaid
    flowchart TD
      A[明确资源需求] --> B[设计资源路径结构]
      B --> C[定义查询参数]
      C --> D[建立资源注册表]
      D --> E[验证资源协议]
      E -->|通过| F[完成资源定义]
      E -->|不通过| G[调整修正]
      G --> B
    ```
    
    ## 核心步骤详解
    
    1. **明确资源需求**
       - 确定资源类型和用途
       - 定义资源的生命周期和作用域
       - 规划资源的访问模式
    
    2. **设计资源路径结构**
       - 使用`<location>`标签定义路径规则
       - 通过EBNF形式描述路径结构
       - 提供清晰的路径示例
    
    3. **定义查询参数**
       - 使用`<params>`标签定义参数
       - 明确参数名称、类型和作用
       - 设计参数组合规则和优先级
    
    4. **建立资源注册表**
       - 使用`<registry>`标签建立映射
       - 将抽象ID映射到具体资源路径
       - 确保映射关系清晰且无冲突
    
    5. **验证资源协议**
       - 测试资源引用的解析正确性
       - 验证资源加载语义（@、@!、@?）
       - 检查嵌套引用和查询参数功能
  </process>
  
  <guideline>
    ### 资源路径设计指南
    
    - **简洁明确**：路径应当简洁但足够明确，避免歧义
      ```
      @file://documents/report.md  ✓
      @file://docs/some-stuff/thing.md  ✗
      ```
    
    - **分层结构**：使用层级结构组织资源，增强可读性
      ```
      @file://project/src/components/button.js  ✓
      @file://project_src_components_button.js  ✗
      ```
    
    - **命名规范**：使用一致的命名规则
      ```
      @file://images/logo.png  ✓
      @file://Images/LOGO.png  ✗
      ```
    
    - **通配符合理使用**：
      ```
      @file://src/**/*.js  ✓ (递归查找所有JS文件)
      @file://**/*  ✗ (过于宽泛)
      ```
    
    ### 查询参数设计指南
    
    - **参数命名**：使用描述性名称，遵循常见约定
      ```
      ?format=json  ✓
      ?f=j  ✗
      ```
    
    - **参数分组**：相关参数应使用一致的前缀
      ```
      ?filter.type=user&filter.status=active  ✓
      ?filter_type=user&status_filter=active  ✗
      ```
    
    - **默认值处理**：明确指定参数的默认行为
      ```
      在<params>中说明: "format默认为'text'"  ✓
      不指定默认值  ✗
      ```
    
    ### 注册表设计指南
    
    - **ID命名**：使用有意义的ID，体现资源内容
      ```
      analytical (用于分析型思维)  ✓
      thought1, thought2  ✗
      ```
    
    - **路径模板**：对于相似资源，使用一致的路径模板
      ```
      @file://PromptX/domain/{domain}/{resource}.{type}.md  ✓
      混合不同路径风格  ✗
      ```
    
    - **分类组织**：按功能或领域对注册表条目分组
      ```
      <!-- 基础思维模式 -->
      | analytical | ... |
      | creative | ... |
      
      <!-- 专业领域思维模式 -->
      | medical | ... |
      ```
      
    ### 资源引用指南
    
    - **加载语义选择**：
      - `@`：一般资源，非关键，可延迟加载
      - `@!`：关键资源，必须立即加载
      - `@?`：大型资源，仅在需要时加载
    
    - **嵌套引用建议**：
      - 简单情况使用简写形式：`@execution:file://path.md`
      - 复杂情况使用完整形式：`@execution:@file://path.md`
      - 多重嵌套不超过3层：`@outer:middle:inner://path`
  </guideline>
  
  <rule>
    1. **路径格式一致性** - 资源路径必须遵循EBNF中定义的语法规则
    2. **三要素完整性** - 自定义协议必须包含location、params和registry三个核心组件
    3. **加载语义明确性** - 资源引用必须明确其加载语义（@、@!、@?）的使用场景
    4. **查询参数规范化** - 参数名称和值格式必须明确规范
    5. **注册表唯一性** - 注册表中的ID必须唯一，不允许重复
    6. **资源获取主动性** - AI必须主动使用工具调用获取资源，特别是@!前缀的资源
    7. **路径解析完整性** - 必须正确处理嵌套引用，从内向外解析
    8. **资源验证必要性** - 必须验证资源是否成功加载，并妥善处理加载失败情况
  </rule>
  
  <constraint>
    1. **路径长度限制** - 资源路径不应过长，建议不超过255字符
    2. **嵌套深度限制** - 嵌套引用不应超过3层，以保持可读性
    3. **查询参数复杂度限制** - 单个资源引用的查询参数不宜过多
    4. **注册表大小限制** - 单个注册表条目数量应控制在可管理范围内
    5. **资源访问权限限制** - 需考虑环境对资源访问的权限限制
    6. **解析环境限制** - 资源路径和参数需考虑在不同解析环境中的兼容性
  </constraint>
  
  <criteria>
    | 指标 | 通过标准 | 不通过标准 |
    |------|---------|-----------|
    | 路径清晰度 | 路径结构直观易懂 | 路径结构混乱或难以理解 |
    | 参数设计合理性 | 参数命名明确，功能清晰 | 参数命名模糊，功能重叠 |
    | 注册表组织性 | 注册表条目分类合理，ID有意义 | 注册表混乱，ID无意义 |
    | 加载语义正确性 | 正确使用@、@!、@?前缀 | 加载语义使用不当 |
    | 嵌套引用可读性 | 嵌套结构清晰，不过度复杂 | 嵌套过深或结构混乱 |
    | 资源获取可靠性 | 资源加载有验证和错误处理 | 缺少验证或错误处理 |
    | 通配符使用合理性 | 通配符模式精确且高效 | 过于宽泛或低效的模式 |
    | 整体一致性 | 资源协议设计风格统一 | 设计风格不一致或混乱 |
  </criteria>
</execution> 