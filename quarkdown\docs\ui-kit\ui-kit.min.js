(()=>{"use strict";function t(e,n){return!(!e||!e.classList.contains(n))||!!e.parentElement&&t(e.parentElement,n)}document.addEventListener("DOMContentLoaded",(function(){document.querySelectorAll("div.button").forEach((function(t){t.addEventListener("keydown",(function(e){var n=e.key;"Enter"!==n&&" "!==n||t.dispatchEvent(new MouseEvent("click"))}))}))}));function e(){return window.innerWidth<440?"mobile":window.innerWidth>=440&&window.innerWidth<900?"tablet":"desktop"}var n=function(){function t(t){this.trapElement=t,this.handleKeyDown=this.handleKeyDown.bind(this),this.trapElement.addEventListener("keydown",this.handleKeyDown)}return t.prototype.handleKeyDown=function(t){var e=Array.from(this.trapElement.querySelectorAll('[role="option"]')).filter((function(t){return"none"!==t.style.display&&-1!==t.tabIndex}));if(["Tab","ArrowDown","ArrowUp"].includes(t.key)&&0!==e.length){var n=e[0],o=e[e.length-1];if("ArrowUp"===t.key)if(document.activeElement===n)o.focus();else{var r=e.indexOf(document.activeElement);e[r-1].focus()}"ArrowDown"===t.key&&(document.activeElement===o?n.focus():(r=e.indexOf(document.activeElement),e[r+1].focus())),"Tab"===t.key&&(t.shiftKey?document.activeElement===n&&(o.focus(),t.preventDefault()):document.activeElement===o&&(n.focus(),t.preventDefault()))}},t.prototype.destroy=function(){this.trapElement.removeEventListener("keydown",this.handleKeyDown)},t}(),o='[data-role="dropdown"]',r='[data-role="dropdown-toggle"]',i='[data-role="dropdown-listbox"]';function l(t){var e,n,o=t.querySelectorAll(r);null==o||o.forEach(a),e=t.querySelector(i),n=o[0].offsetWidth,e&&(e.classList.toggle("dropdown--list_expanded"),e.classList.contains("dropdown--list_expanded")?c(e,n):c(e,void 0))}function a(t){t.classList.contains("button_dropdown")&&t.classList.toggle("button_dropdown_active")}function c(t,e){if(e){var n=parseInt(getComputedStyle(t).minWidth,10),o=isNaN(n)?e:Math.max(n,e);t.style.minWidth="".concat(o,"px")}else t.style.minWidth=""}function d(e){var n=e.target;t(n,"dropdown")&&"dropdown--overlay"!==n.className||document.querySelectorAll(o).forEach((function(t){var e,n;null===(e=t.querySelectorAll(r))||void 0===e||e.forEach((function(t){t.classList.remove("button_dropdown_active")})),null===(n=t.querySelectorAll(i))||void 0===n||n.forEach((function(t){t.classList.remove("dropdown--list_expanded"),t.style.minWidth=""}))}))}function u(t){t.tag.removeAttribute("style"),t.option.setAttribute("style","display: none")}function s(t){t.tag.setAttribute("style","display: none"),t.option.removeAttribute("style")}function f(t){var e,n=null===(e=t.querySelector(".checkbox--input"))||void 0===e?void 0:e.getAttribute("data-filter");n&&(-1===filteringContext.activeFilters.findIndex((function(t){return t===n}))?unfilterSourceset(n):filterSourceset(n)),refreshFiltering()}document.addEventListener("DOMContentLoaded",(function(){document.querySelectorAll(o).forEach((function(t){var e;null===(e=t.querySelectorAll(r))||void 0===e||e.forEach((function(e){e.addEventListener("click",(function(){return l(t)}))})),function(t){new n(t),t.addEventListener("keydown",(function(e){var n;"Escape"===e.key&&(l(t),null===(n=t.querySelector(r))||void 0===n||n.focus())}))}(t)})),document.addEventListener("click",d)})),document.addEventListener("DOMContentLoaded",(function(){var t=document.getElementById("navigation-wrapper"),n=document.getElementById("library-version"),o=document.getElementById("filter-section"),r=document.querySelector("#filter-section + .navigation-controls--btn"),i=document.getElementById("filter-section-dropdown");if(t&&n&&o&&r&&i){var l=null==o?void 0:o.querySelectorAll(".dropdown--option"),a=null==o?void 0:o.querySelectorAll(".platform-selector");if(a&&l)if(a.length===l.length){var c=Array.from({length:a.length}).map((function(t,e){return{tag:a[e],option:l[e]}})),d=c.map((function(t){return t.tag.getBoundingClientRect().width})),v=e(),m=new ResizeObserver((function(){var n=e();v!==n&&(c.forEach(u),d=function(t){return t.map((function(t){return t.tag.getBoundingClientRect().width}))}(c)),v=n,y(),m.unobserve(t)})),g=function(){m.observe(t)};y(),g(),l.forEach((function(t){t.addEventListener("click",(function(t){f(t.target)})),t.addEventListener("keydown",(function(t){var e=t.key;"Enter"!==e&&" "!==e||f(t.target)}))})),window.addEventListener("resize",g)}else console.warn("Dokka: filter section items are not equal");else console.warn("Dokka: filter section items are not found")}else console.warn("Dokka: filter section is not found");function y(){var e,l;if(t&&i){if(t.getBoundingClientRect().width<900)return c.forEach(s),void i.removeAttribute("style");var a=(n&&r?r.getBoundingClientRect().left-n.getBoundingClientRect().right:0)-44-10,f=0;i.removeAttribute("style");var v=!1;c.forEach((function(t,e){(f+=d[e]+4)<a&&e<8?(u(t),v=!0):(s(t),i.setAttribute("style","display: block"))})),v?null===(e=i.firstElementChild)||void 0===e||e.classList.remove("filter-section--dropdown-toggle_as-filters"):null===(l=i.firstElementChild)||void 0===l||l.classList.add("filter-section--dropdown-toggle_as-filters"),null==o||o.classList.remove("filter-section_loading")}}})),window.toggleTocDropdown=function(){var t=document.getElementById("toc-dropdown");t?l(t):console.warn("Dokka: toc dropdown is not found")},document.addEventListener("DOMContentLoaded",(function(){var t,e;t=document.getElementById("toc-toggle"),e=document.getElementById("toc-dropdown"),t&&e?t.addEventListener("click",(function(t){t.stopPropagation(),l(e)})):console.warn("Dokka: toc toggle or dropdown is not found")}));var v=function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],o=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},m=function(){var t=!1;try{var e="__testLocalStorageKey__";localStorage.setItem(e,e),localStorage.removeItem(e),t=!0}catch(t){console.error("Local storage is not available",t)}return{getItem:function(e){return t?localStorage.getItem(e):null},setItem:function(e,n){t&&localStorage.setItem(e,n)}}}();function g(t){var e,n=null===(e=t.getAttribute("data-togglable"))||void 0===e?void 0:e.split(",");!function(){var e,n,o,r;try{for(var i=v(document.getElementsByClassName("tabs-section")),l=i.next();!l.done;l=i.next()){var a=l.value;try{for(var c=(o=void 0,v(a.children)),d=c.next();!d.done;d=c.next()){var u=d.value;u.getAttribute("data-togglable")===t.getAttribute("data-togglable")?u.setAttribute("data-active",""):u.removeAttribute("data-active")}}catch(t){o={error:t}}finally{try{d&&!d.done&&(r=c.return)&&r.call(c)}finally{if(o)throw o.error}}}}catch(t){e={error:t}}finally{try{l&&!l.done&&(n=i.return)&&n.call(i)}finally{if(e)throw e.error}}}(),document.querySelectorAll(".tabs-section-body *[data-togglable]").forEach((function(t){var e=t.getAttribute("data-togglable");n&&e&&n.includes(e)?t.setAttribute("data-active",""):t.classList.contains("sourceset-dependent-content")||t.removeAttribute("data-active")}))}window.initTabs=function(){var t=document.querySelector(".main-content"),e="active-tab-"+(t?t.getAttribute("data-page-type"):null);document.querySelectorAll("div[tabs-section]").forEach((function(t){!function(t){var e=t.querySelector("button[data-active]");e&&g(e)}(t),t.addEventListener("click",(function(t){var n=t.target,o=n?n.getAttribute("data-togglable"):null;o&&(m.setItem(e,JSON.stringify(o)),g(n))}))}));var n=m.getItem(e);if(n){var o=document.querySelector('div[tabs-section] > button[data-togglable="'+JSON.parse(n)+'"]');o&&g(o)}},window.toggleSections=g,document.addEventListener("DOMContentLoaded",(function(){document.querySelectorAll('[data-remove-style="true"]').forEach((function(t){t.removeAttribute("style")}))}))})();