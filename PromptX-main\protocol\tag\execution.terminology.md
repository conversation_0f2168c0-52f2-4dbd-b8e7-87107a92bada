<terminologies>
  <terminology>
    <zh>行为提示单元</zh>
    <en>Execution Prompt Unit</en>
    <definition>
      由<execution>标签及其子标签（如process、guideline、rule、constraint、criteria）构成的、表达完整行为/执行过程的结构化提示词单元。常简称为"行为单元"，两者等同。
    </definition>
    <examples>
      <example>"所有操作流程都应以 #行为提示单元 组织。"</example>
      <example>"每个 #行为单元 都可以独立复用。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>行为单元</zh>
    <en>Execution Unit</en>
    <definition>
      "行为提示单元"的简称，含义完全等同。参见"行为提示单元"。
    </definition>
    <examples>
      <example>"请将你的执行方案拆分为多个 #行为单元。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>流程</zh>
    <en>Process</en>
    <definition>
      在本协议中，#流程 专指 <process> 标签及其结构单元，表示用于承载具体执行步骤、路径的提示词片段。
    </definition>
    <examples>
      <example>"请将详细步骤写入 #流程 单元（即 <process> 标签）。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>指导原则</zh>
    <en>Guideline</en>
    <definition>
      在本协议中，#指导原则 专指 <guideline> 标签及其结构单元，表示用于承载建议性、灵活调整的行为指导内容的提示词片段。
    </definition>
    <examples>
      <example>"所有建议请归入 #指导原则 单元（即 <guideline> 标签）。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>规则</zh>
    <en>Rule</en>
    <definition>
      在本协议中，#规则 专指 <rule> 标签及其结构单元，表示用于承载必须遵守的强制性行为准则的提示词片段。
    </definition>
    <examples>
      <example>"合规要求请写入 #规则 单元（即 <rule> 标签）。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>约束</zh>
    <en>Constraint</en>
    <definition>
      在本协议中，#约束 专指 <constraint> 标签及其结构单元，表示用于承载客观限制条件的提示词片段。
    </definition>
    <examples>
      <example>"所有不可更改的限制请写入 #约束 单元（即 <constraint> 标签）。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>标准</zh>
    <en>Criteria</en>
    <definition>
      在本协议中，#标准 专指 <criteria> 标签及其结构单元，表示用于承载评价标准、验收依据的提示词片段。
    </definition>
    <examples>
      <example>"验收要求请写入 #标准 单元（即 <criteria> 标签）。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>行为模式</zh>
    <en>Execution Mode</en>
    <definition>
      在本协议中，#行为模式 指不同类型的行为/执行方式，如 #流程、#指导原则、#规则、#约束、#标准 等，分别由 <process>、<guideline>、<rule>、<constraint>、<criteria> 标签实现。
    </definition>
    <examples>
      <example>"复杂任务可组合多种 #行为模式。"</example>
    </examples>
  </terminology>
</terminologies> 