@echo off

@if defined QD_NPM_PREFIX (
    set "NODE_PATH=%QD_NPM_PREFIX%\node_modules"
)

@if not defined PUPPETEER_EXECUTABLE_PATH (
    if exist "%ProgramFiles%\Google\Chrome\Application\chrome.exe" (
        set "PUPPETEER_EXECUTABLE_PATH=%ProgramFiles%\Google\Chrome\Application\chrome.exe"
    ) else if exist "%ProgramFiles%\Chromium\Application\chrome.exe" (
        set "PUPPETEER_EXECUTABLE_PATH=%ProgramFiles%\Chromium\Application\chrome.exe"
    ) else if exist "%ProgramFiles%\Mozilla Firefox\firefox.exe" (
        set "PUPPETEER_EXECUTABLE_PATH=%ProgramFiles%\Mozilla Firefox\firefox.exe"
        set "PUPPETEER_BROWSER=firefox"
    ) else if exist "%ProgramFiles(x86)%\Google\Chrome\Application\chrome.exe" (
        set "PUPPETEER_EXECUTABLE_PATH=%ProgramFiles(x86)%\Google\Chrome\Application\chrome.exe"
    ) else if exist "%ProgramFiles(x86)%\Mozilla Firefox\firefox.exe" (
        set "PUPPETEER_EXECUTABLE_PATH=%ProgramFiles(x86)%\Mozilla Firefox\firefox.exe"
        set "PUPPETEER_BROWSER=firefox"
    ) else (
        echo "[!] Could not find a suitable installation of Google Chrome, Chromium or Firefox. PDF generation may not be available."
    )
)

@rem
@rem Copyright 2015 the original author or authors.
@rem
@rem Licensed under the Apache License, Version 2.0 (the "License");
@rem you may not use this file except in compliance with the License.
@rem You may obtain a copy of the License at
@rem
@rem      https://www.apache.org/licenses/LICENSE-2.0
@rem
@rem Unless required by applicable law or agreed to in writing, software
@rem distributed under the License is distributed on an "AS IS" BASIS,
@rem WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
@rem See the License for the specific language governing permissions and
@rem limitations under the License.
@rem

@if "%DEBUG%"=="" @echo off
@rem ##########################################################################
@rem
@rem  quarkdown startup script for Windows
@rem
@rem ##########################################################################

@rem Set local scope for the variables with windows NT shell
if "%OS%"=="Windows_NT" setlocal

set DIRNAME=%~dp0
if "%DIRNAME%"=="" set DIRNAME=.
@rem This is normally unused
set APP_BASE_NAME=%~n0
set APP_HOME=%DIRNAME%..

@rem Resolve any "." and ".." in APP_HOME to make it shorter.
for %%i in ("%APP_HOME%") do set APP_HOME=%%~fi

@rem Add default JVM options here. You can also use JAVA_OPTS and QUARKDOWN_OPTS to pass JVM options to this script.
set DEFAULT_JVM_OPTS=

@rem Find java.exe
if defined JAVA_HOME goto findJavaFromJavaHome

set JAVA_EXE=java.exe
%JAVA_EXE% -version >NUL 2>&1
if %ERRORLEVEL% equ 0 goto execute

echo.
echo ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.
echo.
echo Please set the JAVA_HOME variable in your environment to match the
echo location of your Java installation.

goto fail

:findJavaFromJavaHome
set JAVA_HOME=%JAVA_HOME:"=%
set JAVA_EXE=%JAVA_HOME%/bin/java.exe

if exist "%JAVA_EXE%" goto execute

echo.
echo ERROR: JAVA_HOME is set to an invalid directory: %JAVA_HOME%
echo.
echo Please set the JAVA_HOME variable in your environment to match the
echo location of your Java installation.

goto fail

:execute
@rem Setup the command line

set CLASSPATH=%APP_HOME%\lib\quarkdown-1.6.3.jar;%APP_HOME%\lib\quarkdown-cli.jar;%APP_HOME%\lib\quarkdown-html.jar;%APP_HOME%\lib\quarkdown-interaction.jar;%APP_HOME%\lib\quarkdown-stdlib.jar;%APP_HOME%\lib\quarkdown-core.jar;%APP_HOME%\lib\quarkdown-libs.jar;%APP_HOME%\lib\quarkdown-quarkdoc-reader.jar;%APP_HOME%\lib\quarkdown-server.jar;%APP_HOME%\lib\ktor-server-netty-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-server-websockets-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-server-core-jvm-3.1.2.jar;%APP_HOME%\lib\kotlin-reflect-2.1.20.jar;%APP_HOME%\lib\clikt-mordant-jvm.jar;%APP_HOME%\lib\kotlin-csv-jvm-1.10.0.jar;%APP_HOME%\lib\ktor-client-cio-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-client-core-jvm-3.1.2.jar;%APP_HOME%\lib\kotlinx-coroutines-slf4j-1.10.1.jar;%APP_HOME%\lib\ktor-websocket-serialization-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-serialization-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-websockets-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-http-cio-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-network-tls-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-http-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-events-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-sse-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-network-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-utils-jvm-3.1.2.jar;%APP_HOME%\lib\ktor-io-jvm-3.1.2.jar;%APP_HOME%\lib\kotlinx-coroutines-core-jvm-1.10.1.jar;%APP_HOME%\lib\better-parse-jvm-0.4.4.jar;%APP_HOME%\lib\mordant-omnibus-jvm.jar;%APP_HOME%\lib\mordant-jvm-jna-jvm.jar;%APP_HOME%\lib\mordant-jvm-ffm-jvm.jar;%APP_HOME%\lib\mordant-jvm-graal-ffi-jvm.jar;%APP_HOME%\lib\mordant-jvm.jar;%APP_HOME%\lib\colormath-jvm.jar;%APP_HOME%\lib\kotlin-stdlib-jdk8-1.8.0.jar;%APP_HOME%\lib\clikt-jvm.jar;%APP_HOME%\lib\kotlin-stdlib-jdk7-1.8.0.jar;%APP_HOME%\lib\kotlinx-serialization-core-jvm-1.8.0.jar;%APP_HOME%\lib\kotlinx-io-core-jvm-0.6.0.jar;%APP_HOME%\lib\kotlinx-io-bytestring-jvm-0.6.0.jar;%APP_HOME%\lib\kotlin-stdlib-2.1.20.jar;%APP_HOME%\lib\annotations-23.0.0.jar;%APP_HOME%\lib\directory-watcher-0.19.0.jar;%APP_HOME%\lib\log4j-core-2.24.3.jar;%APP_HOME%\lib\commons-text-1.13.0.jar;%APP_HOME%\lib\romannumerals4j-0.0.1.jar;%APP_HOME%\lib\jbibtex-1.0.20.jar;%APP_HOME%\lib\jsoup-1.20.1.jar;%APP_HOME%\lib\slf4j-simple-2.0.17.jar;%APP_HOME%\lib\jna-5.16.0.jar;%APP_HOME%\lib\slf4j-api-2.0.17.jar;%APP_HOME%\lib\log4j-api-2.24.3.jar;%APP_HOME%\lib\commons-lang3-3.17.0.jar;%APP_HOME%\lib\netty-codec-http2-4.1.118.Final.jar;%APP_HOME%\lib\alpn-api-1.1.3.v20160715.jar;%APP_HOME%\lib\netty-transport-native-kqueue-4.1.118.Final.jar;%APP_HOME%\lib\netty-transport-native-epoll-4.1.118.Final.jar;%APP_HOME%\lib\netty-codec-http-4.1.118.Final.jar;%APP_HOME%\lib\netty-handler-4.1.118.Final.jar;%APP_HOME%\lib\netty-codec-4.1.118.Final.jar;%APP_HOME%\lib\netty-transport-classes-kqueue-4.1.118.Final.jar;%APP_HOME%\lib\netty-transport-classes-epoll-4.1.118.Final.jar;%APP_HOME%\lib\netty-transport-native-unix-common-4.1.118.Final.jar;%APP_HOME%\lib\netty-transport-4.1.118.Final.jar;%APP_HOME%\lib\netty-buffer-4.1.118.Final.jar;%APP_HOME%\lib\netty-resolver-4.1.118.Final.jar;%APP_HOME%\lib\netty-common-4.1.118.Final.jar;%APP_HOME%\lib\config-1.4.3.jar;%APP_HOME%\lib\jansi-2.4.1.jar


@rem Execute quarkdown
"%JAVA_EXE%" %DEFAULT_JVM_OPTS% %JAVA_OPTS% %QUARKDOWN_OPTS%  -classpath "%CLASSPATH%" com.quarkdown.cli.QuarkdownCliKt %*

:end
@rem End local scope for the variables with windows NT shell
if %ERRORLEVEL% equ 0 goto mainEnd

:fail
rem Set variable QUARKDOWN_EXIT_CONSOLE if you need the _script_ return code instead of
rem the _cmd.exe /c_ return code!
set EXIT_CODE=%ERRORLEVEL%
if %EXIT_CODE% equ 0 set EXIT_CODE=1
if not ""=="%QUARKDOWN_EXIT_CONSOLE%" exit %EXIT_CODE%
exit /b %EXIT_CODE%

:mainEnd
if "%OS%"=="Windows_NT" endlocal

:omega
