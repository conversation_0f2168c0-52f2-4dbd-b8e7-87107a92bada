import os
import json
import asyncio
import configparser
from datetime import datetime, timedelta
from jira import JIRA
import requests
import re
from fastmcp import FastMCP
from urllib.parse import urlencode
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 读取配置文件
def load_config():
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config.ini')
    # 使用 UTF-8 编码读取配置文件
    with open(config_path, 'r', encoding='utf-8') as f:
        config.read_file(f)
    return config

# JIRA认证
def get_jira_client():
    config = load_config()
    jira_config = config['jira']
    
    # 获取CAS登录cookie
    session = requests.Session()
    login_url = jira_config['cas_login_url']
    get = session.get(login_url)
    login_url = get.url
    text = get.text

    lt = re.findall('name="lt" value="(.*?)" />', text)[0]

    # 登录数据
    data = {
        "username": jira_config['username'],
        "password": jira_config['password'],
        "lt": lt,
        "execution": 'e1s1',
        "_eventId": "submit",
        "submit": ""
    }
    res = session.post(login_url, data=data)
    resd = requests.utils.dict_from_cookiejar(session.cookies)

    cookie_str = f"UM_distinctid=16bd4960dbc127-0fc98489b208bb-e343166-1fa400-16bd4960dc0538;JSESSIONID={resd['JSESSIONID']}; atlassian.xsrf.token={resd['atlassian.xsrf.token']}"
    
    return JIRA(
        server=jira_config['server'],
        options={"headers": {"Cookie": cookie_str}},
        #verify=jira_config.getboolean('verify_ssl')
    )

# 创建FastMCP应用
app = FastMCP("JIRA缺陷查询",log_level="ERROR")

# 获取缺陷列表
@app.tool(name="get_bugs",description="获取缺陷信息：1.支持批量输入缺陷编号(英文逗号分割)；2.支持输入jira项目名称、类型、解决结果、报告人、过去几天内创建、缺陷状态、发现版本多条件组合查询（若未传，则用默认值查询）")
async def get_bugs(project=None, issuetype=None, resolution=None, reporter=None, created_days=None, status=None, issuekey=None, found_version=None):
    try:
        jira = get_jira_client()
        config = load_config()
        
        # 如果提供了issuekey，直接使用issuekey查询
        if issuekey:
            jql = f"issuekey in ({issuekey})"
            issues = jira.search_issues(jql, maxResults=100)
        else:
            # 获取查询参数，如果用户未提供则使用默认值
            project = project or config['query']['default_project']
            issuetype = issuetype or config['query']['default_issuetype']
            resolution = resolution or config['query']['default_resolution']
            reporter = reporter or config['query']['default_reporter']
            created_days = int(created_days) if created_days else int(config['query']['default_created_days'])
            status = status or config['query']['default_status']
            
            logger.info(f"Status parameter value: {status}")
            logger.info(f"Status parameter type: {type(status)}")
            
            # 计算时间范围
            start_date = (datetime.now() - timedelta(days=created_days)).strftime('%Y-%m-%d')
            end_date = datetime.now().strftime('%Y-%m-%d')
            
            # 构建JQL查询条件
            jql_parts = []
            
            # 项目条件
            if project:
                jql_parts.append(f"project in ({project})")
                
            # 问题类型条件
            if issuetype:
                jql_parts.append(f"issuetype = {issuetype}")
                
            # 解决状态条件
            if resolution:
                resolutions = [f'"{r.strip()}"' for r in resolution.split(',')]
                jql_parts.append(f"resolution in ({', '.join(resolutions)})")
                
            # 报告人条件
            if reporter:
                if reporter.startswith('!'):
                    jql_parts.append(f"reporter not in ({reporter[1:]})")
                else:
                    jql_parts.append(f"reporter in ({reporter})")
                    
            # 状态条件
            if status:
                logger.info(f"Processing status condition: {status}")
                if status.startswith('!'):
                    jql_parts.append(f"status not in ({status[1:]})")
                    logger.info(f"Added status not in condition: {status[1:]}")
                else:
                    jql_parts.append(f"status in ({status})")
                    logger.info(f"Added status in condition: {status}")
            
            # 发现版本条件
            if found_version:
                logger.info(f"Processing found version condition: {found_version}")
                versions = [f'"{v.strip()}"' for v in found_version.split(',')]
                #jql_parts.append(f"found_version in ({', '.join(versions)})")
                jql_parts.append(f"发现版本 in ({', '.join(versions)})")
                logger.info(f"Added found version condition: {', '.join(versions)}")
                    
            # 时间范围条件
            jql_parts.append(f"created >= {start_date}")
            jql_parts.append(f"created <= {end_date}")
            
            # 组合所有条件
            jql = " AND ".join(jql_parts) + " ORDER BY priority DESC, updated DESC"
            logger.info(f"Final JQL query: {jql}")
            
            # 查询缺陷
            issues = jira.search_issues(jql, maxResults=100)
        
        # 处理缺陷数据
        bugs = []
        for issue in issues:
            bug = {
                'key': issue.key,
                'priority': issue.fields.priority.name,
                'reporter': issue.fields.reporter.displayName,
                'status': issue.fields.status.name,
                'assignee': issue.fields.assignee.displayName if issue.fields.assignee else "未分配",
                'created': issue.fields.created[:10],
                'updated': issue.fields.updated[:10],
                'summary': issue.fields.summary,
                'description': issue.fields.description
            }
            bugs.append(bug)
        
        # 计算缺陷个数
        bug_num = len(bugs)
        logger.info(f"查询到的缺陷个数: {bug_num}")
        
        return {
            "data": bugs,
            "bug_num": bug_num
        }
    
    except Exception as e:
        return {"error": str(e)}, 500

# SSE实时获取缺陷更新
@app.tool(name="stream_bugs",description="实时获取缺陷更新")
async def stream_bugs():
    try:
        jira = get_jira_client()
        config = load_config()
        
        async def generate():
            last_check = datetime.now()
            while True:
                # 查询最近更新的缺陷
                jql = f"{config['query']['default_jql']} AND updated >= {last_check.strftime('%Y-%m-%d %H:%M:%S')} ORDER BY updated DESC"
                issues = jira.search_issues(jql, maxResults=100)
                
                for issue in issues:
                    bug = {
                        'key': issue.key,
                        'priority': issue.fields.priority.name,
                        'reporter': issue.fields.reporter.displayName,
                        'status': issue.fields.status.name,
                        'assignee': issue.fields.assignee.displayName if issue.fields.assignee else "未分配",
                        'created': issue.fields.created[:10],
                        'updated': issue.fields.updated[:10],
                        'summary': issue.fields.summary
                    }
                    yield f"data: {json.dumps(bug)}\n\n"
                
                last_check = datetime.now()
                await asyncio.sleep(60)  # 每分钟检查一次更新
        
        return generate()
    
    except Exception as e:
        return {"error": str(e)}, 500

# 获取缺陷统计信息
@app.tool(name="get_bug_stats",description="获取缺陷统计信息：支持输入jira项目名称、类型、解决结果、报告人、过去几天内创建、缺陷状态、发现版本多条件组合查询（若未传，则用默认值查询）")
async def get_bug_stats(project=None, issuetype=None, resolution=None, reporter=None, created_days=None, status=None, found_version=None):
    try:
        jira = get_jira_client()
        config = load_config()
        
        # 获取查询参数，如果用户未提供则使用默认值
        project = project or config['query']['default_project']
        issuetype = issuetype or config['query']['default_issuetype']
        resolution = resolution or config['query']['default_resolution']
        reporter = reporter or config['query']['default_reporter']
        created_days = int(created_days) if created_days else int(config['query']['default_created_days'])
        status = status or config['query']['default_status']
        
        logger.info(f"Status parameter value: {status}")
        logger.info(f"Status parameter type: {type(status)}")
        
        # 计算时间范围
        start_date = (datetime.now() - timedelta(days=created_days)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        # 构建JQL查询条件
        jql_parts = []
        
        # 项目条件
        if project:
            jql_parts.append(f"project in ({project})")
            
        # 问题类型条件
        if issuetype:
            jql_parts.append(f"issuetype = {issuetype}")
            
        # 解决状态条件
        if resolution:
            resolutions = [f'"{r.strip()}"' for r in resolution.split(',')]
            jql_parts.append(f"resolution in ({', '.join(resolutions)})")
            
        # 报告人条件
        if reporter:
            if reporter.startswith('!'):
                jql_parts.append(f"reporter not in ({reporter[1:]})")
            else:
                jql_parts.append(f"reporter in ({reporter})")
                
        # 状态条件
        if status:
            logger.info(f"Processing status condition: {status}")
            if status.startswith('!'):
                jql_parts.append(f"status not in ({status[1:]})")
                logger.info(f"Added status not in condition: {status[1:]}")
            else:
                jql_parts.append(f"status in ({status})")
                logger.info(f"Added status in condition: {status}")
        
        # 发现版本条件
        if found_version:
            logger.info(f"Processing found version condition: {found_version}")
            versions = [f'"{v.strip()}"' for v in found_version.split(',')]
            jql_parts.append(f"发现版本 in ({', '.join(versions)})")
            logger.info(f"Added found version condition: {', '.join(versions)}")
                
        # 时间范围条件
        jql_parts.append(f"created >= {start_date}")
        jql_parts.append(f"created <= {end_date}")
        
        # 组合所有条件
        jql = " AND ".join(jql_parts)
        logger.info(f"Final JQL query: {jql}")
        
        # 查询缺陷
        issues = jira.search_issues(jql, maxResults=1000)
        
        # 统计信息
        stats = {
            'total': len(issues),
            'by_priority': {},
            'by_status': {},
            'by_assignee': {},
            'by_found_version': {}
        }
        
        for issue in issues:
            # 按优先级统计
            priority = issue.fields.priority.name
            stats['by_priority'][priority] = stats['by_priority'].get(priority, 0) + 1
            
            # 按状态统计
            status = issue.fields.status.name
            stats['by_status'][status] = stats['by_status'].get(status, 0) + 1
            
            # 按经办人统计
            assignee = issue.fields.assignee.displayName if issue.fields.assignee else "未分配"
            stats['by_assignee'][assignee] = stats['by_assignee'].get(assignee, 0) + 1
            
            # 按发现版本统计
            if hasattr(issue.fields, 'found_version'):
                found_version = issue.fields.found_version
                if found_version:
                    stats['by_found_version'][found_version] = stats['by_found_version'].get(found_version, 0) + 1
        
        logger.info(f"统计结果: {stats}")
        return {"data": stats}
    
    except Exception as e:
        logger.error(f"统计缺陷信息时发生错误: {str(e)}")
        return {"error": str(e)}, 500

def main():
    app.run(transport='sse')

if __name__ == "__main__":
    main()
    #print(get_bug_stats())