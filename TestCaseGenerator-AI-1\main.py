import os
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                           QPushButton, QLabel, QTextEdit, QFileDialog, QComboBox,
                           QTabWidget, QSplitter, QMessageBox, QProgressBar, QGroupBox,
                           QFormLayout, QSpinBox, QDoubleSpinBox, QLineEdit, QStatusBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QTextDocument, QTextOption
import docx
import markdown
from config import config
from test_case_generator import TestCaseGenerator
import json
import datetime
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, <PERSON>ternFill, Alignment, Border, Side
import re

class GenerationWorker(QThread):
    progress_signal = pyqtSignal(int, str)
    finished_signal = pyqtSignal(str)
    error_signal = pyqtSignal(str)

    def __init__(self, generator, document_text):
        super().__init__()
        self.generator = generator
        self.document_text = document_text

    def run(self):
        try:
            # 文档加载成功
            self.progress_signal.emit(30, "文档加载成功，正在处理...")
            
            # 调用大模型接口前
            self.progress_signal.emit(50, "正在调用大模型生成测试用例...")
            
            # 生成测试用例
            result = self.generator.generate(self.document_text)
            
            if result is not None:
                # 生成完成
                self.progress_signal.emit(100, "测试用例生成完成")
                self.finished_signal.emit(result)
            else:
                self.error_signal.emit("生成已取消")
        except Exception as e:
            self.error_signal.emit(str(e))

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 初始化组件
        self.generator = TestCaseGenerator()
        self.generator.set_update_callback(self.update_progress)
        self.init_ui()
        self.load_config()
        self.worker = None

    def init_ui(self):
        """Initialize the user interface"""
        # 设置窗口属性
        self.setWindowTitle("测试用例生成工具")
        width = config.get("ui", "window_size")["width"]
        height = config.get("ui", "window_size")["height"]
        self.setGeometry(100, 100, width, height)
        self.setMinimumSize(800, 600)  # 设置最小窗口大小

        # Create central widget and main layout
        central_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)  # 设置布局间距
        main_layout.setContentsMargins(10, 10, 10, 10)  # 设置边距
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Horizontal)
        splitter_ratio = config.get("ui", "splitter_ratio")
        
        # Left panel (input)
        left_panel = self.create_left_panel()
        
        # Right panel (output)
        right_panel = self.create_right_panel()
        
        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes(splitter_ratio)
        
        main_layout.addWidget(splitter)
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)
        
        # Add status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 添加版本和签名标签
        version_label = QLabel("Version：1.1（20250408）[资管测试 禹猛猛]")
        version_label.setStyleSheet("color: gray;")  # 设置字体颜色为灰色
        self.status_bar.addPermanentWidget(version_label)  # 使用addPermanentWidget将标签添加到右侧
        
        self.status_bar.showMessage("就绪")

    def create_left_panel(self):
        """Create the left panel with input controls"""
        panel = QWidget()
        layout = QVBoxLayout()

        # Document section
        doc_group = QGroupBox("文档")
        doc_layout = QVBoxLayout()
        
        doc_button_layout = QHBoxLayout()
        self.upload_button = QPushButton("打开文档...")
        self.upload_button.clicked.connect(self.open_document)
        doc_button_layout.addWidget(self.upload_button)
        
        self.file_label = QLabel("未选择文件")
        doc_button_layout.addWidget(self.file_label, 1)
        
        self.clear_button = QPushButton("清空")
        self.clear_button.clicked.connect(self.clear_document)
        doc_button_layout.addWidget(self.clear_button)
        
        doc_layout.addLayout(doc_button_layout)
        
        # 文档内容编辑区
        self.document_preview = QTextEdit()
        self.document_preview.setPlaceholderText("在此处输入或粘贴需求文档内容...")
        self.document_preview.textChanged.connect(self.on_document_changed)
        doc_layout.addWidget(self.document_preview)
        
        doc_group.setLayout(doc_layout)
        layout.addWidget(doc_group)

        # 提示词部分
        prompt_group = QGroupBox("提示词设置")
        prompt_layout = QVBoxLayout()
        
        # 系统提示词
        system_label = QLabel("系统提示词:")
        self.system_prompt = QTextEdit()
        self.system_prompt.setPlaceholderText("输入系统提示词，告诉AI它的角色和任务...")
        self.system_prompt.setMaximumHeight(80)
        self.system_prompt.setText("你是一个专业的测试工程师，擅长编写测试用例。")
        self.system_prompt.setAcceptRichText(False)  # 禁用富文本
        self.system_prompt.setLineWrapMode(QTextEdit.WidgetWidth)
        self.system_prompt.setWordWrapMode(QTextOption.WrapAtWordBoundaryOrAnywhere)
        prompt_layout.addWidget(system_label)
        prompt_layout.addWidget(self.system_prompt)
        
        # 用户提示词
        user_label = QLabel("用户提示词:")
        self.user_prompt = QTextEdit()
        self.user_prompt.setPlaceholderText("输入用户提示词，指定测试用例的格式和要求...")
        self.user_prompt.setMaximumHeight(80)
        self.user_prompt.setText("请根据以下需求文档生成测试用例，使用Markdown格式：")
        self.user_prompt.setAcceptRichText(False)  # 禁用富文本
        self.user_prompt.setLineWrapMode(QTextEdit.WidgetWidth)
        self.user_prompt.setWordWrapMode(QTextOption.WrapAtWordBoundaryOrAnywhere)
        prompt_layout.addWidget(user_label)
        prompt_layout.addWidget(self.user_prompt)
        
        # 提示词模板选择
        template_layout = QHBoxLayout()
        template_label = QLabel("快速模板:")
        self.template_combo = QComboBox()
        self.template_combo.addItems([
            "标准格式 (ID/描述/步骤/预期结果)",
            "简化格式 (场景/步骤/结果)",
            "详细格式 (带优先级和前置条件)",
            "自定义格式"
        ])
        self.template_combo.currentIndexChanged.connect(self.on_template_changed)
        template_layout.addWidget(template_label)
        template_layout.addWidget(self.template_combo)
        prompt_layout.addLayout(template_layout)
        
        prompt_group.setLayout(prompt_layout)
        layout.addWidget(prompt_group)

        # API Configuration section
        api_group = QGroupBox("API 配置")
        api_layout = QFormLayout()
        
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.Password)
        api_layout.addRow("API 密钥:", self.api_key_input)

        self.api_url_input = QLineEdit()
        self.api_url_input.setText(config.get("api", "url"))
        api_layout.addRow("API URL:", self.api_url_input)
        
        # 模型选择部分使用水平布局
        model_layout = QHBoxLayout()
        
        self.model_selection = QComboBox()
        self.model_selection.setFixedWidth(150)
        available_models = config.get("models", "available")
        for model in available_models:
            self.model_selection.addItem(model["name"], model["id"])
            self.model_selection.setItemData(
                self.model_selection.count() - 1,
                model["description"],
                Qt.ToolTipRole
            )
        
        self.model_description = QLabel()
        self.model_description.setWordWrap(True)
        self.model_description.setStyleSheet("color: gray; font-size: 10pt;")
        
        model_layout.addWidget(self.model_selection)
        model_layout.addWidget(self.model_description)
        model_layout.addStretch()
        
        api_layout.addRow("模型:", model_layout)
        
        self.model_selection.currentIndexChanged.connect(self._update_model_description)
        
        self.temperature_input = QDoubleSpinBox()
        self.temperature_input.setRange(0.0, 1.0)
        self.temperature_input.setSingleStep(0.1)
        self.temperature_input.setValue(0.7)
        api_layout.addRow("温度:", self.temperature_input)
        
        api_group.setLayout(api_layout)
        layout.addWidget(api_group)
        
        # 初始化显示当前选择的模型描述
        self._update_model_description()

        # Generation controls
        control_layout = QHBoxLayout()
        
        self.generate_button = QPushButton("生成测试用例")
        self.generate_button.clicked.connect(self.generate_test_cases)
        self.generate_button.setEnabled(False)
        control_layout.addWidget(self.generate_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.cancel_generation)
        self.cancel_button.setEnabled(False)
        control_layout.addWidget(self.cancel_button)
        
        layout.addLayout(control_layout)

        # Progress section
        progress_group = QGroupBox("进度")
        progress_layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")  # 只显示百分比
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("就绪")
        progress_layout.addWidget(self.progress_label)
        
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)

        panel.setLayout(layout)
        return panel

    def create_right_panel(self):
        """Create the right panel with output display"""
        panel = QWidget()
        layout = QVBoxLayout()

        # Output tabs
        self.output_tabs = QTabWidget()
        
        # Preview tab
        self.preview_tab = QWidget()
        preview_layout = QVBoxLayout()
        self.test_case_preview = QTextEdit()
        self.test_case_preview.setReadOnly(True)
        self.test_case_preview.setPlaceholderText("生成的测试用例将显示在这里...")
        # 优化文本编辑器性能
        self.test_case_preview.setLineWrapMode(QTextEdit.WidgetWidth)
        self.test_case_preview.setWordWrapMode(QTextOption.WrapAtWordBoundaryOrAnywhere)
        self.test_case_preview.document().setDocumentMargin(10)
        self.test_case_preview.setAcceptRichText(False)  # 只接受纯文本以提高性能
        preview_layout.addWidget(self.test_case_preview)
        self.preview_tab.setLayout(preview_layout)
        
        # Rendered tab
        self.rendered_tab = QWidget()
        rendered_layout = QVBoxLayout()
        self.rendered_preview = QTextEdit()
        self.rendered_preview.setReadOnly(True)
        # 优化文本编辑器性能
        self.rendered_preview.setLineWrapMode(QTextEdit.WidgetWidth)
        self.rendered_preview.setWordWrapMode(QTextOption.WrapAtWordBoundaryOrAnywhere)
        self.rendered_preview.document().setDocumentMargin(10)
        self.rendered_preview.setAcceptRichText(True)  # 渲染预览需要支持富文本
        rendered_layout.addWidget(self.rendered_preview)
        self.rendered_tab.setLayout(rendered_layout)
        
        self.output_tabs.addTab(self.preview_tab, "原始文本")
        self.output_tabs.addTab(self.rendered_tab, "渲染预览")
        
        layout.addWidget(self.output_tabs)

        # Save options
        save_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存测试用例")
        self.save_button.setEnabled(False)
        self.save_button.clicked.connect(self.save_test_cases)
        save_layout.addWidget(self.save_button)
        
        self.format_combo = QComboBox()
        self.format_combo.addItems([
            "Markdown 格式 (.md)",
            "Word 文档 (.docx)",
            "PDF 文档 (.pdf)",
            "Excel 文档 (.xlsx)"
        ])
        save_layout.addWidget(self.format_combo)
        
        layout.addLayout(save_layout)
        
        panel.setLayout(layout)
        return panel

    def load_config(self):
        """Load configuration values into UI"""
        self.api_key_input.setText(config.get("api", "key"))
        self.api_url_input.setText(config.get("api", "url"))
        model_index = self.model_selection.findData(config.get("api", "model"))
        if model_index >= 0:
            self.model_selection.setCurrentIndex(model_index)
        self.temperature_input.setValue(config.get("api", "temperature"))

    def save_config(self):
        """Save current UI values to configuration"""
        config.set("api", "key", self.api_key_input.text())
        config.set("api", "url", self.api_url_input.text())
        config.set("api", "model", self.model_selection.currentData())  # 保存模型ID而不是显示名称
        config.set("api", "temperature", self.temperature_input.value())

    def clear_document(self):
        """清空文档内容"""
        self.document_preview.clear()
        self.file_label.setText("未选择文件")

    def on_document_changed(self):
        """当文档内容改变时更新UI状态"""
        # 使用 blockSignals 防止递归更新
        self.generate_button.blockSignals(True)
        has_content = bool(self.document_preview.toPlainText().strip())
        self.generate_button.setEnabled(has_content)
        self.generate_button.blockSignals(False)
        
        if has_content:
            self.status_bar.showMessage("就绪")
        else:
            self.status_bar.showMessage("请输入或加载需求文档")

    def open_document(self):
        """Open and load a document file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开需求文档", "", "Word 文档 (*.docx)"
        )
        
        if file_path:
            try:
                doc = docx.Document(file_path)
                text = "\n".join(para.text for para in doc.paragraphs)
                self.document_preview.setText(text)
                self.file_label.setText(os.path.basename(file_path))
                self.status_bar.showMessage("文档加载成功")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"加载文档失败: {str(e)}")

    def on_template_changed(self, index):
        """处理模板选择变化"""
        templates = {
            0: {  # 标准格式
                "system": "你是一个专业的测试工程师，擅长编写测试用例。请使用标准格式编写测试用例。",
                "user": "请根据以下需求文档生成测试用例（请先分析需求文档结构，充分的梳理测试场景），包含以下要素：\n1. 测试用例ID\n2. 测试描述\n3. 测试步骤\n4. 预期结果"
            },
            1: {  # 简化格式
                "system": "你是一个专业的测试工程师，擅长编写简洁的测试用例。",
                "user": "请根据以下需求文档生成测试用例（请先分析需求文档结构，充分的梳理测试场景），使用简化格式：\n1. 测试场景\n2. 操作步骤\n3. 预期结果"
            },
            2: {  # 详细格式
                "system": "你是一个专业的金融软件测试工程师，熟悉金融业务知识和规则、金融软件的架构和业务流程，同时擅长编写详细的测试用例，可以利用边界值、等价类划分、正交试验、场景法、判定表等方法生成测试用例",
                "user": "请根据以下需求文档生成详细的测试用例（请先分析需求文档结构，充分的梳理测试场景），包含以下要素：\n1. 测试用例ID\n2. 优先级\n3. 测试描述\n4. 前置条件\n5. 测试步骤\n6. 预期结果\n7. 测试类型"
            }
        }
        
        if index < 3:  # 非自定义模板
            template = templates[index]
            self.system_prompt.setText(template["system"])
            self.user_prompt.setText(template["user"])

    def generate_test_cases(self):
        """Start test case generation"""
        document_text = self.document_preview.toPlainText().strip()
        if not document_text:
            QMessageBox.warning(self, "警告", "请先输入或加载需求文档内容。")
            return

        # Update API configuration
        self.save_config()
        
        # Update UI state
        self._disable_ui_during_generation()
        
        # 准备生成参数
        self.generator.set_prompts(
            system_prompt=self.system_prompt.toPlainText().strip(),
            user_prompt=self.user_prompt.toPlainText().strip()
        )
        
        # Start generation in background thread
        self.worker = GenerationWorker(self.generator, document_text)
        self.worker.progress_signal.connect(self.update_progress)
        self.worker.finished_signal.connect(self.generation_completed)
        self.worker.error_signal.connect(self.generation_error)
        self.worker.start()

    def _disable_ui_during_generation(self):
        """禁用生成过程中的UI元素"""
        self.generate_button.setEnabled(False)
        self.cancel_button.setEnabled(True)
        self.upload_button.setEnabled(False)
        self.clear_button.setEnabled(False)
        self.document_preview.setReadOnly(True)
        self.save_button.setEnabled(False)
        self.api_key_input.setEnabled(False)
        self.api_url_input.setEnabled(False)
        self.model_selection.setEnabled(False)
        self.temperature_input.setEnabled(False)
        self.progress_bar.setValue(0)

    def _enable_ui_after_generation(self):
        """恢复生成完成后的UI元素状态"""
        self.generate_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        self.upload_button.setEnabled(True)
        self.clear_button.setEnabled(True)
        self.document_preview.setReadOnly(False)
        self.api_key_input.setEnabled(True)
        self.api_url_input.setEnabled(True)
        self.model_selection.setEnabled(True)
        self.temperature_input.setEnabled(True)

    def cancel_generation(self):
        """Cancel ongoing generation"""
        if self.worker and self.worker.isRunning():
            self.generator.cancel()
            self.cancel_button.setEnabled(False)
            self.status_bar.showMessage("正在取消生成...")
            # 启用部分UI元素
            self.api_key_input.setEnabled(True)
            self.api_url_input.setEnabled(True)
            self.model_selection.setEnabled(True)
            self.temperature_input.setEnabled(True)

    def update_progress(self, progress: int, status: str):
        """Update progress bar and status"""
        try:
            # 确保进度值在有效范围内
            progress = max(0, min(100, progress))
            
            # 如果是关键节点（30%、50%、100%），立即更新
            is_key_progress = progress in [30, 50, 100]
            
            # 如果不是关键节点且进度值变化不大，不更新UI
            if not is_key_progress and hasattr(self, '_last_progress') and abs(self._last_progress - progress) < 1:
                return
            
            self._last_progress = progress
            
            # 使用QTimer进行节流更新
            if not hasattr(self, '_update_timer'):
                self._update_timer = QTimer()
                self._update_timer.setSingleShot(True)
                self._update_timer.timeout.connect(self._do_update_progress)
            
            # 存储最新的进度值和状态
            self._pending_progress = progress
            self._pending_status = status
            
            # 如果是关键节点或定时器未运行，立即更新
            if is_key_progress or not self._update_timer.isActive():
                self._do_update_progress()
            
        except Exception as e:
            print(f"更新进度时发生错误: {str(e)}")
    
    def _do_update_progress(self):
        """实际执行进度更新的方法"""
        try:
            if not hasattr(self, '_pending_progress'):
                return
                
            # 批量更新开始
            self.setUpdatesEnabled(False)
            
            # 更新进度条
            self.progress_bar.setValue(self._pending_progress)
            
            # 更新状态文本（只在状态栏显示）
            if hasattr(self, '_pending_status'):
                self.status_bar.showMessage(self._pending_status)
            
            # 批量更新结束
            self.setUpdatesEnabled(True)
            
            # 强制重绘
            self.progress_bar.update()
            
        except Exception as e:
            print(f"执行进度更新时发生错误: {str(e)}")

    def generation_error(self, error_message: str):
        """处理生成过程中的错误"""
        self._enable_ui_after_generation()
        self.status_bar.showMessage(f"生成失败: {error_message}")
        QMessageBox.warning(self, "错误", f"生成测试用例失败：{error_message}")

    def generation_completed(self, result: str):
        """Handle completed test case generation"""
        try:
            # Update UI state
            self._enable_ui_after_generation()
            
            if result:
                # 创建一个临时的QTextDocument来处理大量文本
                from PyQt5.QtGui import QTextDocument
                
                # 批量更新开始，禁用所有更新
                self.setUpdatesEnabled(False)
                self.test_case_preview.setUpdatesEnabled(False)
                self.rendered_preview.setUpdatesEnabled(False)
                
                try:
                    # 更新原始文本预览
                    temp_doc = QTextDocument()
                    temp_doc.setPlainText(result)
                    self.test_case_preview.setDocument(temp_doc.clone())
                    
                    # 更新渲染预览
                    html_doc = QTextDocument()
                    html_content = markdown.markdown(result)
                    html_doc.setHtml(html_content)
                    self.rendered_preview.setDocument(html_doc.clone())
                    
                    # 启用保存按钮
                    self.save_button.setEnabled(True)
                    
                    self.status_bar.showMessage("测试用例生成成功")
                finally:
                    # 确保重新启用更新
                    self.test_case_preview.setUpdatesEnabled(True)
                    self.rendered_preview.setUpdatesEnabled(True)
                    self.setUpdatesEnabled(True)
                    
                    # 强制重绘
                    self.test_case_preview.update()
                    self.rendered_preview.update()
            else:
                self.status_bar.showMessage("测试用例生成已取消")
        except Exception as e:
            self.status_bar.showMessage(f"更新界面时发生错误: {str(e)}")
            print(f"渲染错误: {str(e)}")  # 添加错误日志

    def save_test_cases(self):
        """Save the generated test cases"""
        if not self.test_case_preview.toPlainText():
            QMessageBox.warning(self, "警告", "没有可保存的测试用例。")
            return

        format_index = self.format_combo.currentIndex()
        
        if format_index == 0:  # Markdown
            self.save_as_markdown()
        elif format_index == 1:  # Word
            self.save_as_word()
        elif format_index == 2:  # PDF
            self.save_as_pdf()
        elif format_index == 3:  # Excel
            self.save_as_excel()

    def save_as_markdown(self):
        """Save test cases as Markdown"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Test Cases", "", "Markdown Files (*.md)"
        )
        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(self.test_case_preview.toPlainText())
                self.status_bar.showMessage(f"Test cases saved to {file_path}")
            except Exception as e:
                QMessageBox.warning(self, "Error", f"Failed to save file: {str(e)}")

    def save_as_word(self):
        """Save test cases as Word document"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存测试用例", "", "Word 文档 (*.docx)"
        )
        if file_path:
            try:
                doc = docx.Document()
                doc.add_heading("测试用例", 0)
                
                # Add content paragraphs
                for line in self.test_case_preview.toPlainText().split("\n"):
                    if line.startswith("#"):
                        # Handle headers
                        level = line.count("#")
                        text = line.strip("#").strip()
                        doc.add_heading(text, level)
                    else:
                        # Regular paragraph
                        doc.add_paragraph(line)
                
                doc.save(file_path)
                self.status_bar.showMessage(f"测试用例已保存到 {file_path}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存文件失败: {str(e)}")

    def save_as_pdf(self):
        """Save test cases as PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存测试用例", "", "PDF 文档 (*.pdf)"
            )
            if file_path:
                printer = QPrinter()
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                
                # 创建临时文档对象
                doc = QTextDocument()
                doc.setDefaultStyleSheet("body { font-family: Arial, sans-serif; }")
                
                # 批量更新
                self.setUpdatesEnabled(False)
                doc.setHtml(self.rendered_preview.toHtml())
                
                # 使用打印机打印文档
                painter = doc.documentLayout().paintDevice()
                if painter:
                    painter.end()
                doc.print_(printer)
                
                self.setUpdatesEnabled(True)
                
                self.status_bar.showMessage(f"测试用例已保存到 {file_path}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存 PDF 失败: {str(e)}")

    def save_as_excel(self):
        """Save test cases as Excel"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存测试用例", "", "Excel 文件 (*.xlsx)"
        )
        if file_path:
            try:
                # 打印Markdown内容以便调试
                content = self.test_case_preview.toPlainText()
                print("Markdown Content:")
                print("="*50)
                print(content)
                print("="*50)
                
                # 将Markdown格式转换为数据结构
                test_cases = []
                current_case = None
                
                # 使用正则表达式匹配测试用例的各个部分
                case_pattern = r'#\s*(.*?)\s*(?=\n#|\Z)'  # 匹配整个测试用例
                section_pattern = r'##\s*(.*?)\s*\n(.*?)(?=\n##|\Z)'  # 匹配章节
                
                # 查找所有测试用例
                case_matches = re.finditer(case_pattern, content, re.DOTALL)
                for case_idx, case_match in enumerate(case_matches, 1):
                    case_content = case_match.group(1)
                    
                    # 创建新的测试用例
                    current_case = {
                        'ID': case_idx,
                        'Title': case_content.split('\n')[0].strip(),
                        'Description': '',
                        'Preconditions': '',
                        'Steps': '',
                        'Expected Results': '',
                        'Test Type': ''
                    }
                    
                    # 查找所有章节
                    section_matches = re.finditer(section_pattern, case_content, re.DOTALL)
                    for section_match in section_matches:
                        section_title = section_match.group(1).strip().lower()
                        section_content = section_match.group(2).strip()
                        
                        # 处理章节内容
                        if section_title in ['description', '描述']:
                            current_case['Description'] = section_content
                        elif section_title in ['preconditions', '前置条件']:
                            current_case['Preconditions'] = section_content
                        elif section_title in ['steps', 'test steps', '测试步骤']:
                            # 处理步骤列表
                            steps = []
                            for line in section_content.split('\n'):
                                line = line.strip()
                                if line:
                                    # 移除列表标记和编号
                                    line = re.sub(r'^[-*]\s*', '', line)  # 移除 - 或 * 
                                    line = re.sub(r'^\d+\.\s*', '', line)  # 移除数字编号
                                    steps.append(line.strip())
                            current_case['Steps'] = '\n'.join(steps)
                        elif section_title in ['expected results', 'expected', '预期结果']:
                            current_case['Expected Results'] = section_content
                        elif section_title in ['test type', '测试类型']:
                            current_case['Test Type'] = section_content
                    
                    test_cases.append(current_case)
                
                # 创建Excel工作簿
                wb = Workbook()
                ws = wb.active
                ws.title = "测试用例"
                
                # 定义样式
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="0078D4", end_color="0078D4", fill_type="solid")
                border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                
                # 写入表头
                headers = ['ID', 'Title', 'Description', 'Preconditions', 'Steps', 'Expected Results', 'Test Type']
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, column=col, value=header)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.border = border
                    cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                
                # 写入数据
                for row_idx, case in enumerate(test_cases, 2):
                    ws.cell(row=row_idx, column=1, value=case['ID'])
                    ws.cell(row=row_idx, column=2, value=case['Title'])
                    ws.cell(row=row_idx, column=3, value=case.get('Description', ''))
                    ws.cell(row=row_idx, column=4, value=case.get('Preconditions', ''))
                    ws.cell(row=row_idx, column=5, value=case.get('Steps', ''))
                    ws.cell(row=row_idx, column=6, value=case.get('Expected Results', ''))
                    ws.cell(row=row_idx, column=7, value=case.get('Test Type', ''))
                    
                    # 应用样式
                    for col in range(1, 8):
                        cell = ws.cell(row=row_idx, column=col)
                        cell.border = border
                        cell.alignment = Alignment(wrap_text=True, vertical='top')
                
                # 调整列宽
                for column in ws.columns:
                    max_length = 0
                    column = list(column)
                    for cell in column:
                        try:
                            if cell.value:
                                max_length = max(max_length, len(str(cell.value).split('\n')[0]))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)  # 限制最大宽度为50
                    ws.column_dimensions[column[0].column_letter].width = adjusted_width
                
                # 保存文件
                wb.save(file_path)
                
                self.status_bar.showMessage(f"测试用例已保存到 {file_path}")
                
                # 保存成功后打开文件所在目录
                os.startfile(os.path.dirname(file_path))
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存文件失败: {str(e)}")

    def closeEvent(self, event):
        """Handle application close event"""
        self.save_config()
        event.accept()

    def _update_model_description(self):
        """更新模型描述"""
        # 使用 blockSignals 防止递归更新
        self.model_description.blockSignals(True)
        current_index = self.model_selection.currentIndex()
        if current_index >= 0:
            model_id = self.model_selection.currentData()
            available_models = config.get("models", "available")
            for model in available_models:
                if model["id"] == model_id:
                    self.model_description.setText(model["description"])
                    break
        self.model_description.blockSignals(False)

def main():
    # 在创建 QApplication 之前设置高 DPI 属性
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps)
    
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    # 设置应用程序样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f0f0f0;
        }
        QWidget {
            background-color: #ffffff;
        }
        QTextEdit {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 5px;
        }
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 5px 15px;
            border-radius: 4px;
            min-height: 25px;
        }
        QPushButton:hover {
            background-color: #106ebe;
        }
        QPushButton:disabled {
            background-color: #cccccc;
        }
        QComboBox {
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 5px 30px 5px 10px;
            min-height: 25px;
            background-color: white;
        }
        QComboBox:hover {
            border-color: #0078d4;
        }
        QComboBox:focus {
            border-color: #0078d4;
            border-width: 2px;
        }
        QComboBox::drop-down {
            border: none;
            width: 20px;
            background-color: transparent;
        }
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #666;
            margin-right: 8px;
        }
        QComboBox::down-arrow:hover {
            border-top-color: #0078d4;
        }
        QComboBox QAbstractItemView {
            border: 1px solid #cccccc;
            border-radius: 4px;
            background-color: white;
            selection-background-color: #e5f3ff;
            selection-color: black;
            outline: none;
        }
        QComboBox QAbstractItemView::item {
            min-height: 25px;
            padding: 5px 10px;
        }
        QComboBox QAbstractItemView::item:hover {
            background-color: #e5f3ff;
        }
        QComboBox QAbstractItemView::item:selected {
            background-color: #cce4f7;
        }
        QGroupBox {
            border: 1px solid #cccccc;
            border-radius: 4px;
            margin-top: 1em;
            background-color: white;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px;
            color: #666666;
        }
        QLabel {
            color: #333333;
        }
        QProgressBar {
            border: 1px solid #cccccc;
            border-radius: 4px;
            text-align: center;
            min-height: 20px;
        }
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 3px;
        }
    """)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main() 