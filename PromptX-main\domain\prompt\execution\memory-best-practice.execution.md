<execution domain="prompt-engineering">
  <process>
    # 记忆模式提示词开发流程
    
    ```mermaid
    flowchart TD
      A[设计记忆策略] --> B[构建记忆评估机制]
      B --> C[设计记忆存储流程]
      C --> D[规划记忆回忆策略]
      D --> E[整合验证]
      E -->|验证通过| F[完成记忆模式]
      E -->|需要调整| B
    ```
    
    ## 核心步骤详解
    
    1. **设计记忆策略**
       - 确定适合任务的记忆类型(陈述性/程序性/情景性)
       - 规划记忆的生命周期管理
       - 设计记忆的组织结构
    
    2. **构建记忆评估机制**
       - 设计`<evaluate:thought>`组件
       - 定义价值评估标准
       - 建立多维度评分机制
       - 设置评估阈值
    
    3. **设计记忆存储流程**
       - 设计`<store:execution>`组件
       - 确定存储格式和标记系统
       - 规划存储路径和方式
       - 设计存储确认反馈
    
    4. **规划记忆回忆策略**
       - 设计`<recall:thought>`组件
       - 定义记忆触发条件
       - 建立检索机制和优先级
       - 规划记忆应用方式
    
    5. **整合验证**
       - 确保三大组件协同一致
       - 检验记忆循环的完整性
       - 测试边界条件处理能力
  </process>
  
  <guideline>
    ### 记忆类型选择指南
    
    - **陈述性记忆(declarative)**最适合存储：
      - 用户偏好和设置
      - 事实性知识和信息
      - 具有明确定义的数据
    
    - **程序性记忆(procedural)**最适合存储：
      - 操作步骤和方法
      - 解决方案模板
      - 用户行为模式
    
    - **情景记忆(episodic)**最适合存储：
      - 完整交互历史
      - 特定场景经历
      - 带时间序列的事件
    
    ### 评估组件设计建议
    
    - 使用多维度评分系统，包含：
      ```mermaid
      mindmap
        root((记忆价值评估))
          信息重要性
            关键事实或概念
            影响理解或决策
          信息新颖性
            首次出现
            补充现有知识
          用户相关性
            与用户需求匹配
            与历史交互关联
          信息可信度
            来源可靠性
            内容准确性
      ```
    
    - 强制记忆触发词应明确定义
    - 评估标准应保持一致性
    
    ### 存储组件设计建议
    
    - 存储格式应该结构化且一致
    - 使用标签系统增强检索能力
    - 存储反馈应简洁不干扰主交互
    
    ### 回忆组件设计建议
    
    - 回忆触发应基于语义相关性
    - 检索策略应结合精确匹配和模糊匹配
    - 回忆应用应自然融入回答
    - 记忆冲突有明确的解决策略
    
    ### 平衡内联内容与资源引用
    
    - 核心知识适合直接内联
    - 详细或变化频繁的内容适合资源引用
    - 考虑资源加载成本，规划加载策略
  </guideline>
  
  <rule>
    1. **角色初始化必须加载记忆** - 角色初始化时必须自动加载相关记忆文件
    2. **评估-存储-回忆完整性** - 记忆模式必须包含完整的评估-存储-回忆循环
    3. **实际存储强制执行** - 记忆存储必须通过实际工具调用执行，不得仅在对话中声明
    4. **存储结果验证** - 必须验证存储操作是否成功，并有失败处理机制
    5. **存储格式一致性** - 所有存储的记忆必须遵循统一的格式和标签系统
    6. **回忆主动性** - 系统必须能在相关场景下主动检索和应用记忆，无需用户明确请求
    7. **存储原子性** - 记忆存储操作必须保持原子性，避免部分成功导致的不一致
    8. **资源必须注册** - 创建新的memory资源后必须在 resource/memory.resource.md 中注册，否则@引用将无法正常工作
  </rule>
  
  <constraint>
    1. **记忆容量限制** - 内存中可同时处理的记忆条目数量有限
    2. **存储性能限制** - 存储操作不应明显延迟对话响应时间
    3. **检索精度限制** - 记忆检索存在准确性与召回率的平衡难题
    4. **工具调用限制** - 记忆读写依赖于系统支持的工具调用能力
    5. **文件访问限制** - 记忆文件的访问可能受环境权限限制
    6. **格式兼容性限制** - 记忆格式需兼容不同环境的解析能力
  </constraint>
  
  <criteria>
    | 指标 | 通过标准 | 不通过标准 |
    |------|---------|-----------|
    | 初始化完整性 | 角色初始化时自动加载记忆 | 需用户提醒才加载记忆 |
    | 存储可靠性 | 通过工具调用实际存储并验证 | 仅在对话中声明或无验证 |
    | 存储格式符合性 | 遵循统一格式和标签系统 | 格式不一致或标签混乱 |
    | 价值评估准确性 | 正确识别高价值信息 | 存储低价值或忽略高价值信息 |
    | 回忆及时性 | 相关场景下主动检索应用 | 需用户明确提醒才回忆 |
    | 回忆自然性 | 记忆自然融入回答 | 生硬引用或过度突显记忆来源 |
    | 记忆一致性 | 不同会话中记忆表现一致 | 记忆应用不一致或丢失 |
    | 循环完整性 | 评估-存储-回忆循环完整 | 循环断裂或缺少关键环节 |
  </criteria>
</execution> 