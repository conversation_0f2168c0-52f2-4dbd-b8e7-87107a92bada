# UI渲染系统演示程序
import pygame
import sys
from ui import UI
from board import Board
from tetromino import Tetromino
from score_manager import ScoreManager
from config import GAME_CONFIG, CUTE_COLORS

def main():
    """UI演示主程序"""
    # 初始化pygame
    pygame.init()
    
    # 创建屏幕
    screen = pygame.display.set_mode((GAME_CONFIG['WINDOW_WIDTH'], GAME_CONFIG['WINDOW_HEIGHT']))
    pygame.display.set_caption("可爱俄罗斯方块 - UI演示")
    
    # 创建游戏组件
    ui = UI(screen)
    board = Board()
    score_manager = ScoreManager()
    
    # 添加一些测试数据
    score_manager.add_line_score(4)  # Tetris
    score_manager.add_line_score(2)  # 双行
    score_manager.add_placement_bonus()
    score_manager.add_placement_bonus()
    score_manager.add_drop_score(5, is_hard_drop=True)
    
    # 在游戏板上放置一些方块进行演示
    test_tetromino = Tetromino('T')
    board.place_tetromino(test_tetromino, 3, 17)
    
    test_tetromino2 = Tetromino('I')
    board.place_tetromino(test_tetromino2, 6, 16)
    
    # 填充一些行用于演示
    for x in range(7):
        board.set_cell(x, 19, CUTE_COLORS['S'])
    
    # 创建下一个方块用于预览
    next_piece = Tetromino('O')
    
    # 创建当前方块用于演示
    current_piece = Tetromino('L')
    current_x, current_y = 4, 5
    
    clock = pygame.time.Clock()
    demo_mode = 0  # 0: 正常游戏, 1: 暂停, 2: 游戏结束
    
    print("UI演示程序启动")
    print("控制说明:")
    print("- 空格键: 切换演示模式 (正常/暂停/游戏结束)")
    print("- ESC键: 退出程序")
    
    running = True
    while running:
        # 处理事件
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_SPACE:
                    demo_mode = (demo_mode + 1) % 3
                    print(f"切换到模式: {['正常游戏', '暂停', '游戏结束'][demo_mode]}")
        
        # 绘制背景
        ui.draw_background()
        
        # 绘制标题
        ui.draw_title()
        
        # 绘制游戏板
        ui.draw_board(board)
        
        # 绘制当前方块
        ui.draw_tetromino(current_piece, current_x, current_y)
        
        # 绘制幽灵方块
        ui.draw_ghost_piece(current_piece, current_x, current_y, board)
        
        # 绘制分数面板
        ui.draw_score_panel(score_manager)
        
        # 绘制等级进度条
        ui.draw_level_progress_bar(score_manager)
        
        # 绘制下一个方块预览
        ui.draw_next_piece(next_piece)
        
        # 绘制控制说明
        ui.draw_controls_help()
        
        # 根据演示模式绘制不同界面
        if demo_mode == 1:  # 暂停模式
            ui.draw_pause_screen()
        elif demo_mode == 2:  # 游戏结束模式
            ui.draw_game_over_screen(score_manager.get_current_score())
        
        # 更新显示
        pygame.display.flip()
        clock.tick(GAME_CONFIG['FPS'])
    
    pygame.quit()
    print("UI演示程序结束")

if __name__ == "__main__":
    main()