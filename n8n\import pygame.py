import pygame
import random
import sys
import math
from pygame.locals import *

class GameConstants:
    # 颜色定义
    BLACK = (0, 0, 0)
    WHITE = (255, 255, 255)
    GRAY = (128, 128, 128)
    RED = (255, 105, 97)      # 柔和的红色
    GREEN = (119, 221, 119)    # 柔和的绿色
    BLUE = (122, 179, 255)     # 柔和的蓝色
    YELLOW = (255, 230, 109)   # 柔和的黄色
    PURPLE = (215, 155, 255)   # 柔和的紫色
    CYAN = (157, 242, 242)     # 柔和的青色
    ORANGE = (255, 179, 122)   # 柔和的橙色
    PINK = (255, 182, 193)     # 粉色

    # 游戏配置
    FPS = 60
    CELL_SIZE = 30
    BOARD_WIDTH = 10
    BOARD_HEIGHT = 20
    SIDEBAR_WIDTH = 200
    BG_COLOR = (252, 242, 251)       # 淡粉色背景
    LIGHT_PURPLE_PATTERN_COLOR = (240, 230, 245) # 淡淡的紫色图案颜色

    CHERRY_BLOSSOM_MIN_SIZE = 5
    CHERRY_BLOSSOM_MAX_SIZE = 12

    GRID_COLOR = (230, 220, 240)     # 淡紫色网格
    BORDER_COLOR = (180, 160, 200)   # 紫色边框
    TEXT_COLOR = (100, 80, 120)      # 深紫色文字
    BUTTON_COLOR = (230, 200, 240)   # 淡紫色按钮
    BUTTON_HOVER_COLOR = (210, 180, 220)  # 按钮悬停颜色

GameConstants.WINDOW_WIDTH = GameConstants.CELL_SIZE * GameConstants.BOARD_WIDTH + GameConstants.SIDEBAR_WIDTH
GameConstants.WINDOW_HEIGHT = GameConstants.CELL_SIZE * GameConstants.BOARD_HEIGHT

# 方块形状定义 (每种方块的形状和旋转状态)
SHAPES = {
    'I': [[(0, 0), (0, 1), (0, 2), (0, 3)],
          [(0, 0), (1, 0), (2, 0), (3, 0)]],
    'J': [[(0, 0), (0, 1), (0, 2), (1, 2)],
          [(0, 0), (1, 0), (2, 0), (0, 1)],
          [(0, 0), (1, 0), (1, 1), (1, 2)],
          [(2, 0), (0, 1), (1, 1), (2, 1)]],
    'L': [[(1, 0), (1, 1), (1, 2), (0, 2)],
          [(0, 0), (1, 0), (2, 0), (2, 1)],
          [(0, 0), (0, 1), (0, 2), (1, 0)],
          [(0, 0), (0, 1), (1, 1), (2, 1)]],
    'O': [[(0, 0), (0, 1), (1, 0), (1, 1)]],
    'S': [[(1, 0), (2, 0), (0, 1), (1, 1)],
          [(0, 0), (0, 1), (1, 1), (1, 2)]],
    'T': [[(1, 0), (0, 1), (1, 1), (2, 1)],
          [(0, 0), (0, 1), (1, 1), (0, 2)],
          [(0, 0), (1, 0), (2, 0), (1, 1)],
          [(1, 0), (0, 1), (1, 1), (1, 2)]],
    'Z': [[(0, 0), (1, 0), (1, 1), (2, 1)],
          [(1, 0), (0, 1), (1, 1), (0, 2)]]
}

# 方块颜色
SHAPE_COLORS = {
    'I': GameConstants.CYAN,
    'J': GameConstants.BLUE,
    'L': GameConstants.ORANGE,
    'O': GameConstants.YELLOW,
    'S': GameConstants.GREEN,
    'T': GameConstants.PURPLE,
    'Z': GameConstants.RED
}

class Tetromino:
    def __init__(self, shape=None):
        if shape is None:
            shape = random.choice(list(SHAPES.keys()))
        self.shape = shape
        self.color = SHAPE_COLORS[shape]
        self.rotation = 0
        self.blocks = SHAPES[shape][self.rotation]
        self.x = GameConstants.BOARD_WIDTH // 2 - 1
        self.y = 0
    
    def rotate(self, board):
        old_rotation = self.rotation
        self.rotation = (self.rotation + 1) % len(SHAPES[self.shape])
        self.blocks = SHAPES[self.shape][self.rotation]
        
        # 检查旋转后是否有碰撞，如果有则恢复原状态
        if self._check_collision(board):
            self.rotation = old_rotation
            self.blocks = SHAPES[self.shape][self.rotation]
            return False
        return True
    
    def move(self, dx, dy, board):
        if not self._check_collision(board, dx, dy):
            self.x += dx
            self.y += dy
            return True
        return False
    
    def _check_collision(self, board, offset_x=0, offset_y=0):
        for block_x, block_y in self.blocks:
            x = self.x + block_x + offset_x
            y = self.y + block_y + offset_y
            
            # 检查是否超出边界
            if x < 0 or x >= GameConstants.BOARD_WIDTH or y >= GameConstants.BOARD_HEIGHT:
                return True
            
            # 检查是否与已固定的方块重叠
            if y >= 0 and board[y][x] is not None:
                return True
        
        return False
    
    def get_positions(self):
        positions = []
        for block_x, block_y in self.blocks:
            positions.append((self.x + block_x, self.y + block_y))
        return positions

class Board:
    def __init__(self):
        self.grid = [[None for _ in range(GameConstants.BOARD_WIDTH)] for _ in range(GameConstants.BOARD_HEIGHT)]
        self.current_piece = Tetromino()
        self.next_piece = Tetromino()
        self.game_over = False
        self.score = 0
        self.level = 1
        self.lines_cleared = 0
        self.fall_speed = 1.0  # 初始下落速度
        self.fall_timer = 0
    
    def reset(self):
        self.grid = [[None for _ in range(GameConstants.BOARD_WIDTH)] for _ in range(GameConstants.BOARD_HEIGHT)]
        self.current_piece = Tetromino()
        self.next_piece = Tetromino()
        self.game_over = False
        self.score = 0
        self.level = 1
        self.lines_cleared = 0
        self.fall_speed = 1.0
        self.fall_timer = 0
    
    def lock_piece(self):
        for x, y in self.current_piece.get_positions():
            if y < 0:  # 如果方块锁定位置超出顶部，游戏结束
                self.game_over = True
                return
            
            self.grid[y][x] = self.current_piece.color
        
        # 检查并消除已满的行
        self.clear_lines()
        
        # 生成新的方块
        self.current_piece = self.next_piece
        self.next_piece = Tetromino()
        
        # 检查新方块是否可以放置，如果不能则游戏结束
        if self.current_piece._check_collision(self.grid):
            self.game_over = True
    
    def clear_lines(self):
        lines_to_clear = []
        
        # 找出所有已满的行
        for y in range(GameConstants.BOARD_HEIGHT):
            if all(cell is not None for cell in self.grid[y]):
                lines_to_clear.append(y)
        
        if not lines_to_clear:
            return
        
        # 更新分数和等级
        lines_count = len(lines_to_clear)
        self.lines_cleared += lines_count
        self.score += [0, 100, 300, 500, 800][lines_count] * self.level
        self.level = min(10, 1 + self.lines_cleared // 10)
        self.fall_speed = 1.0 - (self.level - 1) * 0.05  # 随等级提高速度
        
        # 消除行并下移上方的方块
        for line in sorted(lines_to_clear):
            del self.grid[line]
            self.grid.insert(0, [None for _ in range(GameConstants.BOARD_WIDTH)])
    
    def update(self, dt):
        if self.game_over:
            return
        
        # 更新下落计时器
        self.fall_timer += dt
        if self.fall_timer >= self.fall_speed:
            self.fall_timer = 0
            # 尝试下移当前方块，如果不能则锁定
            if not self.current_piece.move(0, 1, self.grid):
                self.lock_piece()
                return # 锁定后立即返回，避免在同一帧内再次处理下落

class Game:
    def __init__(self):
        pygame.init()
        pygame.display.set_caption('可爱俄罗斯方块')
        
        self.clock = pygame.time.Clock()
        self.screen = pygame.display.set_mode((GameConstants.WINDOW_WIDTH, GameConstants.WINDOW_HEIGHT))
        
        # 修改字体设置，使用系统默认字体以支持中文，并调整大小
        try:
            self.font = pygame.font.SysFont('SimHei', 28)       # 减小标题字体
            self.small_font = pygame.font.SysFont('SimHei', 20) # 减小信息字体
            self.tiny_font = pygame.font.SysFont('SimHei', 14)  # 更小的字体用于操作说明
        except:
            # 如果找不到黑体，则尝试使用系统默认字体
            self.font = pygame.font.SysFont(None, 28)
            self.small_font = pygame.font.SysFont(None, 20)
            self.tiny_font = pygame.font.SysFont(None, 14)
        
        self.board = Board()
        self.paused = False
        self.high_score = 0
        
        # 加载音效和音乐
        self.init_sounds()
        
        # 添加可爱元素的图案
        self.patterns = [
            [(0, 0), (1, 1), (2, 2), (3, 3)],  # 对角线图案
            [(0, 3), (1, 2), (2, 1), (3, 0)],  # 反对角线图案
            [(1, 1), (1, 2), (2, 1), (2, 2)],  # 小方块图案
            [(0, 0), (0, 3), (3, 0), (3, 3)]   # 四角图案
        ]
        
        # 初始化樱花粒子
        self.cherry_blossoms = []
        self.cherry_blossom_image = self.create_cherry_blossom_image(GameConstants.CHERRY_BLOSSOM_MAX_SIZE) # 预渲染一个最大尺寸的樱花
        for _ in range(15):  # 创建15个樱花
            self.add_cherry_blossom()
    
    def add_cherry_blossom(self):
        # 在右侧边栏随机位置创建一个樱花
        x = GameConstants.BOARD_WIDTH * GameConstants.CELL_SIZE + random.randint(10, GameConstants.SIDEBAR_WIDTH - 10)
        y = random.randint(-20, 0)  # 从顶部开始
        size = random.randint(GameConstants.CHERRY_BLOSSOM_MIN_SIZE, GameConstants.CHERRY_BLOSSOM_MAX_SIZE)  # 樱花大小
        speed = random.uniform(0.5, 2.0)  # 下落速度
        rotation = random.uniform(0, 360)  # 初始旋转角度
        rotation_speed = random.uniform(-1, 1)  # 旋转速度
        self.cherry_blossoms.append({
            'x': x, 'y': y, 'size': size, 'speed': speed,
            'rotation': rotation, 'rotation_speed': rotation_speed,
            'alpha': random.randint(150, 230)  # 透明度
        })
    
    def update_cherry_blossoms(self, dt):
        # 更新樱花位置
        for blossom in self.cherry_blossoms:
            blossom['y'] += blossom['speed'] * 30 * dt
            blossom['x'] += math.sin(blossom['y'] * 0.05) * 0.5  # 左右摆动
            blossom['rotation'] += blossom['rotation_speed'] * 30 * dt
            
            # 如果樱花飘出屏幕，重新从顶部开始
            if blossom['y'] > GameConstants.WINDOW_HEIGHT:
                blossom['y'] = random.randint(-20, 0)
                blossom['x'] = GameConstants.BOARD_WIDTH * GameConstants.CELL_SIZE + random.randint(10, GameConstants.SIDEBAR_WIDTH - 10)
    
    def create_cherry_blossom_image(self, size):
        # 预渲染一个樱花图像
        temp_surf = pygame.Surface((size*2, size*2), pygame.SRCALPHA)
        color = (255, 222, 230)  # 淡粉色
        center = (size, size)
        for i in range(5):
            angle = i * 72  # 五瓣，每瓣72度
            x = center[0] + size * 0.5 * math.cos(math.radians(angle))
            y = center[1] + size * 0.5 * math.sin(math.radians(angle))
            pygame.draw.ellipse(temp_surf, color, 
                               (x - size*0.5, y - size*0.25, size, size*0.5))
        pygame.draw.circle(temp_surf, (255, 255, 200), center, size*0.2)
        return temp_surf

    def draw_cherry_blossom(self, blossom):
        # 绘制樱花
        size = blossom['size']
        # 根据樱花大小缩放预渲染的图像
        scaled_image = pygame.transform.scale(self.cherry_blossom_image, (size*2, size*2))
        # 设置透明度
        scaled_image.set_alpha(blossom['alpha'])
        
        # 旋转樱花
        rotated = pygame.transform.rotate(scaled_image, blossom['rotation'])
        rot_rect = rotated.get_rect(center=(blossom['x'], blossom['y']))
        self.screen.blit(rotated, rot_rect.topleft)

    def init_sounds(self):
        try:
            pygame.mixer.init()
            # 这里可以加载音效和音乐
            # self.rotate_sound = pygame.mixer.Sound('rotate.wav')
            # self.clear_sound = pygame.mixer.Sound('clear.wav')
            # pygame.mixer.music.load('theme.mp3')
            # pygame.mixer.music.play(-1)  # 循环播放背景音乐
        except:
            print("无法初始化音频系统")
    
    def draw_text(self, text, font, color, x, y, align="left"):
        text_surface = font.render(text, True, color)
        text_rect = text_surface.get_rect()
        if align == "center":
            text_rect.centerx = x
        elif align == "right":
            text_rect.right = x
        else:  # left align
            text_rect.left = x
        text_rect.y = y
        self.screen.blit(text_surface, text_rect)
    
    def draw_button(self, text, x, y, width, height, action=None):
        mouse = pygame.mouse.get_pos()
        click = pygame.mouse.get_pressed()
        
        # 绘制圆角按钮
        rect = pygame.Rect(x, y, width, height)
        radius = 10
        
        # 检查鼠标是否悬停在按钮上
        if x < mouse[0] < x + width and y < mouse[1] < y + height:
            color = GameConstants.BUTTON_HOVER_COLOR
            if click[0] == 1 and action is not None:
                action()
        else:
            color = GameConstants.BUTTON_COLOR
        
        # 绘制圆角矩形
        pygame.draw.rect(self.screen, color, rect, border_radius=radius)
        pygame.draw.rect(self.screen, GameConstants.BORDER_COLOR, rect, 2, border_radius=radius)
        
        # 绘制按钮文本
        button_text = self.small_font.render(text, True, GameConstants.TEXT_COLOR)
        text_rect = button_text.get_rect(center=(x + width/2, y + height/2))
        self.screen.blit(button_text, text_rect)
    
    def draw_board(self):
        # 绘制游戏区域背景
        board_rect = pygame.Rect(0, 0, GameConstants.BOARD_WIDTH * GameConstants.CELL_SIZE, GameConstants.BOARD_HEIGHT * GameConstants.CELL_SIZE)
        pygame.draw.rect(self.screen, GameConstants.BG_COLOR, board_rect)
        
        # 绘制可爱的背景图案
        for pattern in self.patterns:
            pattern_color = GameConstants.LIGHT_PURPLE_PATTERN_COLOR  # 淡淡的紫色
            for x, y in pattern:
                x = x * (GameConstants.BOARD_WIDTH // 4) * GameConstants.CELL_SIZE
                y = y * (GameConstants.BOARD_HEIGHT // 4) * GameConstants.CELL_SIZE
                pygame.draw.circle(self.screen, pattern_color, (x, y), 5)
        
        # 绘制网格线
        for x in range(GameConstants.BOARD_WIDTH + 1):
            pygame.draw.line(self.screen, GameConstants.GRID_COLOR, 
                            (x * GameConstants.CELL_SIZE, 0), 
                            (x * GameConstants.CELL_SIZE, GameConstants.BOARD_HEIGHT * GameConstants.CELL_SIZE))
        for y in range(GameConstants.BOARD_HEIGHT + 1):
            pygame.draw.line(self.screen, GameConstants.GRID_COLOR, 
                            (0, y * GameConstants.CELL_SIZE), 
                            (GameConstants.BOARD_WIDTH * GameConstants.CELL_SIZE, y * GameConstants.CELL_SIZE))
        
        # 绘制已固定的方块
        for y in range(GameConstants.BOARD_HEIGHT):
            for x in range(GameConstants.BOARD_WIDTH):
                if self.board.grid[y][x] is not None:
                    self.draw_cell(x, y, self.board.grid[y][x])
        
        # 绘制幽灵方块
        ghost_y = self.board.current_piece.y
        while not self.board.current_piece._check_collision(self.board.grid, 0, ghost_y - self.board.current_piece.y + 1):
            ghost_y += 1
        
        for x, y in self.board.current_piece.get_positions():
            if ghost_y + y >= 0: # 确保幽灵方块在屏幕内
                # 绘制半透明的幽灵方块
                ghost_color = self.board.current_piece.color[:3] + (80,) # 添加透明度
                self.draw_cell_alpha(x, ghost_y + y, ghost_color)

        # 绘制当前下落的方块
        for x, y in self.board.current_piece.get_positions():
            if y >= 0:  # 只绘制在游戏区域内的部分
                self.draw_cell(x, y, self.board.current_piece.color)
        
        # 绘制游戏区域边框
        pygame.draw.rect(self.screen, GameConstants.BORDER_COLOR, board_rect, 2)
    
    def draw_cell(self, x, y, color):
        # 绘制圆角方块
        rect = pygame.Rect(x * GameConstants.CELL_SIZE + 1, y * GameConstants.CELL_SIZE + 1, GameConstants.CELL_SIZE - 2, GameConstants.CELL_SIZE - 2)
        pygame.draw.rect(self.screen, color, rect, border_radius=5)
        
        # 添加高光效果
        highlight = pygame.Surface((GameConstants.CELL_SIZE - 6, GameConstants.CELL_SIZE - 6), pygame.SRCALPHA)
        highlight.fill((255, 255, 255, 80))  # 半透明白色
        highlight_rect = highlight.get_rect(topleft=(x * GameConstants.CELL_SIZE + 3, y * GameConstants.CELL_SIZE + 3))
        pygame.draw.rect(highlight, (255, 255, 255, 80), highlight.get_rect(), border_radius=3)
        self.screen.blit(highlight, highlight_rect)
        
        # 添加边框
        pygame.draw.rect(self.screen, GameConstants.BORDER_COLOR, rect, 1, border_radius=5)

    def draw_cell_alpha(self, x, y, color):
        # 绘制带透明度的圆角方块
        rect = pygame.Rect(x * GameConstants.CELL_SIZE + 1, y * GameConstants.CELL_SIZE + 1, GameConstants.CELL_SIZE - 2, GameConstants.CELL_SIZE - 2)
        
        # 创建一个带有alpha通道的Surface
        s = pygame.Surface((GameConstants.CELL_SIZE - 2, GameConstants.CELL_SIZE - 2), pygame.SRCALPHA)
        pygame.draw.rect(s, color, s.get_rect(), border_radius=5)
        self.screen.blit(s, rect.topleft)
        
        # 绘制边框
        pygame.draw.rect(self.screen, GameConstants.BORDER_COLOR, rect, 1, border_radius=5)
    
    def draw_sidebar(self):
        sidebar_rect = pygame.Rect(GameConstants.BOARD_WIDTH * GameConstants.CELL_SIZE, 0, GameConstants.SIDEBAR_WIDTH, GameConstants.WINDOW_HEIGHT)
        pygame.draw.rect(self.screen, GameConstants.BG_COLOR, sidebar_rect)
        
        # 绘制樱花
        for blossom in self.cherry_blossoms:
            self.draw_cherry_blossom(blossom)
        
        # 绘制下一个方块预览
        preview_x = GameConstants.BOARD_WIDTH * GameConstants.CELL_SIZE + 50
        preview_y = 30
        self.draw_text("下一个:", self.font, GameConstants.TEXT_COLOR, preview_x, preview_y)
        preview_y += 35
        
        # 绘制预览区域背景 - 增加高度确保足够空间
        preview_rect = pygame.Rect(preview_x - 10, preview_y - 5, 120, 120)
        pygame.draw.rect(self.screen, (245, 235, 250), preview_rect, border_radius=10)
        pygame.draw.rect(self.screen, GameConstants.BORDER_COLOR, preview_rect, 2, border_radius=10)
        
        # 计算预览区域的中心位置
        # 获取当前方块的尺寸以便居中显示
        next_piece_blocks = SHAPES[self.board.next_piece.shape][0] # 使用默认旋转状态的方块形状
        
        # 计算方块的边界
        min_x = min(block_x for block_x, _ in next_piece_blocks)
        max_x = max(block_x for block_x, _ in next_piece_blocks)
        min_y = min(block_y for _, block_y in next_piece_blocks)
        max_y = max(block_y for _, block_y in next_piece_blocks)
        
        # 计算方块的宽度和高度
        width = max_x - min_x + 1
        height = max_y - min_y + 1
        
        # 计算居中位置
        center_x = preview_x + 60 - (width * GameConstants.CELL_SIZE) / 2
        center_y = preview_y + 60 - (height * GameConstants.CELL_SIZE) / 2
        
        # 绘制预览方块
        for block_x, block_y in next_piece_blocks:
            # 调整位置，使方块居中显示
            x = center_x + (block_x - min_x) * GameConstants.CELL_SIZE
            y = center_y + (block_y - min_y) * GameConstants.CELL_SIZE

            rect = pygame.Rect(x, y, GameConstants.CELL_SIZE, GameConstants.CELL_SIZE)
            pygame.draw.rect(self.screen, self.board.next_piece.color, rect, border_radius=3)
            pygame.draw.rect(self.screen, GameConstants.BORDER_COLOR, rect, 1, border_radius=3)
        
        # 绘制游戏信息，使用更紧凑的布局
        info_y = preview_y + 140  # 调整位置以适应更大的预览区域
        info_spacing = 30  # 减小行间距
        
        # 绘制信息面板背景
        info_rect = pygame.Rect(preview_x - 10, info_y - 5, 120, 150)
        pygame.draw.rect(self.screen, (245, 235, 250), info_rect, border_radius=10)
        pygame.draw.rect(self.screen, GameConstants.BORDER_COLOR, info_rect, 2, border_radius=10)
        
        # 绘制游戏信息文本
        self.draw_text(f"分数:", self.small_font, GameConstants.TEXT_COLOR, preview_x, info_y)
        self.draw_text(f"{self.board.score}", self.small_font, GameConstants.TEXT_COLOR, preview_x + 70, info_y, "right")
        info_y += info_spacing
        
        self.draw_text(f"等级:", self.small_font, GameConstants.TEXT_COLOR, preview_x, info_y)
        self.draw_text(f"{self.board.level}", self.small_font, GameConstants.TEXT_COLOR, preview_x + 70, info_y, "right")
        info_y += info_spacing
        
        self.draw_text(f"行数:", self.small_font, GameConstants.TEXT_COLOR, preview_x, info_y)
        self.draw_text(f"{self.board.lines_cleared}", self.small_font, GameConstants.TEXT_COLOR, preview_x + 70, info_y, "right")
        info_y += info_spacing
        
        self.draw_text(f"最高分:", self.small_font, GameConstants.TEXT_COLOR, preview_x, info_y)
        self.draw_text(f"{self.high_score}", self.small_font, GameConstants.TEXT_COLOR, preview_x + 70, info_y, "right")
        
        # 绘制控制按钮
        button_y = info_y + 60
        self.draw_button("重新开始", preview_x, button_y, 100, 30, self.restart_game)
        button_y += 40
        self.draw_button("暂停/继续", preview_x, button_y, 100, 30, self.toggle_pause)
        
        # 添加操作说明 - 修复溢出问题
        controls_y = button_y + 50
        # 增加控制说明面板的高度
        controls_rect = pygame.Rect(preview_x - 10, controls_y - 5, 120, 140)
        pygame.draw.rect(self.screen, (245, 235, 250), controls_rect, border_radius=10)
        pygame.draw.rect(self.screen, GameConstants.BORDER_COLOR, controls_rect, 2, border_radius=10)
        
        self.draw_text("操作说明:", self.small_font, GameConstants.TEXT_COLOR, preview_x, controls_y)
        controls_y += 25
        # 使用更小的字体和更紧凑的间距
        line_spacing = 18  # 减小行间距
        self.draw_text("↑: 旋转", self.tiny_font, GameConstants.TEXT_COLOR, preview_x, controls_y)
        controls_y += line_spacing
        self.draw_text("←→: 移动", self.tiny_font, GameConstants.TEXT_COLOR, preview_x, controls_y)
        controls_y += line_spacing
        self.draw_text("↓: 加速下落", self.tiny_font, GameConstants.TEXT_COLOR, preview_x, controls_y)
        controls_y += line_spacing
        self.draw_text("空格: 暂停", self.tiny_font, GameConstants.TEXT_COLOR, preview_x, controls_y)
        controls_y += line_spacing
        self.draw_text("回车: 硬降", self.tiny_font, GameConstants.TEXT_COLOR, preview_x, controls_y)
    
    def draw_game_over(self):
        overlay = pygame.Surface((GameConstants.WINDOW_WIDTH, GameConstants.WINDOW_HEIGHT), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 128))  # 半透明黑色遮罩
        self.screen.blit(overlay, (0, 0))
        
        # 绘制游戏结束面板
        panel_width, panel_height = 300, 200
        panel_x = (GameConstants.WINDOW_WIDTH - panel_width) // 2
        panel_y = (GameConstants.WINDOW_HEIGHT - panel_height) // 2
        
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height);
        pygame.draw.rect(self.screen, GameConstants.BG_COLOR, panel_rect, border_radius=15);
        pygame.draw.rect(self.screen, GameConstants.BORDER_COLOR, panel_rect, 3, border_radius=15)
        
        # 添加可爱的装饰
        for i in range(20):
            x = panel_x + random.randint(10, panel_width - 10)
            y = panel_y + random.randint(10, panel_height - 10)
            radius = random.randint(2, 5)
            color = random.choice([GameConstants.PINK, GameConstants.PURPLE, GameConstants.CYAN, GameConstants.YELLOW])
            pygame.draw.circle(self.screen, color, (x, y), radius)
        
        self.draw_text("游戏结束!", self.font, GameConstants.TEXT_COLOR, GameConstants.WINDOW_WIDTH // 2, panel_y + 40, "center")
        self.draw_text(f"最终分数: {self.board.score}", self.font, GameConstants.TEXT_COLOR, GameConstants.WINDOW_WIDTH // 2, panel_y + 80, "center")
        self.draw_button("重新开始", WINDOW_WIDTH // 2 - 50, panel_y + 130, 100, 30, self.restart_game)
    
    def draw_pause(self):
        overlay = pygame.Surface((GameConstants.WINDOW_WIDTH, GameConstants.WINDOW_HEIGHT), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 128))  # 半透明黑色遮罩
        self.screen.blit(overlay, (0, 0))
        
        # 绘制暂停面板
        panel_width, panel_height = 200, 100
        panel_x = (GameConstants.WINDOW_WIDTH - panel_width) // 2
        panel_y = (GameConstants.WINDOW_HEIGHT - panel_height) // 2
        
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height);
        pygame.draw.rect(self.screen, GameConstants.BG_COLOR, panel_rect, border_radius=15);
        pygame.draw.rect(self.screen, GameConstants.BORDER_COLOR, panel_rect, 3, border_radius=15)
        
        self.draw_text("游戏暂停", self.font, GameConstants.TEXT_COLOR, GameConstants.WINDOW_WIDTH // 2, GameConstants.WINDOW_HEIGHT // 2, "center")
    
    def restart_game(self):
        if self.board.score > self.high_score:
            self.high_score = self.board.score
        self.board.reset()
        self.paused = False
    
    def toggle_pause(self):
        self.paused = not self.paused
    
    def handle_input(self):
        for event in pygame.event.get():
            if event.type == QUIT:
                pygame.quit()
                sys.exit()
            
            if event.type == KEYDOWN:
                # 添加空格键暂停功能
                if event.key == K_SPACE and not self.board.game_over:
                    self.toggle_pause()
                    continue
                
                if event.key == K_p:
                    self.toggle_pause()
                
                if self.paused or self.board.game_over:
                    continue
                
                if event.key == K_LEFT:
                    self.board.current_piece.move(-1, 0, self.board.grid)
                elif event.key == K_RIGHT:
                    self.board.current_piece.move(1, 0, self.board.grid)
                elif event.key == K_DOWN:
                    self.board.current_piece.move(0, 1, self.board.grid)
                elif event.key == K_UP:
                    self.board.current_piece.rotate(self.board.grid)
                # 将空格键硬降功能改为回车键
                elif event.key == K_RETURN:
                    # 硬降（直接下落到底部）
                    # 计算方块最终下落的位置
                    ghost_y = self.board.current_piece.y
                    while not self.board.current_piece._check_collision(self.board.grid, 0, ghost_y - self.board.current_piece.y + 1):
                        ghost_y += 1
                    self.board.current_piece.y = ghost_y
                    self.board.lock_piece()
    
    def run(self):
        while True:
            dt = self.clock.tick(GameConstants.FPS) / 1000.0  # 转换为秒
            
            self.handle_input()
            
            if not self.paused and not self.board.game_over:
                self.board.update(dt)
                # 更新樱花
                self.update_cherry_blossoms(dt)
            
            # 绘制游戏
            self.screen.fill(GameConstants.BG_COLOR)  # 使用可爱的背景色
            self.draw_board()
            self.draw_sidebar()
            
            if self.board.game_over:
                self.draw_game_over()
            elif self.paused:
                self.draw_pause()
            
            pygame.display.flip()

if __name__ == "__main__":
    game = Game()
    game.run()