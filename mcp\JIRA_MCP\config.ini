[jira]
server = https://se.hundsun.com
username = yumm07649
password = yumm0314@
cas_login_url = https://hs-cas.hundsun.com/cas/login?service=https://se.hundsun.com/
verify_ssl = true

[query]
# 默认查询参数
default_project = TZXTOIVV
default_issuetype = Bug
default_resolution = Unresolved, 未解决(尚未处理), 未解决(延后解决), 未解决(无法解决), 未解决(不需解决), 解决(修复成功)
default_reporter = !jirasync
default_created_days = 7
default_status = "Closed"

[server]
host = 0.0.0.0
port = 8000
debug = true
reload = true

[logging]
level = INFO
format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
file = jira_mcp.log 