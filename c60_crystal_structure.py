"""
3D可交互旋转的碳60晶体结构可视化
支持鼠标交互旋转、缩放和平移
"""

import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import math

class C60CrystalStructure:
    def __init__(self):
        """初始化C60晶体结构"""
        self.c60_positions = self._generate_c60_coordinates()
        self.bonds = self._generate_c60_bonds()

    def _generate_c60_coordinates(self):
        """生成C60分子的原子坐标"""
        # C60的标准坐标（基于足球烯的几何结构）
        # 这里使用简化的坐标生成方法
        positions = []

        # 生成20个六边形和12个五边形的顶点
        # 使用球面坐标系生成近似的C60结构

        # 黄金比例
        phi = (1 + math.sqrt(5)) / 2

        # 12个五边形的中心点（正二十面体的顶点）
        icosahedron_vertices = [
            [1, 1, 1], [1, 1, -1], [1, -1, 1], [1, -1, -1],
            [-1, 1, 1], [-1, 1, -1], [-1, -1, 1], [-1, -1, -1],
            [0, 1/phi, phi], [0, 1/phi, -phi], [0, -1/phi, phi], [0, -1/phi, -phi],
            [1/phi, phi, 0], [1/phi, -phi, 0], [-1/phi, phi, 0], [-1/phi, -phi, 0],
            [phi, 0, 1/phi], [phi, 0, -1/phi], [-phi, 0, 1/phi], [-phi, 0, -1/phi]
        ]

        # 标准化坐标并缩放
        scale = 3.5  # 调整分子大小
        for vertex in icosahedron_vertices:
            norm = math.sqrt(sum(x**2 for x in vertex))
            normalized = [x/norm * scale for x in vertex]
            positions.append(normalized)

        # 添加更多原子以达到60个原子
        # 在每个面的中心添加原子
        additional_positions = [
            [2.5, 2.5, 0], [2.5, -2.5, 0], [-2.5, 2.5, 0], [-2.5, -2.5, 0],
            [2.5, 0, 2.5], [2.5, 0, -2.5], [-2.5, 0, 2.5], [-2.5, 0, -2.5],
            [0, 2.5, 2.5], [0, 2.5, -2.5], [0, -2.5, 2.5], [0, -2.5, -2.5],
            [3.0, 1.5, 1.5], [3.0, 1.5, -1.5], [3.0, -1.5, 1.5], [3.0, -1.5, -1.5],
            [-3.0, 1.5, 1.5], [-3.0, 1.5, -1.5], [-3.0, -1.5, 1.5], [-3.0, -1.5, -1.5],
            [1.5, 3.0, 1.5], [1.5, 3.0, -1.5], [-1.5, 3.0, 1.5], [-1.5, 3.0, -1.5],
            [1.5, -3.0, 1.5], [1.5, -3.0, -1.5], [-1.5, -3.0, 1.5], [-1.5, -3.0, -1.5],
            [1.5, 1.5, 3.0], [1.5, -1.5, 3.0], [-1.5, 1.5, 3.0], [-1.5, -1.5, 3.0],
            [1.5, 1.5, -3.0], [1.5, -1.5, -3.0], [-1.5, 1.5, -3.0], [-1.5, -1.5, -3.0],
            [2.8, 0.8, 0.8], [2.8, 0.8, -0.8], [2.8, -0.8, 0.8], [2.8, -0.8, -0.8]
        ]

        positions.extend(additional_positions)

        # 确保有60个原子
        return np.array(positions[:60])

    def _generate_c60_bonds(self):
        """生成C60分子的化学键"""
        bonds = []
        positions = self.c60_positions

        # 基于距离生成键
        bond_threshold = 2.0  # 键长阈值

        for i in range(len(positions)):
            for j in range(i+1, len(positions)):
                distance = np.linalg.norm(positions[i] - positions[j])
                if distance < bond_threshold:
                    bonds.append([i, j])

        return bonds

    def create_3d_visualization(self, show_bonds=True, show_labels=False):
        """创建3D可视化图形"""
        positions = self.c60_positions

        # 创建原子的3D散点图
        fig = go.Figure()

        # 添加原子
        fig.add_trace(go.Scatter3d(
            x=positions[:, 0],
            y=positions[:, 1],
            z=positions[:, 2],
            mode='markers',
            marker=dict(
                size=8,
                color='darkgray',
                opacity=0.8,
                line=dict(width=2, color='black')
            ),
            text=[f'C{i+1}' for i in range(len(positions))],
            hovertemplate='<b>%{text}</b><br>' +
                         'X: %{x:.2f}<br>' +
                         'Y: %{y:.2f}<br>' +
                         'Z: %{z:.2f}<extra></extra>',
            name='碳原子'
        ))

        # 添加化学键
        if show_bonds:
            for bond in self.bonds:
                i, j = bond
                fig.add_trace(go.Scatter3d(
                    x=[positions[i, 0], positions[j, 0]],
                    y=[positions[i, 1], positions[j, 1]],
                    z=[positions[i, 2], positions[j, 2]],
                    mode='lines',
                    line=dict(color='gray', width=3),
                    showlegend=False,
                    hoverinfo='skip'
                ))

        # 设置布局
        fig.update_layout(
            title=dict(
                text='C60 (富勒烯) 3D分子结构',
                x=0.5,
                font=dict(size=20, color='darkblue')
            ),
            scene=dict(
                xaxis_title='X (Å)',
                yaxis_title='Y (Å)',
                zaxis_title='Z (Å)',
                bgcolor='white',
                xaxis=dict(gridcolor='lightgray'),
                yaxis=dict(gridcolor='lightgray'),
                zaxis=dict(gridcolor='lightgray'),
                aspectmode='cube'
            ),
            width=800,
            height=600,
            margin=dict(l=0, r=0, b=0, t=50)
        )

        return fig

    def create_crystal_lattice(self, nx=2, ny=2, nz=2, lattice_constant=10.0):
        """创建C60晶体晶格结构"""
        crystal_positions = []
        crystal_bonds = []
        atom_count = 0

        for i in range(nx):
            for j in range(ny):
                for k in range(nz):
                    # 计算晶胞的偏移
                    offset = np.array([i * lattice_constant,
                                     j * lattice_constant,
                                     k * lattice_constant])

                    # 添加当前晶胞中的所有原子
                    cell_positions = self.c60_positions + offset
                    crystal_positions.extend(cell_positions)

                    # 添加当前晶胞中的键
                    for bond in self.bonds:
                        crystal_bonds.append([bond[0] + atom_count, bond[1] + atom_count])

                    atom_count += len(self.c60_positions)

        return np.array(crystal_positions), crystal_bonds

    def visualize_crystal(self, nx=2, ny=2, nz=2, lattice_constant=10.0):
        """可视化晶体结构"""
        crystal_positions, crystal_bonds = self.create_crystal_lattice(nx, ny, nz, lattice_constant)

        fig = go.Figure()

        # 添加所有原子
        fig.add_trace(go.Scatter3d(
            x=crystal_positions[:, 0],
            y=crystal_positions[:, 1],
            z=crystal_positions[:, 2],
            mode='markers',
            marker=dict(
                size=4,
                color='darkgray',
                opacity=0.7,
                line=dict(width=1, color='black')
            ),
            name='碳原子',
            hovertemplate='原子位置<br>' +
                         'X: %{x:.2f}<br>' +
                         'Y: %{y:.2f}<br>' +
                         'Z: %{z:.2f}<extra></extra>'
        ))

        # 添加化学键（只显示部分键以避免过于复杂）
        bond_sample = crystal_bonds[::3]  # 只显示每3个键中的1个
        for bond in bond_sample:
            i, j = bond
            if i < len(crystal_positions) and j < len(crystal_positions):
                fig.add_trace(go.Scatter3d(
                    x=[crystal_positions[i, 0], crystal_positions[j, 0]],
                    y=[crystal_positions[i, 1], crystal_positions[j, 1]],
                    z=[crystal_positions[i, 2], crystal_positions[j, 2]],
                    mode='lines',
                    line=dict(color='lightgray', width=1),
                    showlegend=False,
                    hoverinfo='skip'
                ))

        # 设置布局
        fig.update_layout(
            title=dict(
                text=f'C60晶体结构 ({nx}×{ny}×{nz} 晶胞)',
                x=0.5,
                font=dict(size=18, color='darkblue')
            ),
            scene=dict(
                xaxis_title='X (Å)',
                yaxis_title='Y (Å)',
                zaxis_title='Z (Å)',
                bgcolor='white',
                xaxis=dict(gridcolor='lightgray'),
                yaxis=dict(gridcolor='lightgray'),
                zaxis=dict(gridcolor='lightgray'),
                aspectmode='cube'
            ),
            width=900,
            height=700,
            margin=dict(l=0, r=0, b=0, t=50)
        )

        return fig

def main():
    """主函数 - 演示C60结构的可视化"""
    print("正在生成C60晶体结构...")

    # 创建C60结构实例
    c60 = C60CrystalStructure()

    print(f"生成了 {len(c60.c60_positions)} 个碳原子")
    print(f"生成了 {len(c60.bonds)} 个化学键")

    # 创建单个C60分子的3D可视化
    print("\n创建单个C60分子的3D可视化...")
    fig_molecule = c60.create_3d_visualization(show_bonds=True)

    # 显示分子结构
    fig_molecule.show()

    # 创建晶体结构的3D可视化
    print("创建C60晶体结构的3D可视化...")
    fig_crystal = c60.visualize_crystal(nx=2, ny=2, nz=1, lattice_constant=8.0)

    # 显示晶体结构
    fig_crystal.show()

    print("\n可视化完成！")
    print("您可以使用鼠标进行以下操作：")
    print("- 左键拖拽：旋转视角")
    print("- 滚轮：缩放")
    print("- 右键拖拽：平移")
    print("- 双击：重置视角")

def create_interactive_demo():
    """创建交互式演示"""
    c60 = C60CrystalStructure()

    # 创建子图
    fig = make_subplots(
        rows=1, cols=2,
        specs=[[{'type': 'scatter3d'}, {'type': 'scatter3d'}]],
        subplot_titles=('单个C60分子', 'C60晶体结构'),
        horizontal_spacing=0.1
    )

    # 添加单个分子
    positions = c60.c60_positions
    fig.add_trace(
        go.Scatter3d(
            x=positions[:, 0],
            y=positions[:, 1],
            z=positions[:, 2],
            mode='markers',
            marker=dict(size=6, color='red', opacity=0.8),
            name='C60分子',
            hovertemplate='碳原子<br>X: %{x:.2f}<br>Y: %{y:.2f}<br>Z: %{z:.2f}<extra></extra>'
        ),
        row=1, col=1
    )

    # 添加分子的键
    for bond in c60.bonds[:30]:  # 只显示部分键
        i, j = bond
        fig.add_trace(
            go.Scatter3d(
                x=[positions[i, 0], positions[j, 0]],
                y=[positions[i, 1], positions[j, 1]],
                z=[positions[i, 2], positions[j, 2]],
                mode='lines',
                line=dict(color='darkred', width=2),
                showlegend=False,
                hoverinfo='skip'
            ),
            row=1, col=1
        )

    # 添加晶体结构
    crystal_positions, crystal_bonds = c60.create_crystal_lattice(nx=2, ny=1, nz=1, lattice_constant=8.0)
    fig.add_trace(
        go.Scatter3d(
            x=crystal_positions[:, 0],
            y=crystal_positions[:, 1],
            z=crystal_positions[:, 2],
            mode='markers',
            marker=dict(size=3, color='blue', opacity=0.6),
            name='晶体结构',
            hovertemplate='晶体原子<br>X: %{x:.2f}<br>Y: %{y:.2f}<br>Z: %{z:.2f}<extra></extra>'
        ),
        row=1, col=2
    )

    # 更新布局
    fig.update_layout(
        title_text="C60富勒烯分子与晶体结构对比",
        title_x=0.5,
        width=1200,
        height=600,
        scene=dict(aspectmode='cube'),
        scene2=dict(aspectmode='cube')
    )

    return fig

if __name__ == "__main__":
    # 运行主演示
    main()

    # 创建交互式对比演示
    print("\n创建交互式对比演示...")
    demo_fig = create_interactive_demo()
    demo_fig.show()
