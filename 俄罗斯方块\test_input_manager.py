# InputManager类的单元测试
import unittest
import pygame
import time
from input_manager import InputManager

class TestInputManager(unittest.TestCase):
    """InputManager类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        pygame.init()
        self.input_manager = InputManager()
    
    def tearDown(self):
        """测试后的清理"""
        pygame.quit()
    
    def create_key_event(self, key, event_type):
        """创建键盘事件
        
        Args:
            key: pygame按键常量
            event_type: 事件类型
            
        Returns:
            pygame.Event: 键盘事件
        """
        return pygame.event.Event(event_type, key=key)
    
    def test_initialization(self):
        """测试初始化"""
        im = InputManager()
        
        # 检查初始状态
        self.assertEqual(len(im.keys_pressed), 0)
        self.assertEqual(len(im.keys_just_pressed), 0)
        self.assertEqual(len(im.keys_just_released), 0)
        
        # 检查默认设置
        self.assertGreater(im.key_repeat_delay, 0)
        self.assertGreater(im.key_repeat_rate, 0)
        self.assertGreater(im.move_delay, 0)
        self.assertGreater(im.rotation_delay, 0)
        self.assertGreater(im.soft_drop_rate, 0)
    
    def test_key_press_detection(self):
        """测试按键检测"""
        # 模拟按下左箭头键
        key_down_event = self.create_key_event(pygame.K_LEFT, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        # 检查按键状态
        self.assertTrue(self.input_manager.is_key_pressed('left'))
        self.assertTrue(self.input_manager.is_key_just_pressed('left'))
        self.assertFalse(self.input_manager.is_key_just_released('left'))
        
        # 下一帧更新（没有新事件）
        self.input_manager.update([])
        
        # 按键应该仍然被按下，但不是"刚按下"
        self.assertTrue(self.input_manager.is_key_pressed('left'))
        self.assertFalse(self.input_manager.is_key_just_pressed('left'))
        self.assertFalse(self.input_manager.is_key_just_released('left'))
    
    def test_key_release_detection(self):
        """测试按键释放检测"""
        # 先按下按键
        key_down_event = self.create_key_event(pygame.K_RIGHT, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        # 然后释放按键
        key_up_event = self.create_key_event(pygame.K_RIGHT, pygame.KEYUP)
        self.input_manager.update([key_up_event])
        
        # 检查释放状态
        self.assertFalse(self.input_manager.is_key_pressed('right'))
        self.assertFalse(self.input_manager.is_key_just_pressed('right'))
        self.assertTrue(self.input_manager.is_key_just_released('right'))
    
    def test_movement_input(self):
        """测试移动输入"""
        # 测试向左移动
        key_down_event = self.create_key_event(pygame.K_LEFT, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        # 第一次应该可以移动
        self.assertTrue(self.input_manager.can_move_left())
        
        # 立即再次检查应该不能移动（由于延迟）
        self.assertFalse(self.input_manager.can_move_left())
        
        # 等待移动延迟过去
        time.sleep(0.15)
        
        # 测试向右移动
        key_down_event = self.create_key_event(pygame.K_RIGHT, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        # 应该可以向右移动
        self.assertTrue(self.input_manager.can_move_right())
    
    def test_rotation_input(self):
        """测试旋转输入"""
        # 测试上箭头键旋转
        key_down_event = self.create_key_event(pygame.K_UP, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        # 应该可以旋转
        self.assertTrue(self.input_manager.can_rotate())
        
        # 立即再次检查应该不能旋转（由于延迟）
        self.assertFalse(self.input_manager.can_rotate())
        
        # 测试空格键旋转
        key_down_event = self.create_key_event(pygame.K_SPACE, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        # 需要等待旋转延迟
        time.sleep(0.25)  # 等待超过旋转延迟
        self.assertTrue(self.input_manager.can_rotate())
    
    def test_soft_drop_input(self):
        """测试软下落输入"""
        # 按下下箭头键
        key_down_event = self.create_key_event(pygame.K_DOWN, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        # 应该可以软下落
        self.assertTrue(self.input_manager.should_soft_drop())
        
        # 立即再次检查应该不能软下落（由于速率限制）
        self.assertFalse(self.input_manager.should_soft_drop())
    
    def test_pause_and_restart_input(self):
        """测试暂停和重启输入"""
        # 测试暂停
        key_down_event = self.create_key_event(pygame.K_p, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        self.assertTrue(self.input_manager.is_pause_pressed())
        
        # 下一帧应该不再是"刚按下"
        self.input_manager.update([])
        self.assertFalse(self.input_manager.is_pause_pressed())
        
        # 测试重启
        key_down_event = self.create_key_event(pygame.K_r, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        self.assertTrue(self.input_manager.is_restart_pressed())
    
    def test_quit_input(self):
        """测试退出输入"""
        key_down_event = self.create_key_event(pygame.K_ESCAPE, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        self.assertTrue(self.input_manager.is_quit_pressed())
    
    def test_movement_input_comprehensive(self):
        """测试综合移动输入"""
        # 按下多个键
        events = [
            self.create_key_event(pygame.K_LEFT, pygame.KEYDOWN),
            self.create_key_event(pygame.K_UP, pygame.KEYDOWN),
            self.create_key_event(pygame.K_DOWN, pygame.KEYDOWN)
        ]
        self.input_manager.update(events)
        
        # 获取移动输入状态
        movement = self.input_manager.get_movement_input()
        
        # 检查各种输入
        self.assertTrue(movement['move_left'])
        self.assertFalse(movement['move_right'])
        self.assertTrue(movement['rotate'])
        self.assertTrue(movement['soft_drop'])
        self.assertFalse(movement['hard_drop'])
        self.assertFalse(movement['pause'])
        self.assertFalse(movement['restart'])
        self.assertFalse(movement['quit'])
    
    def test_delay_settings(self):
        """测试延迟设置"""
        # 测试移动延迟设置
        original_delay = self.input_manager.move_delay
        self.input_manager.set_move_delay(0.2)
        self.assertEqual(self.input_manager.move_delay, 0.2)
        
        # 测试最小值限制
        self.input_manager.set_move_delay(0.01)
        self.assertGreaterEqual(self.input_manager.move_delay, 0.05)
        
        # 测试旋转延迟设置
        self.input_manager.set_rotation_delay(0.3)
        self.assertEqual(self.input_manager.rotation_delay, 0.3)
        
        # 测试软下落速率设置
        self.input_manager.set_soft_drop_rate(0.02)
        self.assertEqual(self.input_manager.soft_drop_rate, 0.02)
    
    def test_timer_reset(self):
        """测试计时器重置"""
        # 按下一些键来创建计时器
        events = [
            self.create_key_event(pygame.K_LEFT, pygame.KEYDOWN),
            self.create_key_event(pygame.K_RIGHT, pygame.KEYDOWN)
        ]
        self.input_manager.update(events)
        
        # 应该有活跃的计时器
        self.assertGreater(len(self.input_manager.key_timers), 0)
        
        # 重置计时器
        self.input_manager.reset_timers()
        
        # 计时器应该被清空
        self.assertEqual(len(self.input_manager.key_timers), 0)
        self.assertEqual(len(self.input_manager.key_last_repeat), 0)
        self.assertEqual(self.input_manager.last_move_time, 0)
        self.assertEqual(self.input_manager.last_rotation_time, 0)
        self.assertEqual(self.input_manager.last_soft_drop_time, 0)
    
    def test_debug_info(self):
        """测试调试信息"""
        # 按下一些键
        events = [
            self.create_key_event(pygame.K_LEFT, pygame.KEYDOWN),
            self.create_key_event(pygame.K_SPACE, pygame.KEYDOWN)
        ]
        self.input_manager.update(events)
        
        # 获取调试信息
        debug_info = self.input_manager.get_debug_info()
        
        # 检查调试信息结构
        required_fields = [
            'keys_pressed', 'keys_just_pressed', 'keys_just_released',
            'active_timers', 'move_delay', 'rotation_delay', 'soft_drop_rate'
        ]
        
        for field in required_fields:
            self.assertIn(field, debug_info)
        
        # 检查数据类型
        self.assertIsInstance(debug_info['keys_pressed'], list)
        self.assertIsInstance(debug_info['keys_just_pressed'], list)
        self.assertIsInstance(debug_info['active_timers'], int)
        
        # 检查内容
        self.assertIn('left', debug_info['keys_pressed'])
        self.assertIn('space', debug_info['keys_pressed'])
        self.assertEqual(debug_info['active_timers'], 2)
    
    def test_multiple_key_handling(self):
        """测试多键处理"""
        # 同时按下多个键
        events = [
            self.create_key_event(pygame.K_LEFT, pygame.KEYDOWN),
            self.create_key_event(pygame.K_RIGHT, pygame.KEYDOWN),
            self.create_key_event(pygame.K_UP, pygame.KEYDOWN),
            self.create_key_event(pygame.K_DOWN, pygame.KEYDOWN)
        ]
        self.input_manager.update(events)
        
        # 检查所有键都被识别
        self.assertTrue(self.input_manager.is_key_pressed('left'))
        self.assertTrue(self.input_manager.is_key_pressed('right'))
        self.assertTrue(self.input_manager.is_key_pressed('up'))
        self.assertTrue(self.input_manager.is_key_pressed('down'))
        
        # 释放一些键
        release_events = [
            self.create_key_event(pygame.K_LEFT, pygame.KEYUP),
            self.create_key_event(pygame.K_UP, pygame.KEYUP)
        ]
        self.input_manager.update(release_events)
        
        # 检查释放状态
        self.assertFalse(self.input_manager.is_key_pressed('left'))
        self.assertFalse(self.input_manager.is_key_pressed('up'))
        self.assertTrue(self.input_manager.is_key_pressed('right'))
        self.assertTrue(self.input_manager.is_key_pressed('down'))
        
        self.assertTrue(self.input_manager.is_key_just_released('left'))
        self.assertTrue(self.input_manager.is_key_just_released('up'))
    
    def test_string_representations(self):
        """测试字符串表示"""
        # 空状态
        str_repr = str(self.input_manager)
        self.assertIn('InputManager', str_repr)
        self.assertIn('None', str_repr)
        
        # 有按键状态
        key_down_event = self.create_key_event(pygame.K_LEFT, pygame.KEYDOWN)
        self.input_manager.update([key_down_event])
        
        str_repr = str(self.input_manager)
        self.assertIn('left', str_repr)
        
        repr_str = repr(self.input_manager)
        self.assertEqual(str_repr, repr_str)

if __name__ == '__main__':
    unittest.main()