<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>container</title>
<link href="../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer></script>
<link href="../../images/logo-icon.svg">
<link href="../../styles/stylesheet.css" rel="Stylesheet"></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../index.html">
                    quarkdown
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">1.6.3
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":quarkdown-stdlib/main">jvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":quarkdown-stdlib/main" data-filter=":quarkdown-stdlib/main">
                                <span class="checkbox--icon"></span>
                                jvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    quarkdown
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="member" id="content" pageids="quarkdown-stdlib::com.quarkdown.stdlib//container/#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?#kotlin.Boolean#com.quarkdown.core.misc.color.Color?#com.quarkdown.core.misc.color.Color?#com.quarkdown.core.misc.color.Color?#com.quarkdown.core.document.size.Sizes?#com.quarkdown.core.ast.quarkdown.block.Container.BorderStyle?#com.quarkdown.core.document.size.Sizes?#com.quarkdown.core.document.size.Sizes?#com.quarkdown.core.document.size.Sizes?#com.quarkdown.core.ast.quarkdown.block.Container.Alignment?#com.quarkdown.core.ast.quarkdown.block.Container.TextAlignment?#com.quarkdown.core.ast.quarkdown.block.Container.FloatAlignment?#com.quarkdown.core.ast.MarkdownContent?/PointingToDeclaration//742850071">
  <div class="breadcrumbs"><a href="../index.html">quarkdown-stdlib</a><span class="delimiter">/</span><a href="index.html">com.quarkdown.stdlib.module.Layout</a><span class="delimiter">/</span><span class="current">container</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>container</span></span></h1>
  </div>
  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">container</span><span class="token punctuation"> </span><span class="token constant">width</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">height</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">      </span><span class="token punctuation"> </span><span class="token constant">fullwidth</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-boolean/index.html">Boolean</a><span class="token operator"> = </span><span class="token boolean">false</span><span class="token punctuation">}</span><br><span class="token punctuation">     </span><span class="token punctuation"> </span><span class="token constant">foreground</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.misc.color/Color///PointingToDeclaration/">Color</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">     </span><span class="token punctuation"> </span><span class="token constant">background</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.misc.color/Color///PointingToDeclaration/">Color</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">border</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.misc.color/Color///PointingToDeclaration/">Color</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">    </span><span class="token punctuation"> </span><span class="token constant">borderwidth</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Sizes///PointingToDeclaration/">Sizes</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">    </span><span class="token punctuation"> </span><span class="token constant">borderstyle</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.BorderStyle///PointingToDeclaration/">Container.BorderStyle</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">margin</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Sizes///PointingToDeclaration/">Sizes</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">        </span><span class="token punctuation"> </span><span class="token constant">padding</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Sizes///PointingToDeclaration/">Sizes</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">radius</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Sizes///PointingToDeclaration/">Sizes</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">      </span><span class="token punctuation"> </span><span class="token constant">alignment</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.Alignment///PointingToDeclaration/">Container.Alignment</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">  </span><span class="token punctuation"> </span><span class="token constant">textalignment</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment///PointingToDeclaration/">Container.TextAlignment</span><span class="token operator">?</span><span class="token operator"> = </span>alignment?.let(Container.TextAlignment::fromAlignment)<span class="token punctuation">}</span><br><span class="token punctuation">          </span><span class="token punctuation"> </span><span class="token constant">float</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.FloatAlignment///PointingToDeclaration/">Container.FloatAlignment</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">           </span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><p class="paragraph">A general-purpose container that groups content. Any layout rules (e.g. from <a href="align.html">align</a>, <a href="row.html">row</a>, <a href="column.html">column</a>, <a href="grid.html">grid</a>) are ignored inside this container.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">the new <span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container///PointingToDeclaration/">Container</span> node</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>width</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">width of the container. No constraint if unset</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>height</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">height of the container. No constraint if unset</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>fullwidth</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">whether the container should take up the full width of the parent. Overridden by <a href="container.html">width</a>. False if unset</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>foreground</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">text color. Default if unset</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>background</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">background color. Transparent if unset</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>border</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">border color. Default if unset and <a href="container.html">borderwidth</a> is set</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>borderwidth</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">border width. Default if unset and <a href="container.html">border</a> is set</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>borderstyle</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">border style. Normal (solid) if unset and <a href="container.html">border</a> or <a href="container.html">borderwidth</a> is set</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.BorderStyle.NORMAL///PointingToDeclaration/"><code class="lang-kotlin">normal</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.BorderStyle.DASHED///PointingToDeclaration/"><code class="lang-kotlin">dashed</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.BorderStyle.DOTTED///PointingToDeclaration/"><code class="lang-kotlin">dotted</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.BorderStyle.DOUBLE///PointingToDeclaration/"><code class="lang-kotlin">double</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>margin</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">whitespace outside the content. None if unset</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>padding</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">whitespace around the content. None if unset</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>radius</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">corner (and border) radius. None if unset</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>alignment</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">alignment of the content. Default if unset</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.Alignment.START///PointingToDeclaration/"><code class="lang-kotlin">start</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.Alignment.CENTER///PointingToDeclaration/"><code class="lang-kotlin">center</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.Alignment.END///PointingToDeclaration/"><code class="lang-kotlin">end</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>textalignment</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">alignment of the text. <a href="container.html">alignment</a> if unset</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment.START///PointingToDeclaration/"><code class="lang-kotlin">start</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment.CENTER///PointingToDeclaration/"><code class="lang-kotlin">center</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment.END///PointingToDeclaration/"><code class="lang-kotlin">end</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment.JUSTIFY///PointingToDeclaration/"><code class="lang-kotlin">justify</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>float</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">floating position of the container within the parent. Not floating if unset</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.FloatAlignment.START///PointingToDeclaration/"><code class="lang-kotlin">start</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.FloatAlignment.END///PointingToDeclaration/"><code class="lang-kotlin">end</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>body</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Likely a <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call#block-vs-inline-function-calls">body argument</a></li><li>Optional</li></ul></dl><p class="paragraph">content to group</p></div></div></div></div></div><span class="kdoc-tag"><h4 class="kdoctag">Wiki page</h4><a href="https://github.com/iamgio/quarkdown/wiki/Container">Container</a></span></div></div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Quarkdown</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
