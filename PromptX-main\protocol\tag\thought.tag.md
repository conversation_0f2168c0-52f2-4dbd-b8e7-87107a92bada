# DPML#思考提示单元 框架

> **TL;DR:** DPML#思考提示单元 框架定义了结构化的#思考单元 模板，支持四种核心#思维模式 的#思考提示单元 构建：#探索思维（exploration）、#推理思维（reasoning）、#计划思维（plan）和#挑战思维（challenge），帮助AI系统进行系统性分析和推理。

### 目的与功能

DPML#思考提示单元 框架定义了AI系统进行思考分析的#思考单元 模板和结构，它的主要功能是：
- 提供结构化的#思维分析#思考提示单元 模板
- 规范化#思考单元 的组织方式
- 支持可视化#思维导图 和#决策树 的#思考提示单元 表达
- 帮助AI系统通过标准化#思考提示单元 进行系统性、全面性的问题分析
- 区分不同类型的#思维模式#思考提示单元：#探索思维、#推理思维、#计划思维 和#挑战思维

## 📝 语法定义

```ebnf
(* EBNF形式化定义 *)
thought_element ::= '<thought' attributes? '>' content '</thought>'
attributes ::= (' ' attribute)+ | ''
attribute ::= name '="' value '"'
name ::= [a-zA-Z][a-zA-Z0-9_-]*
value ::= [^"]*
content ::= (markdown_content | exploration_element | reasoning_element | plan_element | challenge_element)+
markdown_content ::= (* 任何有效的Markdown文本，包括Mermaid图表 *)

exploration_element ::= '<exploration' attributes? '>' markdown_content '</exploration>'
reasoning_element ::= '<reasoning' attributes? '>' markdown_content '</reasoning>'
plan_element ::= '<plan' attributes? '>' markdown_content '</plan>'
challenge_element ::= '<challenge' attributes? '>' markdown_content '</challenge>'
```

## 🧩 语义说明

#思考提示单元 标签表示一个完整的#思考单元 或#思维框架。标签内容可以包含四种不同#思维模式 的子标签，或直接使用Markdown格式表达#思考内容。

子标签具有明确的语义：
- **#探索思维**: 表示跳跃思考，发散性思维，生成可能性，寻找多种可能性、创新点和关联性
- **#推理思维**: 表示连续思考，收敛性思维，验证可能性，深入分析因果关系、逻辑链条
- **#计划思维**: 表示秩序思考，结构性思维，固化可能性，设计行动步骤、决策路径、组织结构、系统架构
- **#挑战思维**: 表示逆向跳跃思考，批判性思维，质疑可能性，寻找假设漏洞、识别潜在风险、测试极限条件

#探索思维 和#挑战思维 是一对#思维模式 的正反两面：#探索思维 向外发散寻找"可能是什么"，而#挑战思维 向内批判探究"可能不是什么"。二者都采用跳跃式思考，但方向相反。#推理思维 负责系统验证，而#挑战思维 主要提出问题点。

#思考提示单元 特别适合表达概念关系、逻辑推理和系统性思考，为AI提供#思考分析 的参考框架。

### 推荐的#思考顺序

在实际思考过程中，推荐遵循如下顺序以获得系统性和全面性的分析结果：
1. **#探索思维**：首先发散思考，提出尽可能多的可能性和创新点；
2. **#挑战思维**：对#探索思维 阶段的内容进行批判性思考，识别潜在风险和假设漏洞；
3. **#推理思维**：对经过#挑战思维 筛选的内容进行系统性推理和因果分析；
4. **#计划思维**：最后制定具体的行动方案和决策路径。

在复杂问题中，#挑战思维 和#推理思维 可多次交替，#计划思维 阶段也可穿插#挑战思维 以确保方案稳健性。

### 子标签的可选性

#思考提示单元 内的四种#思维模式 子标签（#探索思维、#挑战思维、#推理思维、#计划思维）均为可选。实际的#思考提示单元 可以只包含其中的一种、几种，或全部，具体内容由实际需求决定。

对于提示词的理解者来说，只需知道这些子标签不是必须全部出现，遇到哪些子标签就理解哪些即可，无需关心未出现的部分。
