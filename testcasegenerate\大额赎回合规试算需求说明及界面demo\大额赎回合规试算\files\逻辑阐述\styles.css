﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-53px;
  width:1529px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u643_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1180px;
  height:151px;
}
#u643 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:665px;
  width:1180px;
  height:151px;
  display:flex;
}
#u643 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u643_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u644_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:504px;
  height:240px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u644 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:359px;
  width:504px;
  height:240px;
  display:flex;
}
#u644 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u644_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u645_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u645 {
  border-width:0px;
  position:absolute;
  left:182px;
  top:332px;
  width:74px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u645 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u645_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u646_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u646 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:636px;
  width:199px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u646 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u646_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u647_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u647 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:886px;
  width:393px;
  height:41px;
  display:flex;
}
#u647 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u647_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u648_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:542px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u648 {
  border-width:0px;
  position:absolute;
  left:599px;
  top:886px;
  width:542px;
  height:41px;
  display:flex;
}
#u648 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u648_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u649 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:926px;
  width:393px;
  height:190px;
}
#u650_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:33px;
}
#u650 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u650 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u650_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u651_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:33px;
}
#u651 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:0px;
  width:162px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
}
#u651 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u651_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u652_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:33px;
}
#u652 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:0px;
  width:131px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#037DF3;
}
#u652 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u652_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u653_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u653 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:33px;
  width:100px;
  height:34px;
  display:flex;
}
#u653 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u653_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u654_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:34px;
}
#u654 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:33px;
  width:162px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u654 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u655_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u655 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:33px;
  width:131px;
  height:34px;
  display:flex;
  color:#037DF3;
}
#u655 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u655_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u656_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:31px;
}
#u656 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:67px;
  width:100px;
  height:31px;
  display:flex;
  color:#000000;
}
#u656 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u656_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u657_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:31px;
}
#u657 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:67px;
  width:162px;
  height:31px;
  display:flex;
  color:#D9001B;
}
#u657 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u657_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u658_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
}
#u658 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:67px;
  width:131px;
  height:31px;
  display:flex;
  color:#037DF3;
}
#u658 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u658_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u659_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:32px;
}
#u659 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:98px;
  width:100px;
  height:32px;
  display:flex;
}
#u659 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u659_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u660_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:32px;
}
#u660 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:98px;
  width:162px;
  height:32px;
  display:flex;
  color:#D9001B;
}
#u660 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u660_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u661_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:32px;
}
#u661 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:98px;
  width:131px;
  height:32px;
  display:flex;
  color:#037DF3;
}
#u661 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u661_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u662_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u662 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:130px;
  width:100px;
  height:30px;
  display:flex;
}
#u662 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u662_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u663_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u663 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:130px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u663 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u663_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u664_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u664 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:130px;
  width:131px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u664 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u664_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u665_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u665 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:100px;
  height:30px;
  display:flex;
}
#u665 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u665_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u666_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u666 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:160px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u666 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u666_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u667_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u667 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:160px;
  width:131px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u667 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u667_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u668 {
  border-width:0px;
  position:absolute;
  left:599px;
  top:926px;
  width:542px;
  height:190px;
}
#u669_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:33px;
}
#u669 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
}
#u669 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u669_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u670_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:33px;
}
#u670 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:0px;
  width:199px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#037DF3;
}
#u670 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u670_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u671_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:33px;
}
#u671 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:0px;
  width:181px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#037DF3;
}
#u671 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u671_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u672_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:34px;
}
#u672 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:33px;
  width:162px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u672 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u672_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u673_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:34px;
}
#u673 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:33px;
  width:199px;
  height:34px;
  display:flex;
  color:#037DF3;
}
#u673 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u673_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u674_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:34px;
}
#u674 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:33px;
  width:181px;
  height:34px;
  display:flex;
  color:#037DF3;
}
#u674 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u674_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u675_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:31px;
}
#u675 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:67px;
  width:162px;
  height:31px;
  display:flex;
  color:#D9001B;
}
#u675 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u675_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u676_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:31px;
}
#u676 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:67px;
  width:199px;
  height:31px;
  display:flex;
  color:#037DF3;
}
#u676 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u676_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u677_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:31px;
}
#u677 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:67px;
  width:181px;
  height:31px;
  display:flex;
  color:#037DF3;
}
#u677 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u678_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:32px;
}
#u678 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:98px;
  width:162px;
  height:32px;
  display:flex;
  color:#D9001B;
}
#u678 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u679_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:32px;
}
#u679 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:98px;
  width:199px;
  height:32px;
  display:flex;
  color:#037DF3;
}
#u679 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u679_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u680_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:32px;
}
#u680 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:98px;
  width:181px;
  height:32px;
  display:flex;
  color:#037DF3;
}
#u680 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u681_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u681 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:130px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u681 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u682_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:30px;
}
#u682 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:130px;
  width:199px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u682 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u683_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:30px;
}
#u683 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:130px;
  width:181px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u683 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u683_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u684_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u684 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u684 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u684_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u685_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:30px;
}
#u685 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:160px;
  width:199px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u685 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u685_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u686_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:30px;
}
#u686 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:160px;
  width:181px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u686 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u686_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u687_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:209px;
}
#u687 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:1394px;
  width:393px;
  height:209px;
  display:flex;
}
#u687 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u687_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u688_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:702px;
  height:180px;
}
#u688 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:1394px;
  width:702px;
  height:180px;
  display:flex;
}
#u688 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u688_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u689_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u689 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:1127px;
  width:393px;
  height:41px;
  display:flex;
}
#u689 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u689_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u690_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:542px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u690 {
  border-width:0px;
  position:absolute;
  left:599px;
  top:1127px;
  width:542px;
  height:41px;
  display:flex;
}
#u690 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u690_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u691 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:1167px;
  width:393px;
  height:190px;
}
#u692_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:33px;
}
#u692 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u692 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u693_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:33px;
}
#u693 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:0px;
  width:162px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
}
#u693 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u693_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u694_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:33px;
}
#u694 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:0px;
  width:131px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#037DF3;
}
#u694 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u694_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u695_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u695 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:33px;
  width:100px;
  height:34px;
  display:flex;
}
#u695 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u695_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u696_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:34px;
}
#u696 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:33px;
  width:162px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u696 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u696_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u697_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u697 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:33px;
  width:131px;
  height:34px;
  display:flex;
  color:#037DF3;
}
#u697 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u697_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u698_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:31px;
}
#u698 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:67px;
  width:100px;
  height:31px;
  display:flex;
  color:#000000;
}
#u698 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u698_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u699_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:31px;
}
#u699 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:67px;
  width:162px;
  height:31px;
  display:flex;
  color:#D9001B;
}
#u699 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u699_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u700_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
}
#u700 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:67px;
  width:131px;
  height:31px;
  display:flex;
  color:#037DF3;
}
#u700 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u700_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u701_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:32px;
}
#u701 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:98px;
  width:100px;
  height:32px;
  display:flex;
}
#u701 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u701_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u702_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:32px;
}
#u702 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:98px;
  width:162px;
  height:32px;
  display:flex;
  color:#D9001B;
}
#u702 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u702_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u703_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:32px;
}
#u703 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:98px;
  width:131px;
  height:32px;
  display:flex;
  color:#037DF3;
}
#u703 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u703_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u704_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u704 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:130px;
  width:100px;
  height:30px;
  display:flex;
}
#u704 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u704_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u705_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u705 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:130px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u705 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u705_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u706_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u706 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:130px;
  width:131px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u706 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u706_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u707_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u707 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:100px;
  height:30px;
  display:flex;
}
#u707 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u707_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u708_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u708 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:160px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u708 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u708_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u709_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u709 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:160px;
  width:131px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u709 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u709_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u710 {
  border-width:0px;
  position:absolute;
  left:599px;
  top:1166px;
  width:542px;
  height:191px;
}
#u711_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:34px;
}
#u711 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:34px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
}
#u711 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u711_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u712_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:34px;
}
#u712 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:0px;
  width:199px;
  height:34px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#037DF3;
}
#u712 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u713_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:34px;
}
#u713 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:0px;
  width:181px;
  height:34px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#037DF3;
}
#u713 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u713_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u714_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:34px;
}
#u714 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:34px;
  width:162px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u714 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u714_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u715_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:34px;
}
#u715 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:34px;
  width:199px;
  height:34px;
  display:flex;
  color:#037DF3;
}
#u715 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u715_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u716_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:34px;
}
#u716 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:34px;
  width:181px;
  height:34px;
  display:flex;
  color:#037DF3;
}
#u716 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u716_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u717_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:31px;
}
#u717 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:68px;
  width:162px;
  height:31px;
  display:flex;
  color:#D9001B;
}
#u717 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u717_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u718_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:31px;
}
#u718 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:68px;
  width:199px;
  height:31px;
  display:flex;
  color:#037DF3;
}
#u718 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u718_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u719_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:31px;
}
#u719 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:68px;
  width:181px;
  height:31px;
  display:flex;
  color:#037DF3;
}
#u719 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u719_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u720_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:32px;
}
#u720 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:99px;
  width:162px;
  height:32px;
  display:flex;
  color:#D9001B;
}
#u720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u721_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:32px;
}
#u721 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:99px;
  width:199px;
  height:32px;
  display:flex;
  color:#037DF3;
}
#u721 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u721_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u722_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:32px;
}
#u722 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:99px;
  width:181px;
  height:32px;
  display:flex;
  color:#037DF3;
}
#u722 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u722_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u723_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u723 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:131px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u723 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u723_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u724_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:30px;
}
#u724 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:131px;
  width:199px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u724 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u724_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u725_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:30px;
}
#u725 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:131px;
  width:181px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u725 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u725_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u726_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u726 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:161px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u726 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u726_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u727_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:30px;
}
#u727 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:161px;
  width:199px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u727 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u727_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u728_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:30px;
}
#u728 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:161px;
  width:181px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u728 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u728_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u729_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:1199px;
  height:33px;
}
#u729p000 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:1188px;
  height:6px;
}
#u729p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1188px;
  height:6px;
}
#u729p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:0px;
  width:4px;
  height:2px;
}
#u729p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:2px;
}
#u729p002 {
  border-width:0px;
  position:absolute;
  left:1158px;
  top:-16px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u729p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u729 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:2674px;
  width:1183px;
  height:1px;
  display:flex;
}
#u729 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u729_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u730_img {
  border-width:0px;
  position:absolute;
  left:-16px;
  top:-16px;
  width:33px;
  height:941px;
}
#u730p000 {
  border-width:0px;
  position:absolute;
  left:-465px;
  top:459px;
  width:930px;
  height:6px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u730p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:930px;
  height:6px;
}
#u730p001 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:-11px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u730p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u730p002 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:923px;
  width:4px;
  height:4px;
}
#u730p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u730 {
  border-width:0px;
  position:absolute;
  left:279px;
  top:1751px;
  width:1px;
  height:924px;
  display:flex;
}
#u730 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u730_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u731_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u731 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:2692px;
  width:54px;
  height:15px;
  display:flex;
  text-align:center;
}
#u731 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u731_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u732_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u732 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:2576px;
  width:52px;
  height:15px;
  display:flex;
  text-align:center;
}
#u732 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u732_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u733_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u733 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:2242px;
  width:52px;
  height:15px;
  display:flex;
  text-align:center;
}
#u733 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u733_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u734_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u734 {
  border-width:0px;
  position:absolute;
  left:456px;
  top:2692px;
  width:54px;
  height:15px;
  display:flex;
  text-align:center;
}
#u734 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u734_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u735_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u735 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:2036px;
  width:52px;
  height:15px;
  display:flex;
  text-align:center;
}
#u735 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u735_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u736_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u736 {
  border-width:0px;
  position:absolute;
  left:254px;
  top:2693px;
  width:54px;
  height:15px;
  display:flex;
  text-align:center;
}
#u736 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u736_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u737_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u737 {
  border-width:0px;
  position:absolute;
  left:870px;
  top:2692px;
  width:54px;
  height:15px;
  display:flex;
  text-align:center;
}
#u737 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u737_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u738_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
  text-align:center;
}
#u738 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:1719px;
  width:101px;
  height:15px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
  text-align:center;
}
#u738 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u738_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u739_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
  text-align:center;
}
#u739 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:1719px;
  width:101px;
  height:15px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
  text-align:center;
}
#u739 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u739_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u740_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:210px;
  height:33px;
}
#u740p000 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:198px;
  height:6px;
}
#u740p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:6px;
}
#u740p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:0px;
  width:4px;
  height:2px;
}
#u740p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:2px;
}
#u740p002 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:-16px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u740p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u740 {
  border-width:0px;
  position:absolute;
  left:250px;
  top:2792px;
  width:194px;
  height:1px;
  display:flex;
}
#u740 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u740_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u741 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:2435px;
  width:52px;
  height:15px;
  display:flex;
  text-align:center;
}
#u741 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u741_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u742_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:2px;
}
#u742 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:2421px;
  width:209px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-12.0821936882655deg);
  -moz-transform:rotate(-12.0821936882655deg);
  -ms-transform:rotate(-12.0821936882655deg);
  transform:rotate(-12.0821936882655deg);
}
#u742 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u742_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u743_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:34px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u743 {
  border-width:0px;
  position:absolute;
  left:444px;
  top:2776px;
  width:87px;
  height:34px;
  display:flex;
}
#u743 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u743_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u744_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:880px;
}
#u744 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:1796px;
  width:1px;
  height:879px;
  display:flex;
}
#u744 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u745_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u745 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:2394px;
  width:45px;
  height:16px;
  display:flex;
}
#u745 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u745_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u746_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:34px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u746 {
  border-width:0px;
  position:absolute;
  left:640px;
  top:2777px;
  width:87px;
  height:34px;
  display:flex;
}
#u746 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u746_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u747_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:125px;
  height:33px;
}
#u747p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
  width:114px;
  height:6px;
}
#u747p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:6px;
}
#u747p001 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:0px;
  width:4px;
  height:2px;
}
#u747p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:2px;
}
#u747p002 {
  border-width:0px;
  position:absolute;
  left:85px;
  top:-16px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u747p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u747 {
  border-width:0px;
  position:absolute;
  left:531px;
  top:2792px;
  width:109px;
  height:1px;
  display:flex;
}
#u747 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u747_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u748_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:880px;
}
#u748 {
  border-width:0px;
  position:absolute;
  left:678px;
  top:1796px;
  width:1px;
  height:879px;
  display:flex;
}
#u748 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u748_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u749_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:203px;
  height:2px;
}
#u749 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:2393px;
  width:202px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-3.56690900105045deg);
  -moz-transform:rotate(-3.56690900105045deg);
  -ms-transform:rotate(-3.56690900105045deg);
  transform:rotate(-3.56690900105045deg);
}
#u749 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u750_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:2px;
}
#u750 {
  border-width:0px;
  position:absolute;
  left:278px;
  top:2249px;
  width:203px;
  height:1px;
  display:flex;
}
#u750 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u751_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u751 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:2234px;
  width:42px;
  height:16px;
  display:flex;
}
#u751 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u751_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u752_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:178px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
  text-align:center;
}
#u752 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:1719px;
  width:178px;
  height:15px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
  text-align:center;
}
#u752 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u752_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u753_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u753 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:2411px;
  width:200px;
  height:48px;
  display:flex;
}
#u753 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u753_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u754_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:231px;
  height:33px;
}
#u754p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-3px;
  width:220px;
  height:6px;
}
#u754p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:6px;
}
#u754p001 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:4px;
  height:4px;
}
#u754p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u754p002 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:-17px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u754p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u754 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:2849px;
  width:215px;
  height:1px;
  display:flex;
}
#u754 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u754_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u755_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:34px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u755 {
  border-width:0px;
  position:absolute;
  left:640px;
  top:2833px;
  width:87px;
  height:34px;
  display:flex;
}
#u755 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u755_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u756_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:34px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u756 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:2833px;
  width:103px;
  height:34px;
  display:flex;
}
#u756 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u757_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:144px;
  height:33px;
}
#u757p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-3px;
  width:134px;
  height:6px;
}
#u757p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:6px;
}
#u757p001 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:4px;
  height:4px;
}
#u757p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u757p002 {
  border-width:0px;
  position:absolute;
  left:103px;
  top:-17px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u757p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u757 {
  border-width:0px;
  position:absolute;
  left:727px;
  top:2849px;
  width:128px;
  height:1px;
  display:flex;
}
#u757 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u757_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u758_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u758 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:2775px;
  width:100px;
  height:35px;
  display:flex;
}
#u758 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u758_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u759_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u759 {
  border-width:0px;
  position:absolute;
  left:325px;
  top:2832px;
  width:100px;
  height:35px;
  display:flex;
}
#u759 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u760_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u760 {
  border-width:0px;
  position:absolute;
  left:563px;
  top:2372px;
  width:45px;
  height:16px;
  display:flex;
}
#u760 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u760_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u761_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:880px;
}
#u761 {
  border-width:0px;
  position:absolute;
  left:896px;
  top:1796px;
  width:1px;
  height:879px;
  display:flex;
}
#u761 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u762_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:2px;
}
#u762 {
  border-width:0px;
  position:absolute;
  left:678px;
  top:2294px;
  width:219px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(9.4770990752519deg);
  -moz-transform:rotate(9.4770990752519deg);
  -ms-transform:rotate(9.4770990752519deg);
  transform:rotate(9.4770990752519deg);
}
#u762 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u762_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u763_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u763 {
  border-width:0px;
  position:absolute;
  left:770px;
  top:2269px;
  width:42px;
  height:16px;
  display:flex;
}
#u763 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u763_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u764_img {
  border-width:0px;
  position:absolute;
  left:-16px;
  top:-16px;
  width:33px;
  height:385px;
}
#u764p000 {
  border-width:0px;
  position:absolute;
  left:-187px;
  top:181px;
  width:374px;
  height:6px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u764p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:374px;
  height:6px;
}
#u764p001 {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:-11px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u764p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u764p002 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:367px;
  width:4px;
  height:4px;
}
#u764p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u764 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:2675px;
  width:1px;
  height:368px;
  display:flex;
}
#u764 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u765_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  text-align:center;
}
#u765 {
  border-width:0px;
  position:absolute;
  left:579px;
  top:2722px;
  width:80px;
  height:15px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  text-align:center;
}
#u765 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u765_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u766_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:2px;
}
#u766 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:2263px;
  width:197px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(7.67367492810227deg);
  -moz-transform:rotate(7.67367492810227deg);
  -ms-transform:rotate(7.67367492810227deg);
  transform:rotate(7.67367492810227deg);
}
#u766 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u767_img {
  border-width:0px;
  position:absolute;
  left:-16px;
  top:-16px;
  width:33px;
  height:173px;
}
#u767p000 {
  border-width:0px;
  position:absolute;
  left:-80px;
  top:76px;
  width:162px;
  height:4px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u767p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:4px;
}
#u767p001 {
  border-width:0px;
  position:absolute;
  left:-18px;
  top:-10px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u767p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u767p002 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:154px;
  width:2px;
  height:4px;
}
#u767p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:4px;
}
#u767 {
  border-width:0px;
  position:absolute;
  left:374px;
  top:2676px;
  width:1px;
  height:156px;
  display:flex;
}
#u767 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u768_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  text-align:center;
}
#u768 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:2722px;
  width:54px;
  height:15px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  text-align:center;
}
#u768 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u768_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u769_img {
  border-width:0px;
  position:absolute;
  left:-16px;
  top:-16px;
  width:33px;
  height:118px;
}
#u769p000 {
  border-width:0px;
  position:absolute;
  left:-52px;
  top:48px;
  width:106px;
  height:6px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u769p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:6px;
}
#u769p001 {
  border-width:0px;
  position:absolute;
  left:-18px;
  top:-10px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u769p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u769p002 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:2px;
  height:4px;
}
#u769p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:4px;
}
#u769 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:2674px;
  width:1px;
  height:101px;
  display:flex;
}
#u769 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u769_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u770_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  text-align:center;
}
#u770 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:2717px;
  width:54px;
  height:15px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  text-align:center;
}
#u770 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u770_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u771_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u771 {
  border-width:0px;
  position:absolute;
  left:281px;
  top:2065px;
  width:200px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(9.66137053015315deg);
  -moz-transform:rotate(9.66137053015315deg);
  -ms-transform:rotate(9.66137053015315deg);
  transform:rotate(9.66137053015315deg);
}
#u771 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u772_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u772 {
  border-width:0px;
  position:absolute;
  left:325px;
  top:2977px;
  width:100px;
  height:35px;
  display:flex;
}
#u772 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u772_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u773_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:2px;
}
#u773 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:2584px;
  width:201px;
  height:1px;
  display:flex;
}
#u773 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u773_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u774_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u774 {
  border-width:0px;
  position:absolute;
  left:516px;
  top:2895px;
  width:100px;
  height:35px;
  display:flex;
}
#u774 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u774_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u775_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:34px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u775 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:2895px;
  width:103px;
  height:34px;
  display:flex;
}
#u775 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u775_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u776_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:34px;
  background:inherit;
  background-color:rgba(245, 154, 35, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u776 {
  border-width:0px;
  position:absolute;
  left:1086px;
  top:2896px;
  width:103px;
  height:34px;
  display:flex;
}
#u776 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u776_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u777_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u777 {
  border-width:0px;
  position:absolute;
  left:1092px;
  top:2692px;
  width:54px;
  height:15px;
  display:flex;
  text-align:center;
}
#u777 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u777_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u778_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:255px;
  height:33px;
}
#u778p000 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-3px;
  width:244px;
  height:6px;
}
#u778p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:244px;
  height:6px;
}
#u778p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-1px;
  width:4px;
  height:4px;
}
#u778p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u778p002 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:-17px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u778p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u778 {
  border-width:0px;
  position:absolute;
  left:616px;
  top:2913px;
  width:239px;
  height:1px;
  display:flex;
}
#u778 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u778_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u779_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:144px;
  height:33px;
}
#u779p000 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-3px;
  width:132px;
  height:6px;
}
#u779p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:6px;
}
#u779p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-1px;
  width:4px;
  height:4px;
}
#u779p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u779p002 {
  border-width:0px;
  position:absolute;
  left:104px;
  top:-17px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u779p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u779 {
  border-width:0px;
  position:absolute;
  left:958px;
  top:2911px;
  width:128px;
  height:1px;
  display:flex;
}
#u779 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u779_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u780_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:222px;
  height:2px;
}
#u780 {
  border-width:0px;
  position:absolute;
  left:676px;
  top:2387px;
  width:221px;
  height:1px;
  display:flex;
}
#u780 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u780_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u781_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u781 {
  border-width:0px;
  position:absolute;
  left:770px;
  top:2368px;
  width:30px;
  height:16px;
  display:flex;
}
#u781 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u781_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u782_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:222px;
  height:2px;
}
#u782 {
  border-width:0px;
  position:absolute;
  left:893px;
  top:2416px;
  width:221px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(14.7648574215114deg);
  -moz-transform:rotate(14.7648574215114deg);
  -ms-transform:rotate(14.7648574215114deg);
  transform:rotate(14.7648574215114deg);
}
#u782 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u783_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:880px;
}
#u783 {
  border-width:0px;
  position:absolute;
  left:1111px;
  top:1796px;
  width:1px;
  height:879px;
  display:flex;
}
#u783 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u783_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u784_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u784 {
  border-width:0px;
  position:absolute;
  left:996px;
  top:2388px;
  width:42px;
  height:16px;
  display:flex;
}
#u784 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u784_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u785_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u785 {
  border-width:0px;
  position:absolute;
  left:918px;
  top:2427px;
  width:200px;
  height:32px;
  display:flex;
}
#u785 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u785_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u786_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u786 {
  border-width:0px;
  position:absolute;
  left:697px;
  top:2399px;
  width:200px;
  height:48px;
  display:flex;
}
#u786 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u786_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u787_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:2px;
}
#u787 {
  border-width:0px;
  position:absolute;
  left:893px;
  top:2330px;
  width:219px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(9.4770990752519deg);
  -moz-transform:rotate(9.4770990752519deg);
  -ms-transform:rotate(9.4770990752519deg);
  transform:rotate(9.4770990752519deg);
}
#u787 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u788_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u788 {
  border-width:0px;
  position:absolute;
  left:982px;
  top:2301px;
  width:42px;
  height:16px;
  display:flex;
}
#u788 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u788_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u789_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u789 {
  border-width:0px;
  position:absolute;
  left:516px;
  top:3043px;
  width:100px;
  height:35px;
  display:flex;
}
#u789 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u790_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:199px;
  height:2px;
}
#u790 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:2584px;
  width:198px;
  height:1px;
  display:flex;
}
#u790 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u791_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:34px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u791 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:2977px;
  width:87px;
  height:34px;
  display:flex;
}
#u791 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u792_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:222px;
  height:33px;
}
#u792p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-3px;
  width:212px;
  height:6px;
}
#u792p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:212px;
  height:6px;
}
#u792p001 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:4px;
  height:4px;
}
#u792p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u792p002 {
  border-width:0px;
  position:absolute;
  left:181px;
  top:-17px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u792p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u792 {
  border-width:0px;
  position:absolute;
  left:429px;
  top:2993px;
  width:206px;
  height:1px;
  display:flex;
}
#u792 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u792_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u793_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u793 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:2043px;
  width:42px;
  height:16px;
  display:flex;
}
#u793 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u793_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u794_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:207px;
  height:2px;
}
#u794 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:2097px;
  width:206px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(8.44752724790847deg);
  -moz-transform:rotate(8.44752724790847deg);
  -ms-transform:rotate(8.44752724790847deg);
  transform:rotate(8.44752724790847deg);
}
#u794 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u795_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:80px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u795 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:2126px;
  width:162px;
  height:80px;
  display:flex;
}
#u795 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u795_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u796_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u796 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:2071px;
  width:42px;
  height:16px;
  display:flex;
}
#u796 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u796_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u797_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:433px;
  height:2px;
}
#u797 {
  border-width:0px;
  position:absolute;
  left:679px;
  top:2584px;
  width:432px;
  height:1px;
  display:flex;
}
#u797 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u798_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:88px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
  text-align:center;
}
#u798 {
  border-width:0px;
  position:absolute;
  left:639px;
  top:1719px;
  width:88px;
  height:15px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
  text-align:center;
}
#u798 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u798_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u799_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:34px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u799 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:3043px;
  width:103px;
  height:34px;
  display:flex;
}
#u799 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u800_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:255px;
  height:33px;
}
#u800p000 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-3px;
  width:244px;
  height:6px;
}
#u800p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:244px;
  height:6px;
}
#u800p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-1px;
  width:4px;
  height:4px;
}
#u800p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u800p002 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:-17px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u800p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u800 {
  border-width:0px;
  position:absolute;
  left:616px;
  top:3059px;
  width:239px;
  height:1px;
  display:flex;
}
#u800 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u801_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:222px;
  height:2px;
}
#u801 {
  border-width:0px;
  position:absolute;
  left:677px;
  top:2125px;
  width:221px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(6.5560242338545deg);
  -moz-transform:rotate(6.5560242338545deg);
  -ms-transform:rotate(6.5560242338545deg);
  transform:rotate(6.5560242338545deg);
}
#u801 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u802_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u802 {
  border-width:0px;
  position:absolute;
  left:776px;
  top:2104px;
  width:42px;
  height:16px;
  display:flex;
}
#u802 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u802_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u803_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:80px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u803 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:2153px;
  width:200px;
  height:80px;
  display:flex;
}
#u803 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u803_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u804_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u804 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:2444px;
  width:162px;
  height:32px;
  display:flex;
}
#u804 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u804_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u805_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u805 {
  border-width:0px;
  position:absolute;
  left:497px;
  top:2269px;
  width:162px;
  height:48px;
  display:flex;
}
#u805 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u805_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u806_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u806 {
  border-width:0px;
  position:absolute;
  left:710px;
  top:2296px;
  width:162px;
  height:48px;
  display:flex;
}
#u806 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u806_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u807_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u807 {
  border-width:0px;
  position:absolute;
  left:938px;
  top:2325px;
  width:158px;
  height:32px;
  display:flex;
}
#u807 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u807_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u808_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:217px;
  height:2px;
}
#u808 {
  border-width:0px;
  position:absolute;
  left:896px;
  top:2138px;
  width:216px;
  height:1px;
  display:flex;
}
#u808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u809_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u809 {
  border-width:0px;
  position:absolute;
  left:1004px;
  top:2118px;
  width:30px;
  height:16px;
  display:flex;
}
#u809 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u809_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u810_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u810 {
  border-width:0px;
  position:absolute;
  left:918px;
  top:2142px;
  width:210px;
  height:48px;
  display:flex;
}
#u810 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u810_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u811_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u811 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:1926px;
  width:52px;
  height:15px;
  display:flex;
  text-align:center;
}
#u811 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u811_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u812_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u812 {
  border-width:0px;
  position:absolute;
  left:280px;
  top:1933px;
  width:200px;
  height:1px;
  display:flex;
}
#u812 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u812_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u813_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u813 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:1945px;
  width:200px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(6.8115379990648deg);
  -moz-transform:rotate(6.8115379990648deg);
  -ms-transform:rotate(6.8115379990648deg);
  transform:rotate(6.8115379990648deg);
}
#u813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u814_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u814 {
  border-width:0px;
  position:absolute;
  left:554px;
  top:1926px;
  width:42px;
  height:16px;
  display:flex;
}
#u814 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u814_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u815_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u815 {
  border-width:0px;
  position:absolute;
  left:497px;
  top:1952px;
  width:162px;
  height:48px;
  display:flex;
}
#u815 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u815_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u816_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u816 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:1984px;
  width:162px;
  height:48px;
  display:flex;
}
#u816 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u816_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u817_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:2px;
}
#u817 {
  border-width:0px;
  position:absolute;
  left:674px;
  top:1972px;
  width:220px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(7.58106985132206deg);
  -moz-transform:rotate(7.58106985132206deg);
  -ms-transform:rotate(7.58106985132206deg);
  transform:rotate(7.58106985132206deg);
}
#u817 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u818_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u818 {
  border-width:0px;
  position:absolute;
  left:763px;
  top:1948px;
  width:42px;
  height:16px;
  display:flex;
}
#u818 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u818_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u819_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:2px;
}
#u819 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:2001px;
  width:220px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(7.58106985132206deg);
  -moz-transform:rotate(7.58106985132206deg);
  -ms-transform:rotate(7.58106985132206deg);
  transform:rotate(7.58106985132206deg);
}
#u819 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u819_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u820_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u820 {
  border-width:0px;
  position:absolute;
  left:986px;
  top:1979px;
  width:42px;
  height:16px;
  display:flex;
}
#u820 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u820_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u821_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u821 {
  border-width:0px;
  position:absolute;
  left:918px;
  top:2002px;
  width:158px;
  height:32px;
  display:flex;
}
#u821 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u821_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u822_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#F59A23;
  text-align:center;
}
#u822 {
  border-width:0px;
  position:absolute;
  left:164px;
  top:1947px;
  width:104px;
  height:15px;
  display:flex;
  color:#F59A23;
  text-align:center;
}
#u822 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u822_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
  text-align:center;
}
#u823 {
  border-width:0px;
  position:absolute;
  left:230px;
  top:3155px;
  width:39px;
  height:15px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
  text-align:center;
}
#u823 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u823_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u824_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u824 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:3267px;
  width:132px;
  height:35px;
  display:flex;
}
#u824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u825 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:3176px;
  width:132px;
  height:35px;
  display:flex;
}
#u825 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u825_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u826_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:72px;
  height:33px;
}
#u826p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
  width:62px;
  height:4px;
}
#u826p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:4px;
}
#u826p001 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-2px;
  width:2px;
  height:4px;
  -webkit-transform:rotate(-90deg);
  -moz-transform:rotate(-90deg);
  -ms-transform:rotate(-90deg);
  transform:rotate(-90deg);
}
#u826p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:4px;
}
#u826p002 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:-16px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u826p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u826 {
  border-width:0px;
  position:absolute;
  left:547px;
  top:3239px;
  width:56px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u827_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u827 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:3348px;
  width:100px;
  height:35px;
  display:flex;
}
#u827 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u828_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:319px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u828 {
  border-width:0px;
  position:absolute;
  left:705px;
  top:3348px;
  width:319px;
  height:35px;
  display:flex;
}
#u828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u829_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u829 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:3461px;
  width:129px;
  height:37px;
  display:flex;
}
#u829 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u830_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u830 {
  border-width:0px;
  position:absolute;
  left:705px;
  top:3461px;
  width:129px;
  height:37px;
  display:flex;
}
#u830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u831_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u831 {
  border-width:0px;
  position:absolute;
  left:918px;
  top:3461px;
  width:129px;
  height:37px;
  display:flex;
}
#u831 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u831_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u832_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u832 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:3461px;
  width:129px;
  height:37px;
  display:flex;
}
#u832 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u832_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u833_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u833 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:3346px;
  width:143px;
  height:37px;
  display:flex;
}
#u833 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u834 {
  border-width:0px;
  position:absolute;
  left:1260px;
  top:3346px;
  width:208px;
  height:37px;
  display:flex;
}
#u834 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:208px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u835 {
  border-width:0px;
  position:absolute;
  left:1269px;
  top:3312px;
  width:208px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u835 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u836_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:156px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u836 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:3304px;
  width:156px;
  height:32px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u836 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u837_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:70px;
  height:33px;
}
#u837p000 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-3px;
  width:58px;
  height:6px;
}
#u837p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:6px;
}
#u837p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-1px;
  width:4px;
  height:2px;
  -webkit-transform:rotate(-180deg);
  -moz-transform:rotate(-180deg);
  -ms-transform:rotate(-180deg);
  transform:rotate(-180deg);
}
#u837p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:2px;
}
#u837p002 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:-17px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(-180deg);
  -moz-transform:rotate(-180deg);
  -ms-transform:rotate(-180deg);
  transform:rotate(-180deg);
}
#u837p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u837 {
  border-width:0px;
  position:absolute;
  left:1024px;
  top:3364px;
  width:54px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u837 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u838 {
  border-width:0px;
  position:absolute;
  left:1224px;
  top:3346px;
  width:34px;
  height:37px;
  display:flex;
}
#u838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:156px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u839 {
  border-width:0px;
  position:absolute;
  left:585px;
  top:3226px;
  width:156px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u839 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u840_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u840 {
  border-width:0px;
  position:absolute;
  left:1092px;
  top:3461px;
  width:129px;
  height:37px;
  display:flex;
}
#u840 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u841_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:504px;
  height:240px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u841 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:3059px;
  width:504px;
  height:240px;
  display:flex;
}
#u841 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u842_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:160px;
  height:33px;
}
#u842p000 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:148px;
  height:6px;
  -webkit-transform:rotate(0.0843641748410278deg);
  -moz-transform:rotate(0.0843641748410278deg);
  -ms-transform:rotate(0.0843641748410278deg);
  transform:rotate(0.0843641748410278deg);
}
#u842p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:6px;
}
#u842p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-3px;
  width:4px;
  height:4px;
  -webkit-transform:rotate(-159.915635825159deg);
  -moz-transform:rotate(-159.915635825159deg);
  -ms-transform:rotate(-159.915635825159deg);
  transform:rotate(-159.915635825159deg);
}
#u842p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u842p002 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:-17px;
  width:36px;
  height:36px;
  -webkit-transform:rotate(-179.915635825159deg);
  -moz-transform:rotate(-179.915635825159deg);
  -ms-transform:rotate(-179.915635825159deg);
  transform:rotate(-179.915635825159deg);
}
#u842p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:36px;
}
#u842 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:3327px;
  width:144px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(159.915635825159deg);
  -moz-transform:rotate(159.915635825159deg);
  -ms-transform:rotate(159.915635825159deg);
  transform:rotate(159.915635825159deg);
}
#u842 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u843_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:182px;
  height:33px;
}
#u843p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-3px;
  width:172px;
  height:6px;
  -webkit-transform:rotate(-0.408990984351167deg);
  -moz-transform:rotate(-0.408990984351167deg);
  -ms-transform:rotate(-0.408990984351167deg);
  transform:rotate(-0.408990984351167deg);
}
#u843p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:6px;
}
#u843p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-1px;
  width:4px;
  height:4px;
  -webkit-transform:rotate(-16.4089909843512deg);
  -moz-transform:rotate(-16.4089909843512deg);
  -ms-transform:rotate(-16.4089909843512deg);
  transform:rotate(-16.4089909843512deg);
}
#u843p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u843p002 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:-17px;
  width:38px;
  height:36px;
  -webkit-transform:rotate(-180.408990984351deg);
  -moz-transform:rotate(-180.408990984351deg);
  -ms-transform:rotate(-180.408990984351deg);
  transform:rotate(-180.408990984351deg);
}
#u843p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:36px;
}
#u843 {
  border-width:0px;
  position:absolute;
  left:595px;
  top:3325px;
  width:166px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(16.4089909843512deg);
  -moz-transform:rotate(16.4089909843512deg);
  -ms-transform:rotate(16.4089909843512deg);
  transform:rotate(16.4089909843512deg);
}
#u843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:88px;
  height:33px;
}
#u844p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-1px;
  width:78px;
  height:4px;
}
#u844p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:4px;
}
#u844p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:4px;
  height:4px;
  -webkit-transform:rotate(-90deg);
  -moz-transform:rotate(-90deg);
  -ms-transform:rotate(-90deg);
  transform:rotate(-90deg);
}
#u844p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u844p002 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:-17px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u844p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u844 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:3419px;
  width:72px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u845_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:88px;
  height:33px;
}
#u845p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
  width:78px;
  height:4px;
}
#u845p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:4px;
}
#u845p001 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-2px;
  width:2px;
  height:4px;
  -webkit-transform:rotate(-90deg);
  -moz-transform:rotate(-90deg);
  -ms-transform:rotate(-90deg);
  transform:rotate(-90deg);
}
#u845p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:4px;
}
#u845p002 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:-16px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u845p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u845 {
  border-width:0px;
  position:absolute;
  left:819px;
  top:3419px;
  width:72px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u846_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:88px;
  height:33px;
}
#u846p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
  width:78px;
  height:6px;
}
#u846p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:6px;
}
#u846p001 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-2px;
  width:4px;
  height:4px;
  -webkit-transform:rotate(-90deg);
  -moz-transform:rotate(-90deg);
  -ms-transform:rotate(-90deg);
  transform:rotate(-90deg);
}
#u846p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u846p002 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:-17px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u846p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u846 {
  border-width:0px;
  position:absolute;
  left:564px;
  top:3558px;
  width:72px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u847 {
  border-width:0px;
  position:absolute;
  left:325px;
  top:3602px;
  width:524px;
  height:194px;
}
#u848_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:33px;
}
#u848 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u849_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:33px;
}
#u849 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:0px;
  width:162px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
}
#u849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u850_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:33px;
}
#u850 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:0px;
  width:131px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
}
#u850 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u851_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:33px;
}
#u851 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:0px;
  width:131px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
}
#u851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u852 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:33px;
  width:100px;
  height:34px;
  display:flex;
}
#u852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u853_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:34px;
}
#u853 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:33px;
  width:162px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u854_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u854 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:33px;
  width:131px;
  height:34px;
  display:flex;
  color:#037DF3;
}
#u854 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u855_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u855 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:33px;
  width:131px;
  height:34px;
  display:flex;
  color:#037DF3;
}
#u855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u856_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u856 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:67px;
  width:100px;
  height:34px;
  display:flex;
}
#u856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u857_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:34px;
}
#u857 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:67px;
  width:162px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u858_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u858 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:67px;
  width:131px;
  height:34px;
  display:flex;
  color:#037DF3;
}
#u858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u859_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u859 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:67px;
  width:131px;
  height:34px;
  display:flex;
  color:#037DF3;
}
#u859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u860_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:31px;
}
#u860 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:101px;
  width:100px;
  height:31px;
  display:flex;
  color:#000000;
}
#u860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u861_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:31px;
}
#u861 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:101px;
  width:162px;
  height:31px;
  display:flex;
  color:#D9001B;
}
#u861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u862_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
}
#u862 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:101px;
  width:131px;
  height:31px;
  display:flex;
  color:#037DF3;
}
#u862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u863_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
}
#u863 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:101px;
  width:131px;
  height:31px;
  display:flex;
  color:#037DF3;
}
#u863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u864_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:32px;
}
#u864 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:132px;
  width:100px;
  height:32px;
  display:flex;
}
#u864 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u864_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u865_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:32px;
}
#u865 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:132px;
  width:162px;
  height:32px;
  display:flex;
  color:#D9001B;
}
#u865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u866_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:32px;
}
#u866 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:132px;
  width:131px;
  height:32px;
  display:flex;
  color:#037DF3;
}
#u866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u867_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:32px;
}
#u867 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:132px;
  width:131px;
  height:32px;
  display:flex;
  color:#037DF3;
}
#u867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u868_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u868 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:164px;
  width:100px;
  height:30px;
  display:flex;
}
#u868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u869 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:164px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u870_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u870 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:164px;
  width:131px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u871_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u871 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:164px;
  width:131px;
  height:30px;
  display:flex;
  color:#037DF3;
}
#u871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u872 {
  border-width:0px;
  position:absolute;
  left:619px;
  top:3540px;
  width:251px;
  height:37px;
  display:flex;
}
#u872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u873_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:786px;
  height:176px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u873 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:79px;
  width:786px;
  height:176px;
  display:flex;
  font-size:18px;
}
#u873 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u874_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u874 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:49px;
  width:28px;
  height:16px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u874 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u874_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u875_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:88px;
  height:33px;
}
#u875p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-1px;
  width:78px;
  height:4px;
}
#u875p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:4px;
}
#u875p001 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:4px;
  height:4px;
  -webkit-transform:rotate(-90deg);
  -moz-transform:rotate(-90deg);
  -ms-transform:rotate(-90deg);
  transform:rotate(-90deg);
}
#u875p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:4px;
}
#u875p002 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:-17px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u875p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u875 {
  border-width:0px;
  position:absolute;
  left:550px;
  top:3845px;
  width:72px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u876_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u876 {
  border-width:0px;
  position:absolute;
  left:605px;
  top:3827px;
  width:251px;
  height:37px;
  display:flex;
}
#u876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u877 {
  border-width:0px;
  position:absolute;
  left:325px;
  top:3888px;
  width:524px;
  height:193px;
}
#u878_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:33px;
}
#u878 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:33px;
}
#u879 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:0px;
  width:162px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
}
#u879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u880_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:33px;
}
#u880 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:0px;
  width:131px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
}
#u880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u881_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:33px;
}
#u881 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:0px;
  width:131px;
  height:33px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  color:#D9001B;
}
#u881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u882_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u882 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:33px;
  width:100px;
  height:34px;
  display:flex;
}
#u882 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u882_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u883_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:34px;
}
#u883 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:33px;
  width:162px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u884_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u884 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:33px;
  width:131px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u885_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u885 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:33px;
  width:131px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u886_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u886 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:67px;
  width:100px;
  height:34px;
  display:flex;
}
#u886 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:34px;
}
#u887 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:67px;
  width:162px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u888_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u888 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:67px;
  width:131px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:34px;
}
#u889 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:67px;
  width:131px;
  height:34px;
  display:flex;
  color:#D9001B;
}
#u889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u890 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:101px;
  width:100px;
  height:30px;
  display:flex;
}
#u890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u891 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:101px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u892_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u892 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:101px;
  width:131px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u892 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u893_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u893 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:101px;
  width:131px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u893 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:32px;
}
#u894 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:131px;
  width:100px;
  height:32px;
  display:flex;
}
#u894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u895_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:32px;
}
#u895 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:131px;
  width:162px;
  height:32px;
  display:flex;
  color:#D9001B;
}
#u895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u896_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:32px;
}
#u896 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:131px;
  width:131px;
  height:32px;
  display:flex;
  color:#D9001B;
}
#u896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u897_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:32px;
}
#u897 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:131px;
  width:131px;
  height:32px;
  display:flex;
  color:#D9001B;
}
#u897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u898_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u898 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:163px;
  width:100px;
  height:30px;
  display:flex;
}
#u898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u899_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:30px;
}
#u899 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:163px;
  width:162px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u900_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u900 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:163px;
  width:131px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u900 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u901_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:30px;
}
#u901 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:163px;
  width:131px;
  height:30px;
  display:flex;
  color:#D9001B;
}
#u901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u902_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-16px;
  width:88px;
  height:33px;
}
#u902p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-3px;
  width:78px;
  height:6px;
}
#u902p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:6px;
}
#u902p001 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:-2px;
  width:2px;
  height:4px;
  -webkit-transform:rotate(-90deg);
  -moz-transform:rotate(-90deg);
  -ms-transform:rotate(-90deg);
  transform:rotate(-90deg);
}
#u902p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:4px;
}
#u902p002 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:-16px;
  width:36px;
  height:34px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u902p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:34px;
}
#u902 {
  border-width:0px;
  position:absolute;
  left:549px;
  top:4130px;
  width:72px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u903_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u903 {
  border-width:0px;
  position:absolute;
  left:604px;
  top:4112px;
  width:251px;
  height:37px;
  display:flex;
}
#u903 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u903_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u904_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:524px;
  height:170px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u904 {
  border-width:0px;
  position:absolute;
  left:325px;
  top:4193px;
  width:524px;
  height:170px;
  display:flex;
}
#u904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u905_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u905 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:50px;
  width:87px;
  height:15px;
  display:flex;
}
#u905 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u905_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u906_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:129px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u906 {
  border-width:0px;
  position:absolute;
  left:230px;
  top:3461px;
  width:129px;
  height:37px;
  display:flex;
}
#u906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
