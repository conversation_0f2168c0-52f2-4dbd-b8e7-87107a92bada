<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>pagemargin</title>
<link href="../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer></script>
<link href="../../images/logo-icon.svg">
<link href="../../styles/stylesheet.css" rel="Stylesheet"></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../index.html">
                    quarkdown
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">1.6.3
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":quarkdown-stdlib/main">jvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":quarkdown-stdlib/main" data-filter=":quarkdown-stdlib/main">
                                <span class="checkbox--icon"></span>
                                jvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    quarkdown
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="member" id="content" pageids="quarkdown-stdlib::com.quarkdown.stdlib//pageMarginContent/#com.quarkdown.core.document.layout.page.PageMarginPosition#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071">
  <div class="breadcrumbs"><a href="../index.html">quarkdown-stdlib</a><span class="delimiter">/</span><a href="index.html">com.quarkdown.stdlib.module.Document</a><span class="delimiter">/</span><span class="current">pagemargin</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>pagemargin</span></span></h1>
  </div>
  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">pagemargin</span><span class="token punctuation"> </span><span class="token constant">position</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition///PointingToDeclaration/">PageMarginPosition</span><span class="token operator"> = </span>PageMarginPosition.TOP_CENTER<span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">content</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><p class="paragraph">Displays text content on each page of a document.</p><span class="kdoc-tag"><h4 class="">Return</h4><p class="paragraph">a wrapped <span data-unresolved-link="com.quarkdown.core.ast.quarkdown.invisible/PageMarginContentInitializer///PointingToDeclaration/">PageMarginContentInitializer</span> node</p></span><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>position</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li></ul></dl><p class="paragraph">position of the content within the page</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.TOP_LEFT_CORNER///PointingToDeclaration/"><code class="lang-kotlin">topleftcorner</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.TOP_LEFT///PointingToDeclaration/"><code class="lang-kotlin">topleft</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.TOP_CENTER///PointingToDeclaration/"><code class="lang-kotlin">topcenter</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.TOP_RIGHT///PointingToDeclaration/"><code class="lang-kotlin">topright</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.TOP_RIGHT_CORNER///PointingToDeclaration/"><code class="lang-kotlin">toprightcorner</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.RIGHT_TOP///PointingToDeclaration/"><code class="lang-kotlin">righttop</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.RIGHT_MIDDLE///PointingToDeclaration/"><code class="lang-kotlin">rightmiddle</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.RIGHT_BOTTOM///PointingToDeclaration/"><code class="lang-kotlin">rightbottom</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.BOTTOM_RIGHT_CORNER///PointingToDeclaration/"><code class="lang-kotlin">bottomrightcorner</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.BOTTOM_RIGHT///PointingToDeclaration/"><code class="lang-kotlin">bottomright</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.BOTTOM_CENTER///PointingToDeclaration/"><code class="lang-kotlin">bottomcenter</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.BOTTOM_LEFT///PointingToDeclaration/"><code class="lang-kotlin">bottomleft</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.BOTTOM_LEFT_CORNER///PointingToDeclaration/"><code class="lang-kotlin">bottomleftcorner</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.LEFT_BOTTOM///PointingToDeclaration/"><code class="lang-kotlin">leftbottom</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.LEFT_MIDDLE///PointingToDeclaration/"><code class="lang-kotlin">leftmiddle</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageMarginPosition.LEFT_TOP///PointingToDeclaration/"><code class="lang-kotlin">lefttop</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>content</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Likely a <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call#block-vs-inline-function-calls">body argument</a></li></ul></dl><p class="paragraph">content to be displayed on each page</p></div></div></div></div></div><span class="kdoc-tag"><h4 class="kdoctag">Wiki page</h4><a href="https://github.com/iamgio/quarkdown/wiki/Page+margin+content">Page margin content</a></span></div></div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Quarkdown</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
