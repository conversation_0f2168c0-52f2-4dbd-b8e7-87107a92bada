<execution domain="prompt-engineering">
  <process>
    # 提示词开发流程
    
    ```mermaid
    flowchart TD
      A[需求分析] --> B[协议选择]
      B --> C[结构设计]
      C --> D[内容编写]
      D --> E[语法验证]
      E --> F{是否有效?}
      F -->|是| G[功能测试]
      F -->|否| D
      G --> H{是否满足需求?}
      H -->|是| I[发布使用]
      H -->|否| C
      
      %% 异常处理路径
      E --> E1[语法错误处理]
      E1 --> D
      G --> G1[功能缺陷处理]
      G1 --> D
    ```
    
    ## 核心执行步骤
    
    1. **需求分析**：明确提示词的目标、受众和使用场景
    2. **协议选择**：根据需求选择合适的DPML协议组合
    3. **结构设计**：设计标签层次和组件关系
    4. **内容编写**：实现具体的提示词内容
    5. **验证测试**：确保语法正确性和功能符合预期
  </process>
  
  <guideline>
    # 提示词设计指南
    
    - 使用直观的图形表达复杂概念和关系
    - 分离说明性内容和指令性内容，增强可理解性
    - 关键指令使用醒目格式，确保不被忽略
    - 按逻辑顺序组织内容，保持思路流畅
    - 使用一致的术语和格式，避免混淆
    
    ## 模块化设计建议
    
    ```mermaid
    mindmap
      root((模块化设计))
        按功能分解
          基础定义模块
          处理逻辑模块
          交互规则模块
        复用策略
          通用组件抽取
          标准模式引用
          条件性组合
        版本管理
          兼容性规划
          增量更新
    ```
  </guideline>
  
  <rule>
    # 必须遵循的规则
    
    1. 资源处理必须遵循标准协议（如`@execution://deal-at-reference`）
    2. 所有XML标签必须正确嵌套和闭合
    3. 协议实现绑定必须使用正确的A:B语法
    4. 每个标签的语义必须明确，不存在歧义
    5. 资源引用必须使用正确的协议和路径格式
    6. 复杂提示词必须提供错误处理机制
    7. 标签必须按照协议定义的层次结构使用
  </rule>
  
  <constraint>
    # 限制条件
    
    ```mermaid
    graph TD
      A[技术约束] --> B[AI系统支持的标签种类]
      A --> C[资源大小限制]
      A --> D[嵌套深度限制]
      
      E[语义约束] --> F[指令逻辑一致性]
      E --> G[跨协议兼容性]
      
      H[使用约束] --> I[目标用户理解能力]
      H --> J[执行环境限制]
    ```
    
    - 标签嵌套不应超过5层，避免复杂度过高
    - 单个提示词文件不应超过10KB，保证加载效率
    - 资源引用链不应形成循环依赖
    - 协议组合必须保持语义一致性
  </constraint>
  
  <criteria>
    # 提示词质量评估标准
    
    | 指标 | 优秀 | 合格 | 不合格 |
    |------|------|------|--------|
    | 语法正确性 | 完全符合DPML规范 | 轻微格式问题 | 存在标签错误 |
    | 语义清晰度 | 指令明确无歧义 | 部分表达不够精确 | 存在明显歧义 |
    | 结构合理性 | 层次清晰逻辑连贯 | 结构基本合理 | 结构混乱或不合理 |
    | 资源处理 | 正确处理所有资源引用 | 基本正确但有小缺陷 | 资源处理存在明显问题 |
    | 执行可靠性 | 各种条件下都能正确执行 | 主要场景可靠执行 | 执行不稳定或有严重缺陷 |
    
    ## 验收检查项
    1. 提示词在目标环境中无语法错误
    2. 所有资源引用能被正确解析
    3. 执行流程覆盖正常和异常路径
    4. 关键指令有明确的执行优先级
    5. 组合协议间不存在语义冲突
  </criteria>
</execution> 