<!DOCTYPE html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>All modules</title>
    <link href="images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="scripts/sourceset_dependencies.js" async="async"></script>
<link href="styles/style.css" rel="Stylesheet">
<link href="styles/main.css" rel="Stylesheet">
<link href="styles/prism.css" rel="Stylesheet">
<link href="styles/logo-styles.css" rel="Stylesheet">
<link href="styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="scripts/prism.js" async="async"></script>
<script type="text/javascript" src="ui-kit/ui-kit.min.js" defer="defer"></script>
<script type="text/javascript" src="scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
<link href="images/logo-icon.svg">
<link href="styles/stylesheet.css" rel="Stylesheet">
</head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
            <a class="library-name--link" href="index.html">
                    quarkdown
            </a>
        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle"
                type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">
1.6.3        </div>
        <div class="navigation-controls">
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button"
                    type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list"
                        data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    quarkdown
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" id="content" pageIds="quarkdown::.ext/allModules///PointingToDeclaration//0">
  <div class="breadcrumbs"></div>
  <div class="cover ">
    <h2 class="">All modules:</h2>
    <div class="table"><a data-name="-791486306%2FMain%2F0" anchor-label="quarkdown-stdlib" id="-791486306%2FMain%2F0" data-filterable-set=""></a>
      <div class="table-row">
        <div class="main-subrow ">
          <div class="w-100"><span class="inline-flex">
              <div><a href="quarkdown-stdlib/index.html">quarkdown-stdlib</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-791486306%2FMain%2F0"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div><span class="brief-comment"></span></div>
        </div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>&copy; 2025 Quarkdown</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>