<execution domain="prompt-engineering">
  <process>
    # 角色合成提示词开发流程
    
    ```mermaid
    flowchart TD
      A[确定角色类型与目标] --> B[设计角色人格]
      B --> C[定义角色原则]
      C --> D[构建角色知识]
      D --> E[设计角色经验]
      E --> F[规划角色激活]
      F --> G[整合验证]
      G --> H{角色验证}
      H -->|通过| I[完成角色定义]
      H -->|需调整| J[修改优化]
      J --> B
    ```
    
    ## 核心步骤详解
    
    1. **确定角色类型与目标**
       - 明确角色的主要职责和应用场景
       - 选择适合的角色类型（顾问型/执行型/决策型/创造型）
       - 设定角色能力范围和限制
    
    2. **设计角色人格(`<personality>`)**
       - 选择和构建适合的思维模式组合
       - 定义思维模式的优先级和激活条件
       - 确保人格特征与角色类型相匹配
    
    3. **定义角色原则(`<principle>`)**
       - 构建角色的行为准则和执行框架
       - 设定行为模式的优先级和触发条件
       - 确保原则与人格定义协调一致
    
    4. **构建角色知识(`<knowledge>`)**
       - 设计角色的预设知识库结构
       - 整合领域专业知识和通用知识
       - 建立知识的层次关系和索引系统
    
    5. **设计角色经验(`<experience>`)**
       - 选择合适的记忆模式组合
       - 构建记忆的评估、存储和回忆机制
       - 设置记忆模式的优先级和检索条件
    
    6. **规划角色激活(`<action>`)**
       - 设计角色的初始化序列
       - 定义资源加载的优先级顺序
       - 构建稳健的启动确认机制
  </process>
  
  <guideline>
    ### 角色类型选择指南
    
    - **顾问型角色(Advisor)**适合场景：
      - 需要多角度分析和建议
      - 用户需要决策支持而非直接执行
      - 涉及复杂或模糊问题的解析
      ```mermaid
      mindmap
        root((顾问型角色))
          思维特点
            多角度思考
            批判性分析
          表达方式
            提供选项
            平衡利弊
          知识结构
            领域全面性
            原则性知识
      ```
    
    - **执行型角色(Executor)**适合场景：
      - 需要明确的操作步骤和流程
      - 任务目标明确，需精确执行
      - 重视效率和一致性
      ```mermaid
      mindmap
        root((执行型角色))
          思维特点
            逻辑性思考
            结构化分析
          表达方式
            步骤分解
            明确指令
          知识结构
            操作性知识
            程序性记忆
      ```
    
    - **决策型角色(Decider)**适合场景：
      - 需要根据标准做出判断
      - 在多种选择中确定最佳方案
      - 需要权威性的结论
      ```mermaid
      mindmap
        root((决策型角色))
          思维特点
            批判性思考
            权衡分析
          表达方式
            结论先行
            判断明确
          知识结构
            评估标准
            判断框架
      ```
    
    - **创造型角色(Creator)**适合场景：
      - 需要创新思维和新颖视角
      - 重视独特性和灵感激发
      - 解决开放性问题
      ```mermaid
      mindmap
        root((创造型角色))
          思维特点
            发散思维
            联想能力
          表达方式
            生动形象
            启发性表达
          知识结构
            跨领域关联
            启发性资源
      ```
    
    ### 角色组件设计建议
    
    - **人格(personality)组件**：
      - 使用思维导图展示思维特点和关系
      - 明确主导思维模式和辅助思维模式
      - 设置适当的思维模式切换触发条件
    
    - **原则(principle)组件**：
      - 使用流程图展示核心执行流程
      - 以列表形式呈现行为规则和指导原则
      - 确保原则间的优先级清晰
    
    - **知识(knowledge)组件**：
      - 采用树状结构组织领域知识
      - 区分核心知识和扩展知识
      - 平衡内联知识和资源引用
    
    - **经验(experience)组件**：
      - 明确定义记忆的评估标准
      - 建立一致的存储格式和标签系统
      - 设计高效的检索机制和应用策略
    
    - **激活(action)组件**：
      - 使用流程图展示初始化序列
      - 明确资源依赖关系和加载顺序
      - 包含必要的状态检查和错误处理
  </guideline>
  
  <rule>
    1. **角色完整性** - 角色定义必须包含personality、principle和action三个核心组件
    2. **组件一致性** - 各组件定义的内容必须相互协调，避免矛盾或冲突
    3. **思维边界清晰** - 角色的思维模式必须有明确的边界，避免角色行为不一致
    4. **行为规范明确** - 角色的行为原则和规范必须明确定义，不包含模糊表述
    5. **激活流程完整** - 角色激活组件必须包含完整的初始化流程和资源加载顺序
    6. **资源依赖明确** - 所有外部资源依赖必须明确标注，包括加载时机和路径
    7. **角色边界严格** - 角色能力范围和限制必须明确，避免能力范围模糊或过度承诺
  </rule>
  
  <constraint>
    1. **组件复杂度限制** - 单个组件的复杂度应控制在可管理范围内
    2. **资源依赖数量限制** - 角色直接依赖的外部资源数量应适当控制
    3. **知识库大小限制** - 内联知识库大小应控制在可高效加载的范围内
    4. **角色专注度限制** - 角色定义应保持适度的专注度，避免能力过于发散
    5. **跨组件交互复杂度** - 组件间的交互和依赖关系应保持在可理解和维护的复杂度
  </constraint>
  
  <criteria>
    | 指标 | 通过标准 | 不通过标准 |
    |------|---------|-----------|
    | 角色一致性 | 行为与人格定义匹配 | 行为与定义不符或不稳定 |
    | 组件完整性 | 包含所有必要组件且内容充分 | 缺失关键组件或内容空洞 |
    | 启动可靠性 | 初始化过程稳定可靠 | 启动失败或状态不确定 |
    | 能力明确性 | 角色能力边界清晰 | 能力范围模糊或过度承诺 |
    | 资源集成度 | 外部资源正确加载和应用 | 资源引用错误或未正确利用 |
    | 类型符合度 | 角色特性与目标类型匹配 | 行为与类型定位不符 |
    | 适应性 | 能在预期场景中灵活应对 | 应对能力单一或僵化 |
    | 运行稳定性 | 长期运行中保持一致性 | 状态漂移或行为退化 |
  </criteria>
</execution> 