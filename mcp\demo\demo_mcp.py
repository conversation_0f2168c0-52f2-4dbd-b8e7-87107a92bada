from fastapi import FastAPI, Response
from fastapi.responses import StreamingResponse
import asyncio
from typing import Generator

# 假设fastmcp提供的核心工具装饰器和MCP管理器
class MCP:
    def __init__(self):
        self.tools = {}
    
    def tool(self, name: str, description: str):
        def decorator(func):
            self.tools[name] = {
                "func": func,
                "description": description
            }
            return func
        return decorator

# 初始化MCP实例和FastAPI应用
mcp = MCP()
app = FastAPI()

# SSE事件流生成器（用于实时推送工具调用状态）
async def sse_event_generator() -> Generator[str, None, None]:
    while True:
        # 这里可以添加实际的事件逻辑（如工具调用通知）
        await asyncio.sleep(1)
        yield f"data: SSE心跳检测 - {asyncio.get_event_loop().time()}\n\n"

# 注册SSE端点
@app.get("/sse")
async def sse_endpoint():
    return StreamingResponse(sse_event_generator(), media_type="text/event-stream")

# 定义加减乘除工具（使用@mcp.tool装饰器）
@mcp.tool(
    name="加法计算",
    description="计算两个数的和，参数：a（数值）, b（数值）"
)
def add(a: float, b: float) -> float:
    return a + b

@mcp.tool(
    name="减法计算",
    description="计算两个数的差，参数：a（被减数）, b（减数）"
)
def subtract(a: float, b: float) -> float:
    return a - b

@mcp.tool(
    name="乘法计算",
    description="计算两个数的积，参数：a（乘数）, b（被乘数）"
)
def multiply(a: float, b: float) -> float:
    return a * b

@mcp.tool(
    name="除法计算",
    description="计算两个数的商（注意：除数不能为0），参数：a（被除数）, b（除数）"
)
def divide(a: float, b: float) -> float:
    if b == 0:
        raise ValueError("除数不能为0")
    return a / b

# 启动服务器（支持SSE和工具调用）
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)