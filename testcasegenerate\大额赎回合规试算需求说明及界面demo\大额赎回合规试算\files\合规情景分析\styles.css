﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-100px;
  width:2490px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:523px;
  height:103px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:1043px;
  width:523px;
  height:103px;
  display:flex;
}
#u0 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u0_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1366px;
  height:768px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:183px;
  width:1366px;
  height:768px;
  display:flex;
}
#u2 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1366px;
  height:768px;
  background:inherit;
  background-color:rgba(240, 242, 245, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:183px;
  width:1366px;
  height:768px;
  display:flex;
}
#u3 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1166px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:0px 2px 4px rgba(0, 0, 0, 0.0509803921568627);
  -webkit-box-shadow:0px 2px 4px rgba(0, 0, 0, 0.0509803921568627);
  box-shadow:0px 2px 4px rgba(0, 0, 0, 0.0509803921568627);
}
#u5 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:231px;
  width:1166px;
  height:32px;
  display:flex;
}
#u5 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:32px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:231px;
  width:19px;
  height:32px;
  display:flex;
}
#u7 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:32px;
  background:inherit;
  background-color:rgba(230, 230, 230, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:231px;
  width:1px;
  height:32px;
  display:flex;
}
#u8 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u8_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u10_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:240px;
  width:14px;
  height:14px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
  opacity:0.15;
}
#u10 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u10_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u11_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:5px;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:305px;
  top:245px;
  width:8px;
  height:5px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
  opacity:0.15;
}
#u11 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u11_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:241px;
  width:48px;
  height:12px;
  display:flex;
  opacity:0.9;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u12 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:392px;
  top:241px;
  width:48px;
  height:12px;
  display:flex;
  opacity:0.9;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u13 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u14_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
  line-height:12px;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:241px;
  width:93px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
  line-height:12px;
}
#u14 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u14_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u15_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:32px;
  background:inherit;
  background-color:rgba(230, 230, 230, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:448px;
  top:231px;
  width:1px;
  height:32px;
  display:flex;
}
#u15 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u15_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u16_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:32px;
  background:inherit;
  background-color:rgba(230, 230, 230, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:383px;
  top:231px;
  width:1px;
  height:32px;
  display:flex;
}
#u16 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u16_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u17_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:32px;
  background:inherit;
  background-color:rgba(230, 230, 230, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:231px;
  width:1px;
  height:32px;
  display:flex;
}
#u17 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u17_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u18_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:32px;
  background:inherit;
  background-color:rgba(230, 230, 230, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:1447px;
  top:231px;
  width:1px;
  height:32px;
  display:flex;
}
#u18 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u18_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u20_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:240px;
  width:14px;
  height:14px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
  opacity:0.55;
}
#u20 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u20_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u21_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:5px;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:1453px;
  top:245px;
  width:8px;
  height:5px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
  opacity:0.55;
}
#u21 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u21_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u23_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:32px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:1447px;
  top:231px;
  width:19px;
  height:32px;
  display:flex;
}
#u23 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u23_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u24_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:32px;
  background:inherit;
  background-color:rgba(230, 230, 230, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:1447px;
  top:231px;
  width:1px;
  height:32px;
  display:flex;
}
#u24 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u24_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u26_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:1450px;
  top:240px;
  width:14px;
  height:14px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
  opacity:0.15;
}
#u26 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u26_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u27_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:5px;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:1452px;
  top:245px;
  width:8px;
  height:5px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
  opacity:0.15;
}
#u27 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u27_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u29_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:720px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:2px 0px 4px rgba(0, 0, 0, 0.0509803921568627);
  -webkit-box-shadow:2px 0px 4px rgba(0, 0, 0, 0.0509803921568627);
  box-shadow:2px 0px 4px rgba(0, 0, 0, 0.0509803921568627);
}
#u29 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:231px;
  width:200px;
  height:720px;
  display:flex;
}
#u29 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u29_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u31_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:36px;
  background:inherit;
  background-color:rgba(239, 245, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:279px;
  width:200px;
  height:36px;
  display:flex;
}
#u31 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u31_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u32_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:3px;
  height:36px;
  background:inherit;
  background-color:rgba(70, 134, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:279px;
  width:3px;
  height:36px;
  display:flex;
}
#u32 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u32_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:255px;
  width:48px;
  height:12px;
  display:flex;
  opacity:0.9;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u33 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u33_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u34_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:327px;
  width:48px;
  height:12px;
  display:flex;
  opacity:0.9;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u34 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u34_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u35_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:363px;
  width:72px;
  height:12px;
  display:flex;
  opacity:0.9;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u35 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u35_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u36_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:399px;
  width:48px;
  height:12px;
  display:flex;
  opacity:0.9;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u36 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u36_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u37_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:435px;
  width:72px;
  height:12px;
  display:flex;
  opacity:0.9;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u37 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u37_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u38_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#4686F2;
  text-align:left;
  line-height:12px;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:291px;
  width:48px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#4686F2;
  text-align:left;
  line-height:12px;
}
#u38 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u38_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u39 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u40_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:398px;
  width:14px;
  height:14px;
  display:flex;
}
#u40 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u40_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u41_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:399px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0.75;
}
#u41 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u41_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u43_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:434px;
  width:14px;
  height:14px;
  display:flex;
}
#u43 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u43_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u44_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:435px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0.75;
}
#u44 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u44_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u46_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:290px;
  width:14px;
  height:14px;
  display:flex;
}
#u46 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u46_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u48_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:1px;
  background:inherit;
  background-color:rgba(70, 134, 242, 1);
  border:none;
  border-radius:4.5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:294px;
  width:4px;
  height:1px;
  display:flex;
}
#u48 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u48_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u49_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:3px;
  height:1px;
  background:inherit;
  background-color:rgba(70, 134, 242, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:297px;
  width:3px;
  height:1px;
  display:flex;
}
#u49 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u49_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u50_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:3px;
  height:1px;
  background:inherit;
  background-color:rgba(70, 134, 242, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:299px;
  width:3px;
  height:1px;
  display:flex;
}
#u50 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u50_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u51_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:291px;
  width:11px;
  height:11px;
  display:flex;
}
#u51 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u51_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u52_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:6px;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:297px;
  width:7px;
  height:6px;
  display:flex;
}
#u52 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u52_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u55 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u56 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u57_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u57 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:362px;
  width:14px;
  height:14px;
  display:flex;
  opacity:0.75;
}
#u57 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u57_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u58_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:5px;
}
#u58 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:367px;
  width:6px;
  height:5px;
  display:flex;
  opacity:0.75;
}
#u58 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u58_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u59_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u59 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:362px;
  width:14px;
  height:14px;
  display:flex;
  opacity:0.75;
}
#u59 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u59_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u60 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u61 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u62 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u63_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u63 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:255px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0.75;
}
#u63 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u63_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u64_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:3px;
  height:1px;
}
#u64 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:263px;
  width:3px;
  height:1px;
  display:flex;
  opacity:0.75;
}
#u64 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u64_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u65_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:6px;
}
#u65 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:258px;
  width:6px;
  height:6px;
  display:flex;
  opacity:0.75;
}
#u65 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u65_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u66_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:3px;
  height:3px;
}
#u66 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:257px;
  width:3px;
  height:3px;
  display:flex;
  opacity:0.75;
}
#u66 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u66_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u67_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u67 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:254px;
  width:14px;
  height:14px;
  display:flex;
  opacity:0.75;
}
#u67 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u67_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u68 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u69 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u70_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u70 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:326px;
  width:14px;
  height:14px;
  display:flex;
  opacity:0.75;
}
#u70 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u70_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u71 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u72_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:10px;
}
#u72 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:328px;
  width:14px;
  height:10px;
  display:flex;
  opacity:0.75;
}
#u72 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u72_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u73_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:5px;
}
#u73 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:331px;
  width:3px;
  height:4px;
  display:flex;
  opacity:0.75;
}
#u73 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u73_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u74_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:2px;
}
#u74 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:335px;
  width:3px;
  height:1px;
  display:flex;
  opacity:0.75;
}
#u74 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u74_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u75_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:1px;
  background:inherit;
  background-color:rgba(237, 237, 237, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u75 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:918px;
  width:200px;
  height:1px;
  display:flex;
}
#u75 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u75_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u76 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u77_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u77 {
  border-width:0px;
  position:absolute;
  left:192px;
  top:927px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0.75;
}
#u77 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u77_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u78_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:1px;
  background:inherit;
  background-color:rgba(51, 51, 51, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u78 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:939px;
  width:12px;
  height:1px;
  display:flex;
  opacity:0.75;
}
#u78 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u78_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u79_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:1px;
  background:inherit;
  background-color:rgba(51, 51, 51, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u79 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:930px;
  width:12px;
  height:1px;
  display:flex;
  opacity:0.75;
}
#u79 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u79_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u80_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:1px;
  background:inherit;
  background-color:rgba(51, 51, 51, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u80 {
  border-width:0px;
  position:absolute;
  left:197px;
  top:935px;
  width:9px;
  height:1px;
  display:flex;
  opacity:0.75;
}
#u80 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u80_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u81_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:3px;
  height:4px;
}
#u81 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:933px;
  width:3px;
  height:4px;
  display:flex;
  opacity:0.75;
}
#u81 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u81_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u82 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u83_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1366px;
  height:48px;
  background:-webkit-linear-gradient(180deg, rgba(43, 154, 255, 1) 0%, rgba(39, 101, 216, 1) 100%);
  background:-moz-linear-gradient(270deg, rgba(43, 154, 255, 1) 0%, rgba(39, 101, 216, 1) 100%);
  background:linear-gradient(270deg, rgba(43, 154, 255, 1) 0%, rgba(39, 101, 216, 1) 100%);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u83 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:183px;
  width:1366px;
  height:48px;
  display:flex;
}
#u83 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u83_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u84 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u85_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.2);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u85 {
  border-width:0px;
  position:absolute;
  left:1119px;
  top:193px;
  width:200px;
  height:28px;
  display:flex;
}
#u85 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u85_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u86 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u87_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u87 {
  border-width:0px;
  position:absolute;
  left:1297px;
  top:199px;
  width:16px;
  height:16px;
  display:flex;
}
#u87 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u87_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u88_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u88 {
  border-width:0px;
  position:absolute;
  left:1298px;
  top:200px;
  width:14px;
  height:14px;
  display:flex;
}
#u88 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u88_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u89_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:24px;
}
#u89 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:195px;
  width:50px;
  height:24px;
  display:flex;
}
#u89 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u89_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u90 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u91_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u91 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:199px;
  width:16px;
  height:16px;
  display:flex;
}
#u91 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u91_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u92_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u92 {
  border-width:0px;
  position:absolute;
  left:329px;
  top:200px;
  width:14px;
  height:14px;
  display:flex;
}
#u92 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u92_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u93 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u94_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u94 {
  border-width:0px;
  position:absolute;
  left:1372px;
  top:196px;
  width:70px;
  height:22px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u94 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u94_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u95 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u96_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u96 {
  border-width:0px;
  position:absolute;
  left:1237px;
  top:199px;
  width:16px;
  height:16px;
  display:flex;
}
#u96 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u96_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u97 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u98 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u99 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u100_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:48px;
  background:inherit;
  background-color:rgba(33, 90, 196, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u100 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:183px;
  width:80px;
  height:48px;
  display:flex;
  opacity:0.5;
}
#u100 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u100_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u101_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u101 {
  border-width:0px;
  position:absolute;
  left:368px;
  top:197px;
  width:56px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u101 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u101_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u102_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
  line-height:14px;
}
#u102 {
  border-width:0px;
  position:absolute;
  left:182px;
  top:200px;
  width:118px;
  height:14px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
  line-height:14px;
}
#u102 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u103 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u104_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u104 {
  border-width:0px;
  position:absolute;
  left:1331px;
  top:199px;
  width:16px;
  height:16px;
  display:flex;
}
#u104 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u104_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u105 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u106_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:16px;
}
#u106 {
  border-width:0px;
  position:absolute;
  left:1333px;
  top:199px;
  width:12px;
  height:16px;
  display:flex;
}
#u106 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u107_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u107 {
  border-width:0px;
  position:absolute;
  left:1359px;
  top:199px;
  width:1px;
  height:16px;
  display:flex;
  opacity:0.3;
}
#u107 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u107_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u108 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u109_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1154px;
  height:396px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u109 {
  border-width:0px;
  position:absolute;
  left:306px;
  top:549px;
  width:1154px;
  height:396px;
  display:flex;
}
#u109 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u109_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u110 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u111_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u111 {
  border-width:0px;
  position:absolute;
  left:1370px;
  top:643px;
  width:24px;
  height:24px;
  display:flex;
}
#u111 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u111_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u112_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u112 {
  border-width:0px;
  position:absolute;
  left:1377px;
  top:650px;
  width:11px;
  height:11px;
  display:flex;
}
#u112 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u112_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u113_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u113 {
  border-width:0px;
  position:absolute;
  left:1393px;
  top:643px;
  width:24px;
  height:24px;
  display:flex;
}
#u113 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u113_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u114_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u114 {
  border-width:0px;
  position:absolute;
  left:1399px;
  top:649px;
  width:12px;
  height:12px;
  display:flex;
}
#u114 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u115_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  border-top-left-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u115 {
  border-width:0px;
  position:absolute;
  left:1416px;
  top:643px;
  width:38px;
  height:24px;
  display:flex;
}
#u115 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u116 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u117_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:13px;
}
#u117 {
  border-width:0px;
  position:absolute;
  left:1422px;
  top:649px;
  width:12px;
  height:13px;
  display:flex;
}
#u117 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u117_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u118 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u119_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(213, 247, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u119 {
  border-width:0px;
  position:absolute;
  left:1436px;
  top:649px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
}
#u119 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u120_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:7px;
}
#u120 {
  border-width:0px;
  position:absolute;
  left:1438px;
  top:650px;
  width:7px;
  height:7px;
  display:flex;
  -webkit-transform:rotate(315deg);
  -moz-transform:rotate(315deg);
  -ms-transform:rotate(315deg);
  transform:rotate(315deg);
}
#u120 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u120_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u121 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1154px;
  height:1px;
  background:inherit;
  background-color:rgba(230, 230, 230, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u122 {
  border-width:0px;
  position:absolute;
  left:306px;
  top:576px;
  width:1154px;
  height:1px;
  display:flex;
}
#u122 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u122_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u123 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(213, 247, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u124 {
  border-width:0px;
  position:absolute;
  left:1438px;
  top:555px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u124 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u124_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u125_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u125 {
  border-width:0px;
  position:absolute;
  left:1439px;
  top:556px;
  width:14px;
  height:14px;
  display:flex;
}
#u125 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u125_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u126 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u127_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u127 {
  border-width:0px;
  position:absolute;
  left:1416px;
  top:555px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u127 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u128 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u129_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:5px;
}
#u129 {
  border-width:0px;
  position:absolute;
  left:1417px;
  top:565px;
  width:13px;
  height:5px;
  display:flex;
}
#u129 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u130 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u131_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:5px;
}
#u131 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:556px;
  width:13px;
  height:5px;
  display:flex;
}
#u131 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u131_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:20px;
}
#u132 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:553px;
  width:72px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:20px;
}
#u132 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u132_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:14px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u133 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:556px;
  width:4px;
  height:14px;
  display:flex;
}
#u133 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:20px;
}
#u134 {
  border-width:0px;
  position:absolute;
  left:414px;
  top:553px;
  width:96px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:20px;
}
#u134 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u134_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:14px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u135 {
  border-width:0px;
  position:absolute;
  left:404px;
  top:556px;
  width:4px;
  height:14px;
  display:flex;
}
#u135 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u135_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u136 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u137_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:238px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:2px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u137 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:679px;
  width:1141px;
  height:238px;
  display:flex;
}
#u137 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u137_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:24px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u138 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:724px;
  width:1141px;
  height:24px;
  display:flex;
}
#u138 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u138_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u139 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:748px;
  width:1142px;
  height:1px;
  display:flex;
}
#u139 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u140_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:24px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u140 {
  border-width:0px;
  position:absolute;
  left:313px;
  top:773px;
  width:1141px;
  height:24px;
  display:flex;
}
#u140 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u140_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:24px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u141 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:821px;
  width:1141px;
  height:24px;
  display:flex;
}
#u141 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u141_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u142_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:24px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u142 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:870px;
  width:1141px;
  height:24px;
  display:flex;
}
#u142 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u142_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u143_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:20px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u143 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:919px;
  width:1141px;
  height:20px;
  display:flex;
}
#u143 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u143_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u144_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:28px;
  background:inherit;
  background-color:rgba(243, 243, 243, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(230, 230, 230, 1);
  border-radius:2px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u144 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:673px;
  width:1142px;
  height:28px;
  display:flex;
}
#u144 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u144_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u145_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u145 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:724px;
  width:1142px;
  height:1px;
  display:flex;
}
#u145 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u145_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u146_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u146 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:820px;
  width:1142px;
  height:1px;
  display:flex;
}
#u146 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u146_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u147_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u147 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:844px;
  width:1142px;
  height:1px;
  display:flex;
}
#u147 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u147_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u148_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u148 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:772px;
  width:1142px;
  height:1px;
  display:flex;
}
#u148 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u148_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u149_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u149 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:796px;
  width:1142px;
  height:1px;
  display:flex;
}
#u149 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u149_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u150_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u150 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:869px;
  width:1141px;
  height:1px;
  display:flex;
}
#u150 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u150_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u151_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u151 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:893px;
  width:1141px;
  height:1px;
  display:flex;
}
#u151 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u152_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u152 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:919px;
  width:1141px;
  height:1px;
  display:flex;
}
#u152 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u153_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u153 {
  border-width:0px;
  position:absolute;
  left:405px;
  top:681px;
  width:53px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u153 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u153_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u154_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u154 {
  border-width:0px;
  position:absolute;
  left:497px;
  top:681px;
  width:53px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u154 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u155_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u155 {
  border-width:0px;
  position:absolute;
  left:594px;
  top:681px;
  width:49px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u155 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u156_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u156 {
  border-width:0px;
  position:absolute;
  left:681px;
  top:681px;
  width:51px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u156 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u157_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u157 {
  border-width:0px;
  position:absolute;
  left:863px;
  top:681px;
  width:51px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u157 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u157_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u158_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u158 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:681px;
  width:51px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u158 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u158_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u159_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u159 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:681px;
  width:48px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u159 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u159_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u160_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u160 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:702px;
  width:49px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u160 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u161 {
  border-width:0px;
  position:absolute;
  left:405px;
  top:702px;
  width:49px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u161 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u161_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u162 {
  border-width:0px;
  position:absolute;
  left:497px;
  top:702px;
  width:49px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u162 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u162_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u163 {
  border-width:0px;
  position:absolute;
  left:594px;
  top:702px;
  width:42px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u163 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:170px;
  height:285px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u164 {
  border-width:0px;
  position:absolute;
  left:681px;
  top:702px;
  width:170px;
  height:285px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u164 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u165_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:288px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u165 {
  border-width:0px;
  position:absolute;
  left:863px;
  top:702px;
  width:42px;
  height:288px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u165 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u165_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u166_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u166 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:702px;
  width:42px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u166 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u167_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u167 {
  border-width:0px;
  position:absolute;
  left:490px;
  top:674px;
  width:1px;
  height:27px;
  display:flex;
}
#u167 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u168_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u168 {
  border-width:0px;
  position:absolute;
  left:582px;
  top:674px;
  width:1px;
  height:27px;
  display:flex;
}
#u168 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u169_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u169 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:674px;
  width:1px;
  height:27px;
  display:flex;
}
#u169 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u170_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u170 {
  border-width:0px;
  position:absolute;
  left:669px;
  top:674px;
  width:1px;
  height:27px;
  display:flex;
}
#u170 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u171_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u171 {
  border-width:0px;
  position:absolute;
  left:845px;
  top:674px;
  width:1px;
  height:27px;
  display:flex;
}
#u171 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u171_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u172_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u172 {
  border-width:0px;
  position:absolute;
  left:932px;
  top:674px;
  width:1px;
  height:27px;
  display:flex;
}
#u172 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u173_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u173 {
  border-width:0px;
  position:absolute;
  left:1029px;
  top:674px;
  width:1px;
  height:27px;
  display:flex;
}
#u173 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u174_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u174 {
  border-width:0px;
  position:absolute;
  left:1130px;
  top:674px;
  width:1px;
  height:27px;
  display:flex;
}
#u174 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u175_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:212px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(230, 230, 230, 1);
  border-radius:2px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u175 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:745px;
  width:1142px;
  height:212px;
  display:flex;
}
#u175 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u176_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u176 {
  border-width:0px;
  position:absolute;
  left:1059px;
  top:681px;
  width:51px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u176 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u176_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u177 {
  border-width:0px;
  position:absolute;
  left:1059px;
  top:702px;
  width:42px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u177 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u178_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u178 {
  border-width:0px;
  position:absolute;
  left:1160px;
  top:681px;
  width:51px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u178 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u178_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u179_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:283px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u179 {
  border-width:0px;
  position:absolute;
  left:1160px;
  top:702px;
  width:42px;
  height:283px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u179 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u179_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u180_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u180 {
  border-width:0px;
  position:absolute;
  left:1253px;
  top:674px;
  width:1px;
  height:27px;
  display:flex;
}
#u180 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u181_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u181 {
  border-width:0px;
  position:absolute;
  left:1283px;
  top:681px;
  width:51px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u181 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u182_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:283px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u182 {
  border-width:0px;
  position:absolute;
  left:1283px;
  top:702px;
  width:116px;
  height:283px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u182 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u183 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u184 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u185_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u185 {
  border-width:0px;
  position:absolute;
  left:570px;
  top:585px;
  width:168px;
  height:24px;
  display:flex;
}
#u185 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u186_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u186 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:588px;
  width:24px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u186 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u187_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
#u187 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:588px;
  width:36px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
#u187 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u188 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u189_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u189 {
  border-width:0px;
  position:absolute;
  left:716px;
  top:589px;
  width:16px;
  height:16px;
  display:flex;
}
#u189 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u189_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u190_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:6px;
}
#u190 {
  border-width:0px;
  position:absolute;
  left:719px;
  top:594px;
  width:9px;
  height:6px;
  display:flex;
}
#u190 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u190_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u191 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u192 {
  border-width:0px;
  position:absolute;
  left:366px;
  top:585px;
  width:138px;
  height:24px;
  display:flex;
}
#u192 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u193 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u194_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(241, 70, 88, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u194 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:589px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u194 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u195 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u196_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 190, 190, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u196 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:589px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u196 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u197_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:6px;
}
#u197 {
  border-width:0px;
  position:absolute;
  left:486px;
  top:594px;
  width:9px;
  height:6px;
  display:flex;
}
#u197 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u197_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u198_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
  line-height:18px;
}
#u198 {
  border-width:0px;
  position:absolute;
  left:372px;
  top:588px;
  width:106px;
  height:18px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
  line-height:18px;
}
#u198 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u198_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u199_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u199 {
  border-width:0px;
  position:absolute;
  left:336px;
  top:588px;
  width:24px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u199 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u200 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u201_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u201 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:585px;
  width:24px;
  height:24px;
  display:flex;
}
#u201 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u202 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u203 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u204_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u204 {
  border-width:0px;
  position:absolute;
  left:514px;
  top:589px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u204 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u204_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u205 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u206_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:13px;
}
#u206 {
  border-width:0px;
  position:absolute;
  left:515px;
  top:590px;
  width:12px;
  height:13px;
  display:flex;
}
#u206 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u207_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:1px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  border:none;
  border-radius:0.25px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u207 {
  border-width:0px;
  position:absolute;
  left:524px;
  top:601px;
  width:5px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(45deg);
  -moz-transform:rotate(45deg);
  -ms-transform:rotate(45deg);
  transform:rotate(45deg);
}
#u207 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u207_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u208 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u209_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u209 {
  border-width:0px;
  position:absolute;
  left:570px;
  top:615px;
  width:168px;
  height:24px;
  display:flex;
}
#u209 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u210_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u210 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:618px;
  width:24px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u210 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u211_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
#u211 {
  border-width:0px;
  position:absolute;
  left:576px;
  top:618px;
  width:36px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
#u211 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u211_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u212 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u213_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u213 {
  border-width:0px;
  position:absolute;
  left:716px;
  top:619px;
  width:16px;
  height:16px;
  display:flex;
}
#u213 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u214_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:6px;
}
#u214 {
  border-width:0px;
  position:absolute;
  left:719px;
  top:624px;
  width:9px;
  height:6px;
  display:flex;
}
#u214 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u215 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u216_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u216 {
  border-width:0px;
  position:absolute;
  left:366px;
  top:615px;
  width:168px;
  height:24px;
  display:flex;
}
#u216 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u216_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u217_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u217 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:618px;
  width:48px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u217 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u218_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
#u218 {
  border-width:0px;
  position:absolute;
  left:372px;
  top:618px;
  width:36px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
#u218 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u219 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u220 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:619px;
  width:16px;
  height:16px;
  display:flex;
}
#u220 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u220_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u221_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:6px;
}
#u221 {
  border-width:0px;
  position:absolute;
  left:515px;
  top:624px;
  width:9px;
  height:6px;
  display:flex;
}
#u221 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u221_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u222 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u223_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u223 {
  border-width:0px;
  position:absolute;
  left:798px;
  top:585px;
  width:168px;
  height:24px;
  display:flex;
}
#u223 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u223_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u224 {
  border-width:0px;
  position:absolute;
  left:744px;
  top:588px;
  width:48px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
#u224 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u225_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
#u225 {
  border-width:0px;
  position:absolute;
  left:804px;
  top:588px;
  width:36px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
#u225 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u226 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u227_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:6px;
}
#u227 {
  border-width:0px;
  position:absolute;
  left:948px;
  top:594px;
  width:9px;
  height:6px;
  display:flex;
}
#u227 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u228 label {
  left:0px;
  width:100%;
}
#u228_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:12px;
  height:12px;
}
#u228 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:652px;
  width:100px;
  height:15px;
  display:flex;
}
#u228 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u228_img.selected {
}
#u228.selected {
}
#u228_img.disabled {
}
#u228.disabled {
}
#u228_img.selectedDisabled {
}
#u228.selectedDisabled {
}
#u228_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u228_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u229 label {
  left:0px;
  width:100%;
}
#u229_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:12px;
  height:12px;
}
#u229 {
  border-width:0px;
  position:absolute;
  left:395px;
  top:652px;
  width:100px;
  height:15px;
  display:flex;
}
#u229 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u229_img.selected {
}
#u229.selected {
}
#u229_img.disabled {
}
#u229.disabled {
}
#u229_img.selectedDisabled {
}
#u229.selectedDisabled {
}
#u229_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u229_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u230 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u231 {
  border-width:0px;
  position:absolute;
  left:798px;
  top:615px;
  width:48px;
  height:24px;
  display:flex;
}
#u231 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u232_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u232 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:621px;
  width:24px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u232 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u232_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u233_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:24px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u233 {
  border-width:0px;
  position:absolute;
  left:744px;
  top:615px;
  width:48px;
  height:24px;
  display:flex;
}
#u233 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u233_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u234_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:12px;
}
#u234 {
  border-width:0px;
  position:absolute;
  left:756px;
  top:621px;
  width:24px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:12px;
}
#u234 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u235 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u236_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1154px;
  height:274px;
}
#u236 {
  border-width:0px;
  position:absolute;
  left:306px;
  top:269px;
  width:1154px;
  height:274px;
  display:flex;
}
#u236 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u236_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u237 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u238 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u239 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u240_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:659px;
  height:28px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:0px -1px 0px 0px rgba(230, 230, 230, 1) inset;
  -webkit-box-shadow:0px -1px 0px 0px rgba(230, 230, 230, 1) inset;
  box-shadow:0px -1px 0px 0px rgba(230, 230, 230, 1) inset;
}
#u240 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:334px;
  width:659px;
  height:28px;
  display:flex;
}
#u240 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u240_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u241_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:659px;
  height:24px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u241 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:386px;
  width:659px;
  height:24px;
  display:flex;
}
#u241 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u241_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u242_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:660px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u242 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:362px;
  width:660px;
  height:24px;
  display:flex;
}
#u242 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u242_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u243_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:659px;
  height:24px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u243 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:434px;
  width:659px;
  height:24px;
  display:flex;
}
#u243 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u243_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u244_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:659px;
  height:24px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u244 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:482px;
  width:659px;
  height:24px;
  display:flex;
}
#u244 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u244_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u245_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:659px;
  height:6px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u245 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:530px;
  width:659px;
  height:6px;
  display:flex;
}
#u245 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u246_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:659px;
  height:97px;
}
#u246 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:386px;
  width:659px;
  height:97px;
  display:flex;
}
#u246 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u247_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:661px;
  height:204px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(230, 230, 230, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u247 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:333px;
  width:661px;
  height:204px;
  display:flex;
}
#u247 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u247_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u248_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:29px;
}
#u248 {
  border-width:0px;
  position:absolute;
  left:818px;
  top:334px;
  width:1px;
  height:28px;
  display:flex;
}
#u248 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u248_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u249_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:29px;
}
#u249 {
  border-width:0px;
  position:absolute;
  left:949px;
  top:334px;
  width:1px;
  height:28px;
  display:flex;
}
#u249 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u249_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u250_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:29px;
}
#u250 {
  border-width:0px;
  position:absolute;
  left:1079px;
  top:334px;
  width:1px;
  height:28px;
  display:flex;
}
#u250 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u250_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u251_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:29px;
}
#u251 {
  border-width:0px;
  position:absolute;
  left:1340px;
  top:334px;
  width:1px;
  height:28px;
  display:flex;
}
#u251 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u251_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u252_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:29px;
}
#u252 {
  border-width:0px;
  position:absolute;
  left:1210px;
  top:334px;
  width:1px;
  height:28px;
  display:flex;
}
#u252 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u252_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u253 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u254_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u254 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:362px;
  width:61px;
  height:170px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u254 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u254_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u255_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u255 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:341px;
  width:48px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u255 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u256_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u256 {
  border-width:0px;
  position:absolute;
  left:956px;
  top:341px;
  width:48px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u256 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u256_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u257_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u257 {
  border-width:0px;
  position:absolute;
  left:1086px;
  top:341px;
  width:48px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u257 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u257_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u258_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u258 {
  border-width:0px;
  position:absolute;
  left:1217px;
  top:341px;
  width:72px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u258 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u258_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u259_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u259 {
  border-width:0px;
  position:absolute;
  left:1347px;
  top:341px;
  width:74px;
  height:14px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u259 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u259_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u260_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u260 {
  border-width:0px;
  position:absolute;
  left:951px;
  top:362px;
  width:99px;
  height:170px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u260 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u260_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u261_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u261 {
  border-width:0px;
  position:absolute;
  left:1080px;
  top:362px;
  width:99px;
  height:170px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u261 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u261_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u262_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:left;
  line-height:24px;
}
#u262 {
  border-width:0px;
  position:absolute;
  left:1211px;
  top:362px;
  width:99px;
  height:170px;
  display:flex;
  text-align:left;
  line-height:24px;
}
#u262 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u262_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u263_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:left;
  line-height:24px;
}
#u263 {
  border-width:0px;
  position:absolute;
  left:1343px;
  top:362px;
  width:99px;
  height:170px;
  display:flex;
  text-align:left;
  line-height:24px;
}
#u263 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u263_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u264 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u265_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u265 {
  border-width:0px;
  position:absolute;
  left:799px;
  top:341px;
  width:14px;
  height:14px;
  display:flex;
}
#u265 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u265_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u266_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u266 {
  border-width:0px;
  position:absolute;
  left:799px;
  top:366px;
  width:14px;
  height:14px;
  display:flex;
}
#u266 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u266_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u267_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u267 {
  border-width:0px;
  position:absolute;
  left:799px;
  top:389px;
  width:14px;
  height:14px;
  display:flex;
}
#u267 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u267_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u268_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u268 {
  border-width:0px;
  position:absolute;
  left:799px;
  top:414px;
  width:14px;
  height:14px;
  display:flex;
}
#u268 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u268_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u269_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u269 {
  border-width:0px;
  position:absolute;
  left:799px;
  top:438px;
  width:14px;
  height:14px;
  display:flex;
}
#u269 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u269_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u270 {
  border-width:0px;
  position:absolute;
  left:799px;
  top:461px;
  width:14px;
  height:14px;
  display:flex;
}
#u270 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u270_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u271 {
  border-width:0px;
  position:absolute;
  left:799px;
  top:488px;
  width:14px;
  height:14px;
  display:flex;
}
#u271 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u271_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u272_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u272 {
  border-width:0px;
  position:absolute;
  left:799px;
  top:511px;
  width:14px;
  height:14px;
  display:flex;
}
#u272 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u272_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u273 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  border-top-left-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u274 {
  border-width:0px;
  position:absolute;
  left:1391px;
  top:303px;
  width:40px;
  height:24px;
  display:flex;
}
#u274 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u274_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u275_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u275 {
  border-width:0px;
  position:absolute;
  left:1430px;
  top:303px;
  width:24px;
  height:24px;
  display:flex;
}
#u275 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u275_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u276 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(213, 247, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u277 {
  border-width:0px;
  position:absolute;
  left:1434px;
  top:307px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u277 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u277_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u278_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u278 {
  border-width:0px;
  position:absolute;
  left:1435px;
  top:308px;
  width:14px;
  height:14px;
  display:flex;
}
#u278 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u278_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u279 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u280_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(216, 216, 216, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u280 {
  border-width:0px;
  position:absolute;
  left:1395px;
  top:307px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u280 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u280_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u281 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u282_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u282 {
  border-width:0px;
  position:absolute;
  left:1397px;
  top:309px;
  width:12px;
  height:12px;
  display:flex;
}
#u282 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u282_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u283_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
}
#u283 {
  border-width:0px;
  position:absolute;
  left:1401px;
  top:313px;
  width:10px;
  height:10px;
  display:flex;
}
#u283 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u283_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u284 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u285_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 115, 124, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u285 {
  border-width:0px;
  position:absolute;
  left:1415px;
  top:309px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
}
#u285 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u285_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u286_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:5px;
}
#u286 {
  border-width:0px;
  position:absolute;
  left:1418px;
  top:313px;
  width:7px;
  height:5px;
  display:flex;
}
#u286 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u286_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u287_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:274px;
}
#u287 {
  border-width:0px;
  position:absolute;
  left:787px;
  top:269px;
  width:1px;
  height:274px;
  display:flex;
}
#u287 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u288_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:20px;
}
#u288 {
  border-width:0px;
  position:absolute;
  left:803px;
  top:274px;
  width:72px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:20px;
}
#u288 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u288_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u289_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:14px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u289 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:276px;
  width:4px;
  height:14px;
  display:flex;
}
#u289 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u289_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u290 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u291 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u292_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u292 {
  border-width:0px;
  position:absolute;
  left:373px;
  top:396px;
  width:84px;
  height:24px;
  display:flex;
}
#u292 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u292_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u293_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:left;
  line-height:12px;
}
#u293 {
  border-width:0px;
  position:absolute;
  left:402px;
  top:402px;
  width:35px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:left;
  line-height:12px;
}
#u293 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u293_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u294_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:24px;
  background:inherit;
  background-color:rgba(241, 248, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(3, 125, 243, 1);
  border-radius:2px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u294 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:396px;
  width:84px;
  height:24px;
  display:flex;
}
#u294 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u294_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u295_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
  line-height:12px;
}
#u295 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:402px;
  width:22px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
  line-height:12px;
}
#u295 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u295_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u296_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:right;
  line-height:12px;
}
#u296 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:402px;
  width:55px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:right;
  line-height:12px;
}
#u296 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u296_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u297 {
  border-width:0px;
  position:absolute;
  left:373px;
  top:467px;
  width:84px;
  height:24px;
  display:flex;
}
#u297 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u297_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u298_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:left;
  line-height:12px;
}
#u298 {
  border-width:0px;
  position:absolute;
  left:402px;
  top:473px;
  width:35px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:left;
  line-height:12px;
}
#u298 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u298_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u299_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:24px;
  background:inherit;
  background-color:rgba(241, 248, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(3, 125, 243, 1);
  border-radius:2px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u299 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:467px;
  width:84px;
  height:24px;
  display:flex;
}
#u299 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u299_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u300_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
  line-height:12px;
}
#u300 {
  border-width:0px;
  position:absolute;
  left:488px;
  top:473px;
  width:22px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
  line-height:12px;
}
#u300 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u300_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u301 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u302_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
  line-height:12px;
}
#u302 {
  border-width:0px;
  position:absolute;
  left:306px;
  top:369px;
  width:61px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
  line-height:12px;
}
#u302 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u302_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u303_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u303 {
  border-width:0px;
  position:absolute;
  left:373px;
  top:363px;
  width:168px;
  height:24px;
  display:flex;
}
#u303 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u303_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u304_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  border-top-left-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u304 {
  border-width:0px;
  position:absolute;
  left:517px;
  top:363px;
  width:24px;
  height:24px;
  display:flex;
}
#u304 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u304_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u305_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  text-align:left;
  line-height:12px;
}
#u305 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:369px;
  width:12px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  text-align:left;
  line-height:12px;
}
#u305 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u305_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u306 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u307_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
  line-height:12px;
}
#u307 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:309px;
  width:55px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
  line-height:12px;
}
#u307 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u307_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u308_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u308 {
  border-width:0px;
  position:absolute;
  left:373px;
  top:303px;
  width:168px;
  height:24px;
  display:flex;
}
#u308 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u308_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u309 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u310_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(213, 247, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u310 {
  border-width:0px;
  position:absolute;
  left:519px;
  top:307px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u310 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u310_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u311_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:6px;
}
#u311 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:312px;
  width:9px;
  height:6px;
  display:flex;
}
#u311 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u311_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u312_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u312 {
  border-width:0px;
  position:absolute;
  left:379px;
  top:309px;
  width:124px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u312 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u312_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u313 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u314_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:right;
  line-height:12px;
}
#u314 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:454px;
  width:55px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:right;
  line-height:12px;
}
#u314 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u314_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u315 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u316_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:7px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
  line-height:12px;
}
#u316 {
  border-width:0px;
  position:absolute;
  left:306px;
  top:438px;
  width:61px;
  height:7px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
  line-height:12px;
}
#u316 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u316_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u317_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u317 {
  border-width:0px;
  position:absolute;
  left:373px;
  top:432px;
  width:168px;
  height:24px;
  display:flex;
}
#u317 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u317_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  border-top-left-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u318 {
  border-width:0px;
  position:absolute;
  left:517px;
  top:432px;
  width:24px;
  height:24px;
  display:flex;
}
#u318 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u318_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u319_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  text-align:left;
  line-height:12px;
}
#u319 {
  border-width:0px;
  position:absolute;
  left:523px;
  top:438px;
  width:18px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  text-align:left;
  line-height:12px;
}
#u319 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u319_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u320 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u321_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#F14658;
  text-align:right;
  line-height:12px;
}
#u321 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:339px;
  width:55px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#F14658;
  text-align:right;
  line-height:12px;
}
#u321 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u321_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u322_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u322 {
  border-width:0px;
  position:absolute;
  left:372px;
  top:333px;
  width:168px;
  height:24px;
  display:flex;
}
#u322 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u322_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u323 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u324_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(213, 247, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u324 {
  border-width:0px;
  position:absolute;
  left:518px;
  top:337px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u324 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u324_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u325_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:6px;
}
#u325 {
  border-width:0px;
  position:absolute;
  left:522px;
  top:342px;
  width:9px;
  height:6px;
  display:flex;
}
#u325 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u325_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u326_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u326 {
  border-width:0px;
  position:absolute;
  left:378px;
  top:339px;
  width:124px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u326 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u326_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u327 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u328_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:24px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u328 {
  border-width:0px;
  position:absolute;
  left:733px;
  top:513px;
  width:48px;
  height:24px;
  display:flex;
}
#u328 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u328_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u329_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:12px;
}
#u329 {
  border-width:0px;
  position:absolute;
  left:745px;
  top:519px;
  width:24px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:12px;
}
#u329 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u329_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u330_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:24px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u330 {
  border-width:0px;
  position:absolute;
  left:679px;
  top:513px;
  width:48px;
  height:24px;
  display:flex;
}
#u330 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u330_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u331_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:12px;
}
#u331 {
  border-width:0px;
  position:absolute;
  left:691px;
  top:519px;
  width:24px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  line-height:12px;
}
#u331 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u331_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u332_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u332 {
  border-width:0px;
  position:absolute;
  left:625px;
  top:513px;
  width:48px;
  height:24px;
  display:flex;
}
#u332 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u332_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u333_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u333 {
  border-width:0px;
  position:absolute;
  left:637px;
  top:519px;
  width:24px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  line-height:12px;
}
#u333 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u333_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u334 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u335_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1154px;
  height:1px;
  background:inherit;
  background-color:rgba(230, 230, 230, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u335 {
  border-width:0px;
  position:absolute;
  left:306px;
  top:296px;
  width:1154px;
  height:1px;
  display:flex;
}
#u335 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u335_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u336_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:2px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u336 {
  border-width:0px;
  position:absolute;
  left:315px;
  top:295px;
  width:48px;
  height:2px;
  display:flex;
}
#u336 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u336_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u337_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
}
#u337 {
  border-width:0px;
  position:absolute;
  left:314px;
  top:274px;
  width:82px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
}
#u337 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u337_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u338_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u338 {
  border-width:0px;
  position:absolute;
  left:613px;
  top:339px;
  width:168px;
  height:24px;
  display:flex;
}
#u338 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u338_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u339 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u340_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(217, 217, 217, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u340 {
  border-width:0px;
  position:absolute;
  left:613px;
  top:303px;
  width:168px;
  height:24px;
  display:flex;
}
#u340 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u340_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u341 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u342_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u342 {
  border-width:0px;
  position:absolute;
  left:759px;
  top:307px;
  width:16px;
  height:16px;
  display:flex;
}
#u342 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u342_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u343_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:6px;
}
#u343 {
  border-width:0px;
  position:absolute;
  left:762px;
  top:312px;
  width:9px;
  height:6px;
  display:flex;
}
#u343 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u343_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u344_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:left;
  line-height:12px;
}
#u344 {
  border-width:0px;
  position:absolute;
  left:619px;
  top:309px;
  width:36px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#000000;
  text-align:left;
  line-height:12px;
}
#u344 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u344_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u345_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u345 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:307px;
  width:48px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:right;
}
#u345 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u345_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u346 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u347_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:underline ;
  text-align:right;
}
#u347 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:342px;
  width:48px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-decoration:underline ;
  text-align:right;
}
#u347 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u347_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u348_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
  line-height:12px;
}
#u348 {
  border-width:0px;
  position:absolute;
  left:619px;
  top:345px;
  width:36px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
  line-height:12px;
}
#u348 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u348_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u349 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u350 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u351_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u351 {
  border-width:0px;
  position:absolute;
  left:541px;
  top:398px;
  width:16px;
  height:16px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
}
#u351 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u351_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u352_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:10px;
}
#u352 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:401px;
  width:12px;
  height:10px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
}
#u352 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u352_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u353 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u354_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u354 {
  border-width:0px;
  position:absolute;
  left:541px;
  top:470px;
  width:16px;
  height:16px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
}
#u354 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u354_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u355_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:10px;
}
#u355 {
  border-width:0px;
  position:absolute;
  left:543px;
  top:473px;
  width:12px;
  height:10px;
  display:flex;
  -webkit-transform:rotate(270deg);
  -moz-transform:rotate(270deg);
  -ms-transform:rotate(270deg);
  transform:rotate(270deg);
}
#u355 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u355_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u356_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u356 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:281px;
  width:25px;
  height:25px;
  display:flex;
}
#u356 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u356_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u357_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:82px;
}
#u357 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:200px;
  width:1px;
  height:81px;
  display:flex;
}
#u357 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u357_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u358 {
  border-width:0px;
  position:absolute;
  left:619px;
  top:-22px;
  width:1023px;
  height:234px;
}
#u359_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u359 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
  display:flex;
}
#u359 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u359_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u360_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:25px;
}
#u360 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:0px;
  width:77px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u360 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u360_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u361_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
}
#u361 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:0px;
  width:200px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u361 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u361_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u362_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u362 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:0px;
  width:323px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u362 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u362_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u363_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u363 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:0px;
  width:300px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u363 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u363_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u364_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u364 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:25px;
  width:123px;
  height:25px;
  display:flex;
}
#u364 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u364_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u365_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:25px;
}
#u365 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:25px;
  width:77px;
  height:25px;
  display:flex;
}
#u365 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u365_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u366_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
}
#u366 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:25px;
  width:200px;
  height:25px;
  display:flex;
}
#u366 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u366_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u367_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u367 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:25px;
  width:323px;
  height:25px;
  display:flex;
}
#u367 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u367_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u368_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u368 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:25px;
  width:300px;
  height:25px;
  display:flex;
}
#u368 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u368_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u369_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u369 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:123px;
  height:25px;
  display:flex;
}
#u369 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u369_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u370_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:25px;
}
#u370 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:50px;
  width:77px;
  height:25px;
  display:flex;
}
#u370 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u370_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u371_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
}
#u371 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:50px;
  width:200px;
  height:25px;
  display:flex;
}
#u371 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u371_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u372_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u372 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:50px;
  width:323px;
  height:25px;
  display:flex;
}
#u372 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u372_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u373_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u373 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
}
#u373 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u373_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u374_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u374 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:75px;
  width:123px;
  height:25px;
  display:flex;
}
#u374 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u374_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u375_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:25px;
}
#u375 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:75px;
  width:77px;
  height:25px;
  display:flex;
}
#u375 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u375_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u376_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
}
#u376 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:75px;
  width:200px;
  height:25px;
  display:flex;
}
#u376 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u376_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u377_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u377 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:75px;
  width:323px;
  height:25px;
  display:flex;
}
#u377 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u377_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u378_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u378 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:75px;
  width:300px;
  height:25px;
  display:flex;
}
#u378 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u378_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u379_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u379 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:123px;
  height:25px;
  display:flex;
}
#u379 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u379_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u380_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:25px;
}
#u380 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:100px;
  width:77px;
  height:25px;
  display:flex;
}
#u380 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u380_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u381_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
}
#u381 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:100px;
  width:200px;
  height:25px;
  display:flex;
}
#u381 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u381_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u382_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u382 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:100px;
  width:323px;
  height:25px;
  display:flex;
}
#u382 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u382_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u383_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u383 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:100px;
  width:300px;
  height:25px;
  display:flex;
}
#u383 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u383_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u384_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:34px;
}
#u384 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:125px;
  width:123px;
  height:34px;
  display:flex;
}
#u384 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u384_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u385_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:34px;
}
#u385 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:125px;
  width:77px;
  height:34px;
  display:flex;
}
#u385 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u385_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u386_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:34px;
}
#u386 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:125px;
  width:200px;
  height:34px;
  display:flex;
}
#u386 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u386_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u387_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:34px;
}
#u387 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:125px;
  width:323px;
  height:34px;
  display:flex;
}
#u387 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u387_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u388_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:34px;
}
#u388 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:125px;
  width:300px;
  height:34px;
  display:flex;
}
#u388 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u388_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u389_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u389 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:159px;
  width:123px;
  height:25px;
  display:flex;
}
#u389 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u389_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u390_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:25px;
}
#u390 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:159px;
  width:77px;
  height:25px;
  display:flex;
}
#u390 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u390_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u391_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
}
#u391 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:159px;
  width:200px;
  height:25px;
  display:flex;
}
#u391 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u391_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u392_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u392 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:159px;
  width:323px;
  height:25px;
  display:flex;
}
#u392 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u392_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u393_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u393 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:159px;
  width:300px;
  height:25px;
  display:flex;
}
#u393 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u393_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u394_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u394 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:184px;
  width:123px;
  height:25px;
  display:flex;
}
#u394 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u394_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u395_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:25px;
}
#u395 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:184px;
  width:77px;
  height:25px;
  display:flex;
}
#u395 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u395_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u396_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
}
#u396 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:184px;
  width:200px;
  height:25px;
  display:flex;
}
#u396 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u396_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u397_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u397 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:184px;
  width:323px;
  height:25px;
  display:flex;
}
#u397 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u397_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u398_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u398 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:184px;
  width:300px;
  height:25px;
  display:flex;
}
#u398 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u398_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u399_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u399 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:209px;
  width:123px;
  height:25px;
  display:flex;
}
#u399 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u399_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u400_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:77px;
  height:25px;
}
#u400 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:209px;
  width:77px;
  height:25px;
  display:flex;
}
#u400 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u400_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u401_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
}
#u401 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:209px;
  width:200px;
  height:25px;
  display:flex;
}
#u401 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u401_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u402_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u402 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:209px;
  width:323px;
  height:25px;
  display:flex;
}
#u402 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u402_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u403_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u403 {
  border-width:0px;
  position:absolute;
  left:723px;
  top:209px;
  width:300px;
  height:25px;
  display:flex;
}
#u403 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u403_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u404_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u404 {
  border-width:0px;
  position:absolute;
  left:781px;
  top:515px;
  width:25px;
  height:25px;
  display:flex;
}
#u404 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u404_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u405_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:677px;
  height:2px;
}
#u405 {
  border-width:0px;
  position:absolute;
  left:803px;
  top:525px;
  width:676px;
  height:1px;
  display:flex;
}
#u405 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u405_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u406_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u406 {
  border-width:0px;
  position:absolute;
  left:1491px;
  top:520px;
  width:78px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u406 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u406_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u407_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:361px;
  height:75px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u407 {
  border-width:0px;
  position:absolute;
  left:1485px;
  top:550px;
  width:361px;
  height:75px;
  display:flex;
}
#u407 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u407_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u408 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u409_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(3, 125, 243, 1);
  border-radius:2px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u409 {
  border-width:0px;
  position:absolute;
  left:794px;
  top:304px;
  width:52px;
  height:24px;
  display:flex;
}
#u409 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u409_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u410_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
  line-height:12px;
}
#u410 {
  border-width:0px;
  position:absolute;
  left:816px;
  top:310px;
  width:24px;
  height:12px;
  display:flex;
  opacity:0.9;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
  line-height:12px;
}
#u410 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u410_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u411 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u412_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u412 {
  border-width:0px;
  position:absolute;
  left:800px;
  top:310px;
  width:12px;
  height:12px;
  display:flex;
}
#u412 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u412_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u413_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u413 {
  border-width:0px;
  position:absolute;
  left:800px;
  top:310px;
  width:12px;
  height:12px;
  display:flex;
}
#u413 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u413_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u414_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u414 {
  border-width:0px;
  position:absolute;
  left:1878px;
  top:516px;
  width:36px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u414 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u414_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u415_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:361px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u415 {
  border-width:0px;
  position:absolute;
  left:1872px;
  top:546px;
  width:361px;
  height:60px;
  display:flex;
}
#u415 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u415_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u416_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u416 {
  border-width:0px;
  position:absolute;
  left:904px;
  top:306px;
  width:25px;
  height:25px;
  display:flex;
}
#u416 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u416_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u417_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:560px;
  height:2px;
}
#u417 {
  border-width:0px;
  position:absolute;
  left:926px;
  top:316px;
  width:559px;
  height:1px;
  display:flex;
}
#u417 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u417_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u418 {
  border-width:0px;
  position:absolute;
  left:1491px;
  top:276px;
  width:155px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u418 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u418_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u419_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:361px;
  height:75px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u419 {
  border-width:0px;
  position:absolute;
  left:1491px;
  top:303px;
  width:361px;
  height:75px;
  display:flex;
}
#u419 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u419_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u420_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u420 {
  border-width:0px;
  position:absolute;
  left:1872px;
  top:269px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u420 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u420_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u421 {
  border-width:0px;
  position:absolute;
  left:1872px;
  top:300px;
  width:400px;
  height:180px;
}
#u422_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u422 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
  display:flex;
}
#u422 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u422_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u423_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u423 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:0px;
  width:120px;
  height:30px;
  display:flex;
}
#u423 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u423_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u424_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
}
#u424 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:0px;
  width:80px;
  height:30px;
  display:flex;
}
#u424 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u424_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u425_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u425 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:0px;
  width:100px;
  height:30px;
  display:flex;
}
#u425 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u425_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u426_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u426 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:100px;
  height:30px;
  display:flex;
}
#u426 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u426_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u427_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u427 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:30px;
  width:120px;
  height:30px;
  display:flex;
}
#u427 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u427_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u428_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
}
#u428 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:30px;
  width:80px;
  height:30px;
  display:flex;
}
#u428 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u428_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u429_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u429 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:30px;
  width:100px;
  height:30px;
  display:flex;
}
#u429 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u429_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u430_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u430 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:100px;
  height:30px;
  display:flex;
}
#u430 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u430_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u431_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u431 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:60px;
  width:120px;
  height:30px;
  display:flex;
}
#u431 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u431_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u432_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
}
#u432 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:60px;
  width:80px;
  height:30px;
  display:flex;
}
#u432 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u432_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u433_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u433 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:60px;
  width:100px;
  height:30px;
  display:flex;
}
#u433 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u433_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u434_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u434 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:90px;
  width:100px;
  height:30px;
  display:flex;
}
#u434 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u434_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u435_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u435 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:90px;
  width:120px;
  height:30px;
  display:flex;
}
#u435 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u435_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u436_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
}
#u436 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:90px;
  width:80px;
  height:30px;
  display:flex;
}
#u436 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u436_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u437_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u437 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:90px;
  width:100px;
  height:30px;
  display:flex;
}
#u437 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u437_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u438_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u438 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:100px;
  height:30px;
  display:flex;
}
#u438 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u438_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u439_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u439 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:120px;
  width:120px;
  height:30px;
  display:flex;
}
#u439 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u439_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u440_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
}
#u440 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:120px;
  width:80px;
  height:30px;
  display:flex;
}
#u440 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u440_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u441_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u441 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:120px;
  width:100px;
  height:30px;
  display:flex;
}
#u441 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u441_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u442_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u442 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:150px;
  width:100px;
  height:30px;
  display:flex;
}
#u442 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u442_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u443_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u443 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:150px;
  width:120px;
  height:30px;
  display:flex;
}
#u443 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u443_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u444_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:30px;
}
#u444 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:150px;
  width:80px;
  height:30px;
  display:flex;
}
#u444 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u444_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u445_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u445 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:150px;
  width:100px;
  height:30px;
  display:flex;
}
#u445 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u445_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u446_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u446 {
  border-width:0px;
  position:absolute;
  left:855px;
  top:619px;
  width:25px;
  height:25px;
  display:flex;
}
#u446 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u446_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u447_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:626px;
  height:2px;
}
#u447 {
  border-width:0px;
  position:absolute;
  left:876px;
  top:669px;
  width:625px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(6.81722264305682deg);
  -moz-transform:rotate(6.81722264305682deg);
  -ms-transform:rotate(6.81722264305682deg);
  transform:rotate(6.81722264305682deg);
}
#u447 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u447_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u448_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u448 {
  border-width:0px;
  position:absolute;
  left:1504px;
  top:694px;
  width:323px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u448 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u448_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u449 {
  border-width:0px;
  position:absolute;
  left:1485px;
  top:729px;
  width:1105px;
  height:174px;
}
#u450_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u450 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
  display:flex;
}
#u450 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u450_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u451_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:359px;
  height:25px;
}
#u451 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:0px;
  width:359px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u451 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u451_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u452_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u452 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:0px;
  width:323px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u452 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u452_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u453_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u453 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:0px;
  width:300px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u453 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u453_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u454_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:24px;
}
#u454 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:25px;
  width:123px;
  height:24px;
  display:flex;
}
#u454 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u454_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u455_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:359px;
  height:24px;
}
#u455 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:25px;
  width:359px;
  height:24px;
  display:flex;
}
#u455 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u455_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u456_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:24px;
}
#u456 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:25px;
  width:323px;
  height:24px;
  display:flex;
}
#u456 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u456_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u457_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:24px;
}
#u457 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:25px;
  width:300px;
  height:24px;
  display:flex;
}
#u457 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u457_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u458_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u458 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:49px;
  width:123px;
  height:25px;
  display:flex;
}
#u458 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u458_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u459_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:359px;
  height:25px;
}
#u459 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:49px;
  width:359px;
  height:25px;
  display:flex;
}
#u459 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u459_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u460_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u460 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:49px;
  width:323px;
  height:25px;
  display:flex;
}
#u460 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u460_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u461_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u461 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:49px;
  width:300px;
  height:25px;
  display:flex;
}
#u461 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u461_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u462_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u462 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:74px;
  width:123px;
  height:25px;
  display:flex;
}
#u462 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u462_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u463_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:359px;
  height:25px;
}
#u463 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:74px;
  width:359px;
  height:25px;
  display:flex;
}
#u463 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u463_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u464_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u464 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:74px;
  width:323px;
  height:25px;
  display:flex;
}
#u464 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u464_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u465_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u465 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:74px;
  width:300px;
  height:25px;
  display:flex;
}
#u465 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u465_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u466_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u466 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:99px;
  width:123px;
  height:25px;
  display:flex;
}
#u466 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u466_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u467_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:359px;
  height:25px;
}
#u467 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:99px;
  width:359px;
  height:25px;
  display:flex;
}
#u467 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u467_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u468_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u468 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:99px;
  width:323px;
  height:25px;
  display:flex;
}
#u468 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u468_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u469_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u469 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:99px;
  width:300px;
  height:25px;
  display:flex;
}
#u469 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u469_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u470_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u470 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:124px;
  width:123px;
  height:25px;
  display:flex;
}
#u470 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u470_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u471_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:359px;
  height:25px;
}
#u471 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:124px;
  width:359px;
  height:25px;
  display:flex;
}
#u471 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u471_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u472_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u472 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:124px;
  width:323px;
  height:25px;
  display:flex;
}
#u472 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u473_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u473 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:124px;
  width:300px;
  height:25px;
  display:flex;
}
#u473 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u473_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u474_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:25px;
}
#u474 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:149px;
  width:123px;
  height:25px;
  display:flex;
}
#u474 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u474_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u475_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:359px;
  height:25px;
}
#u475 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:149px;
  width:359px;
  height:25px;
  display:flex;
}
#u475 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u475_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u476_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:323px;
  height:25px;
}
#u476 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:149px;
  width:323px;
  height:25px;
  display:flex;
}
#u476 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u476_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u477_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
}
#u477 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:149px;
  width:300px;
  height:25px;
  display:flex;
}
#u477 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u477_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u478_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u478 {
  border-width:0px;
  position:absolute;
  left:1498px;
  top:922px;
  width:114px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u478 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u478_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u479 {
  border-width:0px;
  position:absolute;
  left:1485px;
  top:956px;
  width:440px;
  height:363px;
}
#u480_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u480 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
  display:flex;
}
#u480 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u480_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u481_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u481 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:0px;
  width:120px;
  height:30px;
  display:flex;
}
#u481 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u481_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u482_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u482 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:0px;
  width:110px;
  height:30px;
  display:flex;
}
#u482 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u482_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u483_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u483 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:0px;
  width:110px;
  height:30px;
  display:flex;
}
#u483 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u483_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u484_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u484 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:100px;
  height:30px;
  display:flex;
}
#u484 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u484_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u485_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u485 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:30px;
  width:120px;
  height:30px;
  display:flex;
}
#u485 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u485_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u486_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u486 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:30px;
  width:110px;
  height:30px;
  display:flex;
}
#u486 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u486_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u487_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u487 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:30px;
  width:110px;
  height:30px;
  display:flex;
}
#u487 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u487_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u488_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u488 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:100px;
  height:30px;
  display:flex;
}
#u488 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u488_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u489_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u489 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:60px;
  width:120px;
  height:30px;
  display:flex;
}
#u489 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u489_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u490_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u490 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:60px;
  width:110px;
  height:30px;
  display:flex;
}
#u490 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u490_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u491_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u491 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:60px;
  width:110px;
  height:30px;
  display:flex;
}
#u491 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u491_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u492_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u492 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:90px;
  width:100px;
  height:30px;
  display:flex;
}
#u492 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u492_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u493_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u493 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:90px;
  width:120px;
  height:30px;
  display:flex;
}
#u493 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u493_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u494_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u494 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:90px;
  width:110px;
  height:30px;
  display:flex;
}
#u494 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u494_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u495_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u495 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:90px;
  width:110px;
  height:30px;
  display:flex;
}
#u495 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u495_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u496_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:33px;
}
#u496 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:100px;
  height:33px;
  display:flex;
}
#u496 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u496_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u497_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:33px;
}
#u497 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:120px;
  width:120px;
  height:33px;
  display:flex;
}
#u497 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u497_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u498_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:33px;
}
#u498 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:120px;
  width:110px;
  height:33px;
  display:flex;
}
#u498 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u498_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u499_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:33px;
}
#u499 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:120px;
  width:110px;
  height:33px;
  display:flex;
}
#u499 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u499_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u500_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u500 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:153px;
  width:100px;
  height:30px;
  display:flex;
}
#u500 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u500_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u501_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u501 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:153px;
  width:120px;
  height:30px;
  display:flex;
}
#u501 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u501_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u502_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u502 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:153px;
  width:110px;
  height:30px;
  display:flex;
}
#u502 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u502_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u503_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u503 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:153px;
  width:110px;
  height:30px;
  display:flex;
}
#u503 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u503_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u504_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u504 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:183px;
  width:100px;
  height:30px;
  display:flex;
}
#u504 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u504_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u505_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u505 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:183px;
  width:120px;
  height:30px;
  display:flex;
}
#u505 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u505_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u506_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u506 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:183px;
  width:110px;
  height:30px;
  display:flex;
}
#u506 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u506_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u507_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u507 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:183px;
  width:110px;
  height:30px;
  display:flex;
}
#u507 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u507_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u508_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u508 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:213px;
  width:100px;
  height:30px;
  display:flex;
}
#u508 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u508_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u509_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u509 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:213px;
  width:120px;
  height:30px;
  display:flex;
}
#u509 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u509_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u510_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u510 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:213px;
  width:110px;
  height:30px;
  display:flex;
}
#u510 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u510_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u511_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u511 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:213px;
  width:110px;
  height:30px;
  display:flex;
}
#u511 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u511_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u512_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u512 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:243px;
  width:100px;
  height:30px;
  display:flex;
}
#u512 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u512_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u513_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u513 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:243px;
  width:120px;
  height:30px;
  display:flex;
}
#u513 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u513_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u514_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u514 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:243px;
  width:110px;
  height:30px;
  display:flex;
}
#u514 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u514_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u515_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u515 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:243px;
  width:110px;
  height:30px;
  display:flex;
}
#u515 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u515_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u516_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u516 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:273px;
  width:100px;
  height:30px;
  display:flex;
}
#u516 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u516_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u517_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u517 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:273px;
  width:120px;
  height:30px;
  display:flex;
}
#u517 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u517_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u518_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u518 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:273px;
  width:110px;
  height:30px;
  display:flex;
}
#u518 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u518_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u519_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u519 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:273px;
  width:110px;
  height:30px;
  display:flex;
}
#u519 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u519_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u520_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u520 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:303px;
  width:100px;
  height:30px;
  display:flex;
}
#u520 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u520_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u521_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u521 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:303px;
  width:120px;
  height:30px;
  display:flex;
}
#u521 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u521_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u522_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u522 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:303px;
  width:110px;
  height:30px;
  display:flex;
}
#u522 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u522_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u523_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u523 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:303px;
  width:110px;
  height:30px;
  display:flex;
}
#u523 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u523_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u524_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u524 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:333px;
  width:100px;
  height:30px;
  display:flex;
}
#u524 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u524_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u525_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u525 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:333px;
  width:120px;
  height:30px;
  display:flex;
}
#u525 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u525_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u526_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u526 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:333px;
  width:110px;
  height:30px;
  display:flex;
}
#u526 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u526_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u527_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
}
#u527 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:333px;
  width:110px;
  height:30px;
  display:flex;
}
#u527 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u527_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u528_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:361px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u528 {
  border-width:0px;
  position:absolute;
  left:1630px;
  top:925px;
  width:361px;
  height:15px;
  display:flex;
}
#u528 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u528_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u529_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u529 {
  border-width:0px;
  position:absolute;
  left:1347px;
  top:757px;
  width:25px;
  height:25px;
  display:flex;
}
#u529 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u529_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u530_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:2px;
}
#u530 {
  border-width:0px;
  position:absolute;
  left:1250px;
  top:893px;
  width:219px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u530 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u530_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u531_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:240px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u531 {
  border-width:0px;
  position:absolute;
  left:1219px;
  top:1043px;
  width:240px;
  height:30px;
  display:flex;
}
#u531 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u531_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u532_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u532 {
  border-width:0px;
  position:absolute;
  left:1219px;
  top:1012px;
  width:78px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u532 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u532_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u533_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u533 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:643px;
  width:25px;
  height:25px;
  display:flex;
}
#u533 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u533_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u534_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:310px;
  height:2px;
}
#u534 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:824px;
  width:309px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u534 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u534_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u535_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:299px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u535 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:990px;
  width:299px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u535 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u535_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u536_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u536 {
  border-width:0px;
  position:absolute;
  left:306px;
  top:1077px;
  width:87px;
  height:35px;
  display:flex;
}
#u536 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u536_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u537_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u537 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:1077px;
  width:158px;
  height:35px;
  display:flex;
}
#u537 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u537_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u538_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u538 {
  border-width:0px;
  position:absolute;
  left:551px;
  top:1077px;
  width:159px;
  height:35px;
  display:flex;
}
#u538 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u538_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u539_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u539 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:1023px;
  width:118px;
  height:35px;
  display:flex;
}
#u539 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u539_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u540_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:35px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u540 {
  border-width:0px;
  position:absolute;
  left:449px;
  top:1023px;
  width:385px;
  height:35px;
  display:flex;
}
#u540 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u540_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u541_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u541 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:32px;
  width:78px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u541 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u541_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u542_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:361px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u542 {
  border-width:0px;
  position:absolute;
  left:203px;
  top:59px;
  width:361px;
  height:15px;
  display:flex;
}
#u542 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u542_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u543_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u543 {
  border-width:0px;
  position:absolute;
  left:522px;
  top:553px;
  width:25px;
  height:25px;
  display:flex;
}
#u543 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u543_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u544 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u545_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u545 {
  border-width:0px;
  position:absolute;
  left:1345px;
  top:1225px;
  width:24px;
  height:24px;
  display:flex;
}
#u545 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u545_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u546_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u546 {
  border-width:0px;
  position:absolute;
  left:1352px;
  top:1232px;
  width:11px;
  height:11px;
  display:flex;
}
#u546 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u546_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u547_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u547 {
  border-width:0px;
  position:absolute;
  left:1368px;
  top:1225px;
  width:24px;
  height:24px;
  display:flex;
}
#u547 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u547_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u548_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u548 {
  border-width:0px;
  position:absolute;
  left:1374px;
  top:1231px;
  width:12px;
  height:12px;
  display:flex;
}
#u548 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u548_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u549_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(221, 221, 221, 1);
  border-radius:2px;
  border-top-left-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u549 {
  border-width:0px;
  position:absolute;
  left:1391px;
  top:1225px;
  width:38px;
  height:24px;
  display:flex;
}
#u549 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u549_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u550 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u551_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:13px;
}
#u551 {
  border-width:0px;
  position:absolute;
  left:1397px;
  top:1231px;
  width:12px;
  height:13px;
  display:flex;
}
#u551 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u551_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u552 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u553_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
  background:inherit;
  background-color:rgba(213, 247, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u553 {
  border-width:0px;
  position:absolute;
  left:1411px;
  top:1231px;
  width:12px;
  height:12px;
  display:flex;
  opacity:0;
}
#u553 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u553_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u554_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:7px;
}
#u554 {
  border-width:0px;
  position:absolute;
  left:1413px;
  top:1232px;
  width:7px;
  height:7px;
  display:flex;
  -webkit-transform:rotate(315deg);
  -moz-transform:rotate(315deg);
  -ms-transform:rotate(315deg);
  transform:rotate(315deg);
}
#u554 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u554_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u555 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u556_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1154px;
  height:1px;
  background:inherit;
  background-color:rgba(230, 230, 230, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u556 {
  border-width:0px;
  position:absolute;
  left:279px;
  top:1215px;
  width:1154px;
  height:1px;
  display:flex;
}
#u556 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u556_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u557 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u558_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(213, 247, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u558 {
  border-width:0px;
  position:absolute;
  left:1411px;
  top:1194px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u558 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u558_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u559_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u559 {
  border-width:0px;
  position:absolute;
  left:1412px;
  top:1195px;
  width:14px;
  height:14px;
  display:flex;
}
#u559 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u559_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u560 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u561_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u561 {
  border-width:0px;
  position:absolute;
  left:1389px;
  top:1194px;
  width:16px;
  height:16px;
  display:flex;
  opacity:0;
}
#u561 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u561_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u562 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u563_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:5px;
}
#u563 {
  border-width:0px;
  position:absolute;
  left:1390px;
  top:1204px;
  width:13px;
  height:5px;
  display:flex;
}
#u563 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u563_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u564 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u565_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:5px;
}
#u565 {
  border-width:0px;
  position:absolute;
  left:1391px;
  top:1195px;
  width:13px;
  height:5px;
  display:flex;
}
#u565 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u565_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u566_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:20px;
}
#u566 {
  border-width:0px;
  position:absolute;
  left:295px;
  top:1192px;
  width:72px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:20px;
}
#u566 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u566_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u567_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:14px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u567 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:1195px;
  width:4px;
  height:14px;
  display:flex;
}
#u567 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u567_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u568_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
  line-height:20px;
}
#u568 {
  border-width:0px;
  position:absolute;
  left:387px;
  top:1192px;
  width:96px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  color:#037DF3;
  text-align:left;
  line-height:20px;
}
#u568 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u568_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u569_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:14px;
  background:inherit;
  background-color:rgba(3, 125, 243, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u569 {
  border-width:0px;
  position:absolute;
  left:377px;
  top:1195px;
  width:4px;
  height:14px;
  display:flex;
}
#u569 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u569_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u570 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u571_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:238px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:2px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u571 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1255px;
  width:1141px;
  height:238px;
  display:flex;
}
#u571 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u571_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u572_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:24px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u572 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1300px;
  width:1141px;
  height:24px;
  display:flex;
}
#u572 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u572_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u573_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u573 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1324px;
  width:1142px;
  height:1px;
  display:flex;
}
#u573 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u573_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u574_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:24px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u574 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:1349px;
  width:1141px;
  height:24px;
  display:flex;
}
#u574 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u574_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u575_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:24px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u575 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1397px;
  width:1141px;
  height:24px;
  display:flex;
}
#u575 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u575_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u576_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:24px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u576 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1446px;
  width:1141px;
  height:24px;
  display:flex;
}
#u576 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u576_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u577_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:20px;
  background:inherit;
  background-color:rgba(247, 247, 247, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u577 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1495px;
  width:1141px;
  height:20px;
  display:flex;
}
#u577 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u577_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u578_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:28px;
  background:inherit;
  background-color:rgba(243, 243, 243, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(230, 230, 230, 1);
  border-radius:2px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u578 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1249px;
  width:1142px;
  height:28px;
  display:flex;
}
#u578 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u579_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u579 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1300px;
  width:1142px;
  height:1px;
  display:flex;
}
#u579 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u580_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u580 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1396px;
  width:1142px;
  height:1px;
  display:flex;
}
#u580 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u580_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u581_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u581 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1420px;
  width:1142px;
  height:1px;
  display:flex;
}
#u581 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u581_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u582_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u582 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1348px;
  width:1142px;
  height:1px;
  display:flex;
}
#u582 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u582_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u583_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u583 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1372px;
  width:1142px;
  height:1px;
  display:flex;
}
#u583 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u583_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u584_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u584 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1445px;
  width:1141px;
  height:1px;
  display:flex;
}
#u584 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u584_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u585_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u585 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1469px;
  width:1141px;
  height:1px;
  display:flex;
}
#u585 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u585_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u586_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1141px;
  height:1px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u586 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:1495px;
  width:1141px;
  height:1px;
  display:flex;
}
#u586 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u586_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u587_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u587 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:1257px;
  width:53px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u587 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u587_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u588_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u588 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:1257px;
  width:53px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u588 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u588_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u589_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u589 {
  border-width:0px;
  position:absolute;
  left:547px;
  top:1257px;
  width:49px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u589 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u589_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u590_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u590 {
  border-width:0px;
  position:absolute;
  left:634px;
  top:1257px;
  width:51px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u590 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u590_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u591_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u591 {
  border-width:0px;
  position:absolute;
  left:816px;
  top:1257px;
  width:51px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u591 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u591_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u592_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u592 {
  border-width:0px;
  position:absolute;
  left:906px;
  top:1257px;
  width:51px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u592 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u592_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u593_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u593 {
  border-width:0px;
  position:absolute;
  left:271px;
  top:1257px;
  width:48px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u593 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u593_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u594_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u594 {
  border-width:0px;
  position:absolute;
  left:271px;
  top:1278px;
  width:49px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u594 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u594_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u595_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u595 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:1278px;
  width:49px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u595 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u595_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u596_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u596 {
  border-width:0px;
  position:absolute;
  left:450px;
  top:1278px;
  width:49px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u596 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u596_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u597_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u597 {
  border-width:0px;
  position:absolute;
  left:547px;
  top:1278px;
  width:42px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u597 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u597_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u598_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:170px;
  height:285px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u598 {
  border-width:0px;
  position:absolute;
  left:634px;
  top:1278px;
  width:170px;
  height:285px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u598 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u598_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u599_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:288px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u599 {
  border-width:0px;
  position:absolute;
  left:816px;
  top:1278px;
  width:42px;
  height:288px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u599 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u599_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u600_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u600 {
  border-width:0px;
  position:absolute;
  left:906px;
  top:1278px;
  width:66px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u600 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u600_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u601_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u601 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:1250px;
  width:1px;
  height:27px;
  display:flex;
}
#u601 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u602_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u602 {
  border-width:0px;
  position:absolute;
  left:535px;
  top:1250px;
  width:1px;
  height:27px;
  display:flex;
}
#u602 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u602_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u603_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u603 {
  border-width:0px;
  position:absolute;
  left:351px;
  top:1250px;
  width:1px;
  height:27px;
  display:flex;
}
#u603 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u603_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u604_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u604 {
  border-width:0px;
  position:absolute;
  left:622px;
  top:1250px;
  width:1px;
  height:27px;
  display:flex;
}
#u604 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u604_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u605_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u605 {
  border-width:0px;
  position:absolute;
  left:798px;
  top:1250px;
  width:1px;
  height:27px;
  display:flex;
}
#u605 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u605_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u606_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u606 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:1250px;
  width:1px;
  height:27px;
  display:flex;
}
#u606 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u606_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u607_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u607 {
  border-width:0px;
  position:absolute;
  left:982px;
  top:1250px;
  width:1px;
  height:27px;
  display:flex;
}
#u607 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u607_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u608_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u608 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:1250px;
  width:1px;
  height:27px;
  display:flex;
}
#u608 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u608_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u609_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1142px;
  height:212px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(230, 230, 230, 1);
  border-radius:2px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u609 {
  border-width:0px;
  position:absolute;
  left:271px;
  top:1321px;
  width:1142px;
  height:212px;
  display:flex;
}
#u609 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u609_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u610_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u610 {
  border-width:0px;
  position:absolute;
  left:1012px;
  top:1257px;
  width:51px;
  height:12px;
  display:flex;
  font-family:'PingFangSC-Medium', 'PingFang SC Medium', 'PingFang SC', sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:12px;
}
#u610 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u610_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u611_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:27px;
}
#u611 {
  border-width:0px;
  position:absolute;
  left:1206px;
  top:1250px;
  width:1px;
  height:27px;
  display:flex;
}
#u611 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u611_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u612_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:237px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u612 {
  border-width:0px;
  position:absolute;
  left:991px;
  top:1277px;
  width:66px;
  height:237px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
  line-height:24px;
}
#u612 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u612_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u613_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:628px;
  height:2px;
}
#u613 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:891px;
  width:627px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u613 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u613_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u614 {
  border-width:0px;
  position:absolute;
  left:293px;
  top:1565px;
  width:717px;
  height:281px;
}
#u615_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u615 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
  display:flex;
}
#u615 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u615_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u616_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u616 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:0px;
  width:120px;
  height:30px;
  display:flex;
}
#u616 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u616_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u617_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:497px;
  height:30px;
}
#u617 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:0px;
  width:497px;
  height:30px;
  display:flex;
}
#u617 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u617_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u618_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u618 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:100px;
  height:30px;
  display:flex;
}
#u618 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u618_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u619_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u619 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:30px;
  width:120px;
  height:30px;
  display:flex;
}
#u619 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u619_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u620_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:497px;
  height:30px;
}
#u620 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:30px;
  width:497px;
  height:30px;
  display:flex;
}
#u620 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u620_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u621_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u621 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:100px;
  height:30px;
  display:flex;
}
#u621 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u621_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u622_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u622 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:60px;
  width:120px;
  height:30px;
  display:flex;
}
#u622 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u622_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u623_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:497px;
  height:30px;
}
#u623 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:60px;
  width:497px;
  height:30px;
  display:flex;
}
#u623 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u623_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u624_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u624 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:90px;
  width:100px;
  height:30px;
  display:flex;
}
#u624 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u624_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u625_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u625 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:90px;
  width:120px;
  height:30px;
  display:flex;
}
#u625 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u625_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u626_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:497px;
  height:30px;
}
#u626 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:90px;
  width:497px;
  height:30px;
  display:flex;
}
#u626 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u626_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u627_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:33px;
}
#u627 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:100px;
  height:33px;
  display:flex;
}
#u627 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u627_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u628_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:33px;
}
#u628 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:120px;
  width:120px;
  height:33px;
  display:flex;
}
#u628 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u628_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u629_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:497px;
  height:33px;
}
#u629 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:120px;
  width:497px;
  height:33px;
  display:flex;
}
#u629 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u629_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u630_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u630 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:153px;
  width:100px;
  height:34px;
  display:flex;
}
#u630 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u630_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u631_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:34px;
}
#u631 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:153px;
  width:120px;
  height:34px;
  display:flex;
}
#u631 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u631_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u632_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:497px;
  height:34px;
}
#u632 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:153px;
  width:497px;
  height:34px;
  display:flex;
}
#u632 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u632_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u633_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:34px;
}
#u633 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:187px;
  width:100px;
  height:34px;
  display:flex;
}
#u633 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u633_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u634_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:34px;
}
#u634 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:187px;
  width:120px;
  height:34px;
  display:flex;
}
#u634 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u634_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u635_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:497px;
  height:34px;
}
#u635 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:187px;
  width:497px;
  height:34px;
  display:flex;
}
#u635 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u635_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u636_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u636 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:221px;
  width:100px;
  height:30px;
  display:flex;
}
#u636 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u636_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u637_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u637 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:221px;
  width:120px;
  height:30px;
  display:flex;
}
#u637 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u637_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u638_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:497px;
  height:30px;
}
#u638 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:221px;
  width:497px;
  height:30px;
  display:flex;
}
#u638 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u638_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u639_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u639 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:251px;
  width:100px;
  height:30px;
  display:flex;
}
#u639 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u639_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u640_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
}
#u640 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:251px;
  width:120px;
  height:30px;
  display:flex;
}
#u640 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u640_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u641_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:497px;
  height:30px;
}
#u641 {
  border-width:0px;
  position:absolute;
  left:220px;
  top:251px;
  width:497px;
  height:30px;
  display:flex;
}
#u641 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u641_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u642_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u642 {
  border-width:0px;
  position:absolute;
  left:242px;
  top:1164px;
  width:144px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u642 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u642_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
