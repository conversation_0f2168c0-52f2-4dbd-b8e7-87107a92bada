from fastapi import FastAPI
from fastmcp import MCP, MCPConfig  # 修改这行
import uvicorn

# 创建FastAPI应用
app = FastAPI()

# 配置MCP
config = MCPConfig(
    name="calculator",
    description="A simple calculator MCP service",
    version="1.0.0",
    stream=True  # 启用SSE支持
)

# 创建MCP实例
mcp = MCP(app, config)

@mcp.tool()
async def add(a: float, b: float) -> float:
    """
    计算两个数的和
    
    Args:
        a: 第一个数
        b: 第二个数
        
    Returns:
        两个数的和
    """
    return a + b

@mcp.tool()
async def subtract(a: float, b: float) -> float:
    """
    计算两个数的差
    
    Args:
        a: 被减数
        b: 减数
        
    Returns:
        两个数的差
    """
    return a - b

@mcp.tool()
async def multiply(a: float, b: float) -> float:
    """
    计算两个数的积
    
    Args:
        a: 第一个数
        b: 第二个数
        
    Returns:
        两个数的积
    """
    return a * b

@mcp.tool()
async def divide(a: float, b: float) -> float:
    """
    计算两个数的商
    
    Args:
        a: 被除数
        b: 除数
        
    Returns:
        两个数的商
    
    Raises:
        ValueError: 当除数为0时抛出异常
    """
    if b == 0:
        raise ValueError("除数不能为0")
    return a / b

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)