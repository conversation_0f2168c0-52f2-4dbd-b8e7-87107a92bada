# 输入控制系统集成测试
import unittest
import pygame
import time
from input_manager import InputManager
from board import Board
from tetromino import Tetromino
from score_manager import ScoreManager

class TestInputIntegration(unittest.TestCase):
    """输入控制系统集成测试"""
    
    def setUp(self):
        """测试前的设置"""
        pygame.init()
        self.input_manager = InputManager()
        self.board = Board()
        self.score_manager = ScoreManager()
        self.current_piece = Tetromino('T')
        self.current_x = 5
        self.current_y = 5
    
    def tearDown(self):
        """测试后的清理"""
        pygame.quit()
    
    def create_key_event(self, key, event_type):
        """创建键盘事件"""
        return pygame.event.Event(event_type, key=key)
    
    def test_left_movement_integration(self):
        """测试左移动集成"""
        events = [self.create_key_event(pygame.K_LEFT, pygame.KEYDOWN)]
        self.input_manager.update(events)
        
        movement = self.input_manager.get_movement_input()
        self.assertTrue(movement['move_left'])
        
        if movement['move_left']:
            if self.board.can_move_left(self.current_piece, self.current_x, self.current_y):
                self.current_x -= 1
        
        self.assertEqual(self.current_x, 4)
    
    def test_collision_prevention(self):
        """测试碰撞阻止功能"""
        self.current_x = 0
        
        events = [self.create_key_event(pygame.K_LEFT, pygame.KEYDOWN)]
        self.input_manager.update(events)
        
        movement = self.input_manager.get_movement_input()
        self.assertTrue(movement['move_left'])
        
        if movement['move_left']:
            if self.board.can_move_left(self.current_piece, self.current_x, self.current_y):
                self.current_x -= 1
        
        self.assertEqual(self.current_x, 0)
    
    def test_input_responsiveness(self):
        """测试输入响应性"""
        start_time = time.time()
        
        events = [self.create_key_event(pygame.K_LEFT, pygame.KEYDOWN)]
        self.input_manager.update(events)
        movement = self.input_manager.get_movement_input()
        
        end_time = time.time()
        response_time = end_time - start_time
        
        self.assertLess(response_time, 0.001)
        self.assertTrue(movement['move_left'])

if __name__ == '__main__':
    unittest.main()