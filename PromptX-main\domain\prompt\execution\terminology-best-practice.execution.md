<execution domain="prompt-engineering">
  <guideline>
    # 术语定义最佳实践

    1. **术语原子化**：每个术语定义应聚焦单一概念，不包含多个关联但独立的概念

    2. **术语命名**：
       - 中文名称应简洁明确，避免生僻词
       - 英文名称使用专业术语，遵循行业惯例
       - 保持中英文名称的语义一致性

    3. **定义清晰性**：
       - 定义应精确、简洁，避免循环定义
       - 可同时包含AI理解和系统实现两个层面
       - 描述应独立自足，不依赖特定上下文即可理解

    4. **示例具体化**：
       - 提供具体、有代表性的示例
       - 示例应涵盖典型使用场景
       - 复杂概念可提供多个示例说明不同方面

    5. **引用规范**：
       - 术语后推荐使用空格与后续内容分隔，增强可读性
       - 如果术语本身包含空格，仍可正常使用：`#用户故事`
       - 在同一文档中保持术语引用的一致性

    6. **维护更新**：
       - 修改文档时同步更新相关术语
       - 保持术语定义与文档内容的一致性
       - 术语变更时考虑向后兼容性
  </guideline>
</execution> 