from fastmcp import FastMCP
from datetime import datetime
import time
import json

app = FastMCP("DateTimeServer")

@app.tool()
def get_date():
    """Get current date in YYYY-MM-DD format,获取当前日期"""
    return datetime.now().strftime("%Y-%m-%d")

@app.tool()
def get_time():
    """Get current time in HH:MM:SS format,获取当前时间"""
    return datetime.now().strftime("%H:%M:%S")

@app.tool()
def subscribe_time(interval: int = 1):
    """Subscribe to real-time time updates using SSE
    Args:
        interval (int): Update interval in seconds
        订阅实时时间更新
    """
    while True:
        current_time = datetime.now().strftime("%H:%M:%S")
        current_date = datetime.now().strftime("%Y-%m-%d")
        data = {
            "time": current_time,
            "date": current_date
        }
        yield f"data: {json.dumps(data)}\n\n"
        time.sleep(interval)

def main():
    app.run(transport='sse')

if __name__ == "__main__":
    main()
