# 输入控制系统演示程序
import pygame
import sys
from input_manager import InputManager
from ui import UI
from board import Board
from tetromino import Tetromino
from score_manager import ScoreManager
from config import GAME_CONFIG

def main():
    """输入控制演示主程序"""
    # 初始化pygame
    pygame.init()
    
    # 创建屏幕
    screen = pygame.display.set_mode((GAME_CONFIG['WINDOW_WIDTH'], GAME_CONFIG['WINDOW_HEIGHT']))
    pygame.display.set_caption("俄罗斯方块 - 输入控制演示")
    
    # 创建游戏组件
    input_manager = InputManager()
    ui = UI(screen)
    board = Board()
    score_manager = ScoreManager()
    
    # 创建当前方块
    current_piece = Tetromino()
    current_x = GAME_CONFIG['BOARD_WIDTH'] // 2 - 2
    current_y = 0
    
    # 创建下一个方块
    next_piece = Tetromino()
    
    clock = pygame.time.Clock()
    paused = False
    game_over = False
    
    print("输入控制演示程序启动")
    print("控制说明:")
    print("- 左右箭头键: 移动方块")
    print("- 上箭头键/空格键: 旋转方块")
    print("- 下箭头键: 软下落")
    print("- P键: 暂停/恢复游戏")
    print("- R键: 重新开始")
    print("- ESC键: 退出程序")
    
    running = True
    while running:
        # 获取事件
        events = pygame.event.get()
        
        # 处理退出事件
        for event in events:
            if event.type == pygame.QUIT:
                running = False
        
        # 更新输入管理器
        input_manager.update(events)
        
        # 获取输入状态
        movement = input_manager.get_movement_input()
        
        # 处理退出
        if movement['quit']:
            running = False
            continue
        
        # 处理暂停
        if movement['pause']:
            paused = not paused
            print(f"游戏{'暂停' if paused else '恢复'}")
        
        # 处理重启
        if movement['restart']:
            if game_over:
                # 重置游戏状态
                board.clear_board()
                score_manager.reset()
                current_piece = Tetromino()
                next_piece = Tetromino()
                current_x = GAME_CONFIG['BOARD_WIDTH'] // 2 - 2
                current_y = 0
                game_over = False
                paused = False
                print("游戏重新开始")
        
        # 如果游戏暂停或结束，跳过游戏逻辑
        if paused or game_over:
            pass
        else:
            # 处理方块移动
            if movement['move_left']:
                if board.can_move_left(current_piece, current_x, current_y):
                    current_x -= 1
                    print("方块向左移动")
            
            if movement['move_right']:
                if board.can_move_right(current_piece, current_x, current_y):
                    current_x += 1
                    print("方块向右移动")
            
            # 处理方块旋转
            if movement['rotate']:
                if board.can_rotate(current_piece, current_x, current_y):
                    current_piece.rotate()
                    print("方块旋转")
                else:
                    # 尝试踢墙
                    success, new_x, new_y = board.try_wall_kick(current_piece, current_x, current_y)
                    if success:
                        current_piece.rotate()
                        current_x, current_y = new_x, new_y
                        print("方块旋转（踢墙）")
            
            # 处理软下落
            if movement['soft_drop']:
                if board.can_move_down(current_piece, current_x, current_y):
                    current_y += 1
                    score_manager.add_drop_score(1, is_hard_drop=False)
                    print("软下落")
            
            # 处理硬下落
            if movement['hard_drop']:
                drop_distance = 0
                while board.can_move_down(current_piece, current_x, current_y):
                    current_y += 1
                    drop_distance += 1
                
                if drop_distance > 0:
                    score_manager.add_drop_score(drop_distance, is_hard_drop=True)
                    print(f"硬下落 {drop_distance} 格")
            
            # 检查方块是否需要固定
            if not board.can_move_down(current_piece, current_x, current_y):
                # 放置方块
                if board.place_tetromino(current_piece, current_x, current_y):
                    score_manager.add_placement_bonus()
                    
                    # 清除行
                    lines_cleared, cleared_lines = board.clear_lines()
                    if lines_cleared > 0:
                        earned_score = score_manager.add_line_score(lines_cleared)
                        print(f"消除 {lines_cleared} 行，获得 {earned_score} 分")
                    
                    # 生成新方块
                    current_piece = next_piece
                    next_piece = Tetromino()
                    current_x = GAME_CONFIG['BOARD_WIDTH'] // 2 - 2
                    current_y = 0
                    
                    # 检查游戏结束
                    if board.is_game_over(current_piece, current_x, current_y):
                        game_over = True
                        score_manager.save_high_score()
                        print("游戏结束！")
                else:
                    # 无法放置方块，游戏结束
                    game_over = True
                    score_manager.save_high_score()
                    print("游戏结束！")
        
        # 绘制游戏
        ui.draw_background()
        ui.draw_title()
        ui.draw_board(board)
        
        if not game_over:
            ui.draw_tetromino(current_piece, current_x, current_y)
            ui.draw_ghost_piece(current_piece, current_x, current_y, board)
        
        ui.draw_score_panel(score_manager)
        ui.draw_level_progress_bar(score_manager)
        ui.draw_next_piece(next_piece)
        ui.draw_controls_help()
        
        # 绘制状态界面
        if paused:
            ui.draw_pause_screen()
        elif game_over:
            ui.draw_game_over_screen(score_manager.get_current_score())
        
        # 显示调试信息
        debug_info = input_manager.get_debug_info()
        if debug_info['keys_pressed']:
            debug_text = f"按键: {', '.join(debug_info['keys_pressed'])}"
            font = pygame.font.Font(None, 20)
            text_surface = font.render(debug_text, True, (255, 255, 255))
            screen.blit(text_surface, (10, 10))
        
        # 更新显示
        pygame.display.flip()
        clock.tick(GAME_CONFIG['FPS'])
    
    pygame.quit()
    print("输入控制演示程序结束")

if __name__ == "__main__":
    main()