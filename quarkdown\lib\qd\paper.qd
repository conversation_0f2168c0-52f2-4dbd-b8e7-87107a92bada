.localization {paper}
    - English
        - abstract: Abstract
        - definition: Definition
        - lemma: Lemma
        - theorem: Theorem
        - proof: Proof
    - Italian
        - abstract: Sommario
        - definition: Definizione
        - lemma: Lemma
        - theorem: Teorema
        - proof: Dimostrazione
    - German
        - abstract: Abstract
        - definition: Definition
        - lemma: Lemma
        - theorem: Theorem
        - proof: Beweis
    - Japanese
        - abstract: 概要
        - definition: 定義
        - lemma: 補題
        - theorem: 定理
        - proof: 証明
    - Chinese
        - abstract: 摘要
        - definition: 定义
        - lemma: 引理
        - theorem: 定理
        - proof: 证明

<!-- Alignment of the 'Abstract' title, relative to its body content -->

.var {abstractalignment} {center}

<!-- The suffix that follows the title of a block, e.g. "Definition", "Lemma", ... -->

.var {paperblocksuffix} {\.}

<!-- Content at the end of a proof block -->

.var {proofend} {∎}

<!-- An 'abstract' block -->

.function {abstract}
    content:
    .container padding:{0 1cm} fullwidth:{yes}
        .align {.abstractalignment}
            ####! .localize {paper:abstract}

        .container padding:{2mm 0}
            .content

    .whitespace

<!-- Internal helper that builds a titled numerable block -->

.function {namedparagraph}
    name tag? content:
    .numbered {.tag::otherwise {}}
        number:
        **.concatenate {
            .name
        } with:{
            .string {" .number"}
        } if:{
            .isnotempty {.number}
        }.paperblocksuffix** .content

.function {INTERNALtypedparagraph}
    type content:
    .var {localizedname} {.localize {paper:.type}}
    .var {numberingtag} {.concatenate {.type} {s}}
    .namedparagraph {.localizedname} {.numberingtag} {.content}

<-- A numerable 'definition' block -->

.function {definition}
    content:
    .INTERNALtypedparagraph {definition} {.content}

<-- A numerable 'lemma' block -->

.function {lemma}
    content:
    .INTERNALtypedparagraph {lemma} {.content}

<-- A numerable 'theorem' block -->

.function {theorem}
    content:
    .INTERNALtypedparagraph {theorem} {.content}

<-- A numerable 'proof' block -->

.function {proof}
    content:
    .INTERNALtypedparagraph {proof} {.content}

    .align {end}
        .text {.proofend} size:{huge}
