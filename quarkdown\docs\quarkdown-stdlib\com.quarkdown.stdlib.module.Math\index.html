<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>com.quarkdown.stdlib.module.Math</title>
<link href="../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer></script>
<link href="../../images/logo-icon.svg">
<link href="../../styles/stylesheet.css" rel="Stylesheet"></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../index.html">
                    quarkdown
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">1.6.3
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":quarkdown-stdlib/main">jvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":quarkdown-stdlib/main" data-filter=":quarkdown-stdlib/main">
                                <span class="checkbox--icon"></span>
                                jvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    quarkdown
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="package" id="content" pageids="quarkdown-stdlib::com.quarkdown.stdlib.module.Math////PointingToDeclaration//742850071">
  <div class="breadcrumbs"><a href="../index.html">quarkdown-stdlib</a><span class="delimiter">/</span><span class="current">com.quarkdown.stdlib.module.Math</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="FUNCTION,EXTENSION_FUNCTION">Functions</button></div>
    <div class="tabs-section-body">
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="-958484356%2FFunctions%2F742850071" anchor-label="abs" id="-958484356%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="abs.html"><span><span>abs</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-958484356%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">abs</span><span class="token punctuation"> </span><span class="token constant">x</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1285833169%2FFunctions%2F742850071" anchor-label="cos" id="1285833169%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="cos.html"><span><span>cos</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1285833169%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">cos</span><span class="token punctuation"> </span><span class="token constant">x</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1981639322%2FFunctions%2F742850071" anchor-label="divide" id="1981639322%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="divide.html"><span><span>divide</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1981639322%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">divide</span><span class="token punctuation"> </span><span class="token constant">a</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">by</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-322150568%2FFunctions%2F742850071" anchor-label="iseven" id="-322150568%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="iseven.html"><span><span>iseven</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-322150568%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">iseven</span><span class="token punctuation"> </span><span class="token constant">x</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Boolean///PointingToDeclaration/">Boolean</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1738350686%2FFunctions%2F742850071" anchor-label="logn" id="1738350686%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="logn.html"><span><span>logn</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1738350686%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">logn</span><span class="token punctuation"> </span><span class="token constant">x</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1135622021%2FFunctions%2F742850071" anchor-label="multiply" id="1135622021%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="multiply.html"><span><span>multiply</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1135622021%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">multiply</span><span class="token punctuation"> </span><span class="token constant">a</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">by</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1378069898%2FFunctions%2F742850071" anchor-label="negate" id="-1378069898%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="negate.html"><span><span>negate</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1378069898%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">negate</span><span class="token punctuation"> </span><span class="token constant">x</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1647129985%2FFunctions%2F742850071" anchor-label="pi" id="-1647129985%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="pi.html"><span><span>pi</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1647129985%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">pi</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1944199439%2FFunctions%2F742850071" anchor-label="pow" id="1944199439%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="pow.html"><span><span>pow</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1944199439%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">pow</span><span class="token punctuation"> </span><span class="token constant">base</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">to</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1753396442%2FFunctions%2F742850071" anchor-label="range" id="1753396442%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="range.html"><span><span>range</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1753396442%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">range</span><span class="token punctuation"> </span><span class="token constant">from</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">to</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Range///PointingToDeclaration/">Range</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Creates a range of numbers, which can also be iterated through. The behavior of an open range is delegated to the consumer. For instance, using a left-open range with <a href="../com.quarkdown.stdlib.module.Flow/foreach.html">foreach</a> will make the loop start from 1. The difference between this function and the built-in <code class="lang-kotlin">..</code> operator is that the latter does not allow for dynamic evaluation, hence both ends must be literals. This function allows evaluating ends dynamically: for instance, <code class="lang-kotlin">.range from:{1} to:{.sum {1} {2}}</code>. Floating-point numbers are truncated to integers.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1026353745%2FFunctions%2F742850071" anchor-label="rem" id="1026353745%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="rem.html"><span><span>rem</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1026353745%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">rem</span><span class="token punctuation"> </span><span class="token constant">a</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">b</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1260795432%2FFunctions%2F742850071" anchor-label="round" id="-1260795432%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="round.html"><span><span>round</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1260795432%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">round</span><span class="token punctuation"> </span><span class="token constant">x</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Rounds a floating-point number to the nearest integer.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="518944866%2FFunctions%2F742850071" anchor-label="sin" id="518944866%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="sin.html"><span><span>sin</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="518944866%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">sin</span><span class="token punctuation"> </span><span class="token constant">x</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="686886740%2FFunctions%2F742850071" anchor-label="sqrt" id="686886740%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="sqrt.html"><span><span>sqrt</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="686886740%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">sqrt</span><span class="token punctuation"> </span><span class="token constant">x</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-664857483%2FFunctions%2F742850071" anchor-label="subtract" id="-664857483%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="subtract.html"><span><span>subtract</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-664857483%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">subtract</span><span class="token punctuation"> </span><span class="token constant">a</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">b</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="137963906%2FFunctions%2F742850071" anchor-label="sum" id="137963906%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="sum.html"><span><span>sum</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="137963906%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">sum</span><span class="token punctuation"> </span><span class="token constant">a</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">b</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-224453525%2FFunctions%2F742850071" anchor-label="tan" id="-224453525%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="tan.html"><span><span>tan</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-224453525%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">tan</span><span class="token punctuation"> </span><span class="token constant">x</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="860268675%2FFunctions%2F742850071" anchor-label="truncate" id="860268675%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="truncate.html"><span><span>truncate</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="860268675%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">truncate</span><span class="token punctuation"> </span><span class="token constant">x</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-number/index.html">Number</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">decimals</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Number///PointingToDeclaration/">Number</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Truncates a floating-point number to a specified number of decimal places.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Quarkdown</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
