# -*- coding: utf-8 -*-
"""
该脚本在pre-receive或post-receive钩子中被调用,也可以直接将该文件作为git的钩子使用
若钩子为shell脚本，则需要加入以下代码调用该脚本:
while read line;do
        echo $line | python $PATH/pre-receive.py
done
当用户执行git push的时候会在远程版本库上触发此脚本
该脚本的主要作用：获取用户提交至版本库的文件列表,提交者及时间信息
"""
import re
import subprocess
import sys
import re

__author__ = "wanglong"


class Trigger(object):

    def __init__(self):
        """
        初始化文件列表信息，提交者信息，提交时间,当前操作的分支
        """
        self.pushAuthor = ""
        self.pushTime = ""
        self.fileList = []
        self.ref = ""

    def __getGitInfo(self):
        """
        """
        self.oldObject, self.newObject, self.ref = sys.stdin.readline().strip().split(' ')

    def __getPushInfo(self):
        """
        git show命令获取push作者，时间，以及文件列表
        文件的路径为相对于版本库根目录的一个相对路径
        """

        rev = subprocess.Popen('git rev-list ' + self.newObject, shell=True, stdout=subprocess.PIPE)
        revList = rev.stdout.readlines()
        revList = [x.strip() for x in revList]
        # print(self.oldObject)
        if self.oldObject == "0000000000000000000000000000000000000000":
            # "分支新增不进行校验"
            print(self.oldObject)
            exit(0)
        elif self.newObject == "0000000000000000000000000000000000000000":
            # "分支删除不进行校验"
            print(self.newObject)
            exit(0)
        elif self.oldObject not in revList[::]:
            print(self.oldObject, self.newObject)
            exit(0)
        else:
            print(self.oldObject, self.newObject)
            indexOld = revList.index(self.oldObject)
            pushList = revList[:indexOld]
        # 循环获取每次提交的文件列表
        for pObject in pushList:
            p = subprocess.Popen('git show ' + pObject, shell=True, stdout=subprocess.PIPE)
            pipe = p.stdout.readlines()
            pipe = [x.strip() for x in pipe]

            self.pushAuthor = pipe[1].strip("Author:").strip()

            self.pushTime = pipe[2].strip("Date:").strip()

            self.fileList.extend(['/'.join(fileName.split("/")[1:]) for fileName in pipe if
                                  fileName.startswith("+++") and not fileName.endswith("null")])

    def committer_check(self):
        """
         返回文件列表信息，提交者信息，提交时间
         """
        self.__getGitInfo()
        self.__getPushInfo()

        print("Time:", self.pushTime)
        print("Author:", self.pushAuthor)
        print("Ref:", self.ref)
        print("Files:", self.fileList)
        for file in self.fileList:
            # print(file)
            if re.search(r'master', self.ref) and \
                    self.pushAuthor.split(" ")[0] not in committer_List:
                return False
            # elif re.search('.*' + '.xml', i) and self.pushAuthor not in committer_List \
            #         and re.search('.*' + 'hsurp-pub-zip/deploy/template/server' + '.*', self.ref):
            #     return False
            # elif re.search('.*' + '.properties', i) and self.pushAuthor not in committer_List \
            #         and re.search('.*' + 'hsurp-pub-zip/deploy/template/server' + '.*', self.ref):
            #     return False
            else:
                continue


def sub_str(value):
    ls = value.group().split('\\')[1:]
    ls = [int(i, 8) for i in ls]
    try:
        print(bytes(ls))
        return bytes(ls).decode('utf8')
    except UnicodeDecodeError:
        return '！错！'


if __name__ == "__main__":
    committer_List = ['sunxx10266']
    strName = ''
    t = Trigger()
    # 如果是已授权用户操作，就做如下判断
    if t.committer_check() is False:
        print('未授权用户，非配置管理员无提交master分支权限，请联系配管')
        exit(1)
    else:
        print("递交成功")

