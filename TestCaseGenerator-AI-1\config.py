import os
import json
from pathlib import Path

class Config:
    DEFAULT_CONFIG = {
        "api": {
            "url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "key": "",
            "model": "gpt-4-turbo",  # 默认使用GPT-4
            "temperature": 0.7,
            "max_tokens": 2000,
            "top_p": 1.0
        },
        "models": {
            "available": [
                {
                    "id": "gpt-4-turbo",
                    "name": "GPT-4 Turbo",
                    "description": "OpenAI最新的大语言模型，性能最强"
                },
                {
                    "id": "gpt-3.5-turbo",
                    "name": "GPT-3.5 Turbo",
                    "description": "OpenAI性价比较高的模型"
                },
                {
                    "id": "claude-3-opus",
                    "name": "Claude 3 Opus",
                    "description": "Anthropic最新的大语言模型，性能强大"
                },
                {
                    "id": "claude-3-sonnet",
                    "name": "Claude 3 Sonnet",
                    "description": "Anthropic性价比较高的模型"
                },
                {
                    "id": "deepseek-chat",
                    "name": "Deepseek Chat",
                    "description": "Deepseek开源大语言模型"
                },
                {
                    "id": "deepseek-coder",
                    "name": "Deepseek Coder",
                    "description": "Deepseek专门用于代码生成的模型"
                },
                {
                    "id": "deepseek-math",
                    "name": "Deepseek Math",
                    "description": "Deepseek专门用于数学问题的模型"
                },
                {
                    "id": "qwen-max",
                    "name": "通义千问Max",
                    "description": "阿里通义千问最强大的模型"
                },
                {
                    "id": "qwen-plus",
                    "name": "通义千问Plus",
                    "description": "通用型通义千问模型"
                },
                {
                    "id": "qwen-turbo",
                    "name": "通义千问Turbo",
                    "description": "响应最快的通义千问模型"
                }
            ]
        },
        "ui": {
            "window_size": {
                "width": 1200,
                "height": 800
            },
            "splitter_ratio": [600, 600]
        },
        "prompt": {
            "template": (
                "Based on the following requirement document, generate a comprehensive set of test cases:\n\n"
                "{{document_content}}\n\n"
                "Please organize the test cases with the following structure:\n"
                "1. Test Case ID\n"
                "2. Test Description\n"
                "3. Preconditions\n"
                "4. Test Steps\n"
                "5. Expected Results\n"
                "6. Test Type (Functional, Non-functional, etc.)\n\n"
                "Format the output in markdown with proper headers and sections."
            )
        }
    }

    def __init__(self):
        self.config_path = Path(__file__).parent / "config.json"
        self.load_config()

    def load_config(self):
        """Load configuration from file or create default if not exists"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = self.DEFAULT_CONFIG
                self.save_config()
        except Exception as e:
            print(f"Error loading config: {e}")
            self.config = self.DEFAULT_CONFIG

    def save_config(self):
        """Save current configuration to file"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            print(f"Error saving config: {e}")

    def get(self, section, key, default=None):
        """Get a configuration value"""
        try:
            return self.config[section][key]
        except KeyError:
            return default

    def set(self, section, key, value):
        """Set a configuration value"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
        self.save_config()

# Global configuration instance
config = Config() 