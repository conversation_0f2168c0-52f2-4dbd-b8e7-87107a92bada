<thought domain="prompt-engineering">
  <exploration>
    # 提示词结构探索
    
    ```mermaid
    mindmap
      root((提示词设计))
        结构选择
          单一协议
            思考型(thought)
            执行型(execution)
            记忆型(memory)
            资源型(resource)
          协议组合
            thought+execution
            execution+memory
            thought+resource
            完整角色组合
        表达方式
          图形化表达
            流程图
            思维导图
            序列图
            状态图
          文本化表达
            有序列表
            无序列表
            表格
            纯文本
        目标用户
          AI系统
            通用大语言模型
            特定领域模型
            嵌入式AI系统
          人类用户
            提示词工程师
            领域专家
            终端使用者
    ```
  </exploration>
  
  <reasoning>
    # 提示词效果分析
    
    ```mermaid
    graph TD
      A[提示词分析] --> B[语义清晰度]
      A --> C[结构合理性]
      A --> D[执行可靠性]
      
      B --> B1[标签语义是否明确]
      B --> B2[内容描述是否准确]
      B --> B3[指令是否无歧义]
      
      C --> C1[层次结构是否合理]
      C --> C2[组件关系是否正确]
      C --> C3[是否符合DPML规范]
      
      D --> D1[是否有明确的执行流程]
      D --> D2[错误处理是否完备]
      D --> D3[边界条件是否考虑]
    ```
    
    ## 协议兼容性分析
    
    在组合多协议时，需考虑：
    1. 协议语义是否互补而非冲突
    2. 数据流向是否顺畅，输入输出是否匹配
    3. 优先级是否明确，特别是多协议规则冲突时
    4. 资源引用的加载时机是否合理设置
  </reasoning>
  
  <challenge>
    # 提示词设计风险评估
    
    ```mermaid
    mindmap
      root((潜在问题))
        结构问题
          标签嵌套不当
          协议混用冲突
          优先级设置错误
        语义问题
          指令歧义
          执行路径不明确
          边界条件未定义
        资源问题
          引用路径错误
          加载时机不当
          资源大小超限
        执行问题
          循环依赖
          无法验证结果
          异常处理不足
    ```
    
    ## 关键检查点
    
    1. 提示词是否存在逻辑矛盾或自相冲突的指令？
    2. 对于复杂任务，是否可分解为明确的子任务和判断步骤？
    3. 资源引用是否考虑了加载失败的应对措施？
    4. 提示词是否过度依赖特定AI系统的特性而缺乏通用性？
    5. 结构是否过于复杂，增加了理解和执行的难度？
  </challenge>
</thought> 