# Tetromino方块类
import random
import copy
from config import TETROMINO_SHAPES, CUTE_COLORS

class Tetromino:
    """俄罗斯方块类，定义方块形状和行为"""
    
    def __init__(self, shape_type=None):
        """初始化方块
        
        Args:
            shape_type: 方块类型，如果为None则随机选择
        """
        if shape_type is None:
            shape_type = random.choice(list(TETROMINO_SHAPES.keys()))
        
        self.shape_type = shape_type
        self.shapes = TETROMINO_SHAPES[shape_type]
        self.rotation = 0
        self.color = CUTE_COLORS[shape_type]
        self.x = 0  # 当前X位置
        self.y = 0  # 当前Y位置
    
    def get_current_shape(self):
        """获取当前旋转状态的形状"""
        return self.shapes[self.rotation]
    
    def rotate(self):
        """旋转方块（顺时针90度）"""
        if len(self.shapes) > 1:  # O形状不需要旋转
            self.rotation = (self.rotation + 1) % len(self.shapes)
            return True
        return False
    
    def get_rotated_shape(self):
        """获取旋转后的形状（不改变当前状态）"""
        if len(self.shapes) > 1:
            next_rotation = (self.rotation + 1) % len(self.shapes)
            return self.shapes[next_rotation]
        return self.get_current_shape()
    
    def get_rotated_blocks(self):
        """获取旋转后的方块坐标列表（不改变当前状态）"""
        blocks = []
        shape = self.get_rotated_shape()
        for row_idx, row in enumerate(shape):
            for col_idx, cell in enumerate(row):
                if cell == '#':
                    blocks.append((col_idx, row_idx))
        return blocks
    
    def get_shape_blocks(self):
        """获取形状的方块坐标列表"""
        blocks = []
        shape = self.get_current_shape()
        for row_idx, row in enumerate(shape):
            for col_idx, cell in enumerate(row):
                if cell == '#':
                    blocks.append((col_idx, row_idx))
        return blocks
    
    def get_absolute_blocks(self):
        """获取方块在游戏板上的绝对坐标"""
        blocks = self.get_shape_blocks()
        return [(self.x + block_x, self.y + block_y) for block_x, block_y in blocks]
    
    def get_color(self):
        """获取方块颜色"""
        return self.color
    
    def get_bounding_box(self):
        """获取方块的边界框"""
        blocks = self.get_shape_blocks()
        if not blocks:
            return 0, 0, 0, 0
        
        min_x = min(block[0] for block in blocks)
        max_x = max(block[0] for block in blocks)
        min_y = min(block[1] for block in blocks)
        max_y = max(block[1] for block in blocks)
        
        return min_x, min_y, max_x - min_x + 1, max_y - min_y + 1
    
    def copy(self):
        """创建方块的副本"""
        new_tetromino = Tetromino(self.shape_type)
        new_tetromino.rotation = self.rotation
        new_tetromino.x = self.x
        new_tetromino.y = self.y
        return new_tetromino
    
    def move(self, dx, dy):
        """移动方块
        
        Args:
            dx: X方向移动距离
            dy: Y方向移动距离
        """
        self.x += dx
        self.y += dy
    
    def set_position(self, x, y):
        """设置方块位置
        
        Args:
            x: X坐标
            y: Y坐标
        """
        self.x = x
        self.y = y
    
    def get_position(self):
        """获取方块位置"""
        return self.x, self.y
    
    def reset_rotation(self):
        """重置旋转状态"""
        self.rotation = 0
    
    def can_rotate(self):
        """检查是否可以旋转"""
        return len(self.shapes) > 1
    
    def get_shape_name(self):
        """获取方块形状名称"""
        return self.shape_type
    
    @staticmethod
    def get_all_shape_types():
        """获取所有方块类型"""
        return list(TETROMINO_SHAPES.keys())
    
    @staticmethod
    def create_random():
        """创建随机方块"""
        return Tetromino()
    
    def __str__(self):
        """字符串表示"""
        return f"Tetromino({self.shape_type}, rotation={self.rotation}, pos=({self.x}, {self.y}))"
    
    def __repr__(self):
        """详细字符串表示"""
        return self.__str__()