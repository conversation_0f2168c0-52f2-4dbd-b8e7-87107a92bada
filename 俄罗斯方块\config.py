# 游戏配置文件

# 游戏基础配置
GAME_CONFIG = {
    'BOARD_WIDTH': 10,
    'BOARD_HEIGHT': 20,
    'BLOCK_SIZE': 30,
    'WINDOW_WIDTH': 800,
    'WINDOW_HEIGHT': 600,
    'FPS': 60,
    'INITIAL_FALL_SPEED': 500,  # 毫秒
    'SPEED_INCREASE_RATE': 0.9,
    'LINES_PER_LEVEL': 10
}

# 可爱风格颜色配置
CUTE_COLORS = {
    'I': (255, 182, 193),  # 浅粉色
    'O': (255, 218, 185),  # 桃色
    'T': (221, 160, 221),  # 梅花色
    'S': (152, 251, 152),  # 浅绿色
    'Z': (255, 160, 122),  # 浅橙色
    'J': (173, 216, 230),  # 浅蓝色
    'L': (255, 255, 224),  # 浅黄色
    'BACKGROUND': (248, 248, 255),  # 幽灵白
    'GRID': (230, 230, 250),  # 薰衣草色
    'TEXT': (75, 0, 130),   # 靛青色
    'SHADOW': (200, 200, 200, 50),  # 半透明阴影
    # 分数动画颜色
    'SCORE_LOW': (135, 206, 250),   # 浅天蓝色 - 低分
    'SCORE_MID': (255, 165, 0),     # 橙色 - 中分
    'SCORE_HIGH': (255, 69, 0)       # 红橙色 - 高分
}

# 俄罗斯方块形状定义
TETROMINO_SHAPES = {
    'I': [
        ['.....',
         '..#..',
         '..#..',
         '..#..',
         '..#..'],
        ['.....',
         '.....',
         '####.',
         '.....',
         '.....']
    ],
    'O': [
        ['.....',
         '.....',
         '.##..',
         '.##..',
         '.....']
    ],
    'T': [
        ['.....',
         '.....',
         '.#...',
         '###..',
         '.....'],
        ['.....',
         '.....',
         '.#...',
         '.##..',
         '.#...'],
        ['.....',
         '.....',
         '.....',
         '###..',
         '.#...'],
        ['.....',
         '.....',
         '.#...',
         '##...',
         '.#...']
    ],
    'S': [
        ['.....',
         '.....',
         '.##..',
         '##...',
         '.....'],
        ['.....',
         '.....',
         '#....',
         '##...',
         '.#...']
    ],
    'Z': [
        ['.....',
         '.....',
         '##...',
         '.##..',
         '.....'],
        ['.....',
         '.....',
         '.#...',
         '##...',
         '#....']
    ],
    'J': [
        ['.....',
         '.....',
         '#....',
         '###..',
         '.....'],
        ['.....',
         '.....',
         '.##..',
         '.#...',
         '.#...'],
        ['.....',
         '.....',
         '.....',
         '###..',
         '..#..'],
        ['.....',
         '.....',
         '.#...',
         '.#...',
         '##...']
    ],
    'L': [
        ['.....',
         '.....',
         '..#..',
         '###..',
         '.....'],
        ['.....',
         '.....',
         '.#...',
         '.#...',
         '.##..'],
        ['.....',
         '.....',
         '.....',
         '###..',
         '#....'],
        ['.....',
         '.....',
         '##...',
         '.#...',
         '.#...']
    ]
}

# 控制键配置
CONTROLS = {
    'LEFT': 'LEFT',
    'RIGHT': 'RIGHT', 
    'DOWN': 'DOWN',
    'ROTATE': 'UP',
    'ROTATE_ALT': 'SPACE',
    'PAUSE': 'p',
    'RESTART': 'r'
}