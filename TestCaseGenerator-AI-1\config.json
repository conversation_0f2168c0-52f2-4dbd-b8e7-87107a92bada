{"api": {"url": "https://dashscope.aliyuncs.com/compatible-mode/v1", "key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxx", "model": "qwen-plus", "temperature": 0.7, "max_tokens": 2000, "top_p": 1.0}, "models": {"available": [{"id": "qwen-max", "name": "通义千问Max", "description": "性能最强的通义千问模型"}, {"id": "qwen-plus", "name": "通义千问Plus", "description": "通用型通义千问模型"}, {"id": "qwen-turbo", "name": "通义千问Turbo", "description": "响应最快的通义千问模型"}, {"id": "qwen-1.8b-chat", "name": "通义千问1.8B", "description": "轻量级通义千问模型"}]}, "ui": {"window_size": {"width": 1200, "height": 800}, "splitter_ratio": [600, 600]}, "prompt": {"template": "Based on the following requirement document, generate a comprehensive set of test cases:\n\n{{document_content}}\n\nPlease organize the test cases with the following structure:\n1. Test Case ID\n2. Test Description\n3. Preconditions\n4. Test Steps\n5. Expected Results\n6. Test Type (Functional, Non-functional, etc.)\n\nFormat the output in markdown with proper headers and sections."}}