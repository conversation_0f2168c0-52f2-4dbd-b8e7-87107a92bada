<terminologies>
  <terminology>
    <zh>思考提示单元</zh>
    <en>Thought Prompt Unit</en>
    <definition>
      由<thought>标签及其子标签（如exploration、reasoning、plan、challenge）构成的、表达完整思考过程的结构化提示词单元。常简称为"思考单元"，两者等同。
    </definition>
    <examples>
      <example>"本协议所有复杂推理均应以 #思考提示单元 为基本结构。"</example>
      <example>"每个 #思考单元 都可以独立复用。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>思考单元</zh>
    <en>Thought Unit</en>
    <definition>
      "思考提示单元"的简称，含义完全等同。参见"思考提示单元"。
    </definition>
    <examples>
      <example>"请将你的分析拆分为多个 #思考单元。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>探索思维</zh>
    <en>Exploration</en>
    <definition>
      在本协议中，#探索思维 专指 <exploration> 标签及其结构单元，表示用于承载发散性、创新性思考内容的提示词片段。
    </definition>
    <examples>
      <example>"请将你的假设写入 #探索思维 单元（即 <exploration> 标签）。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>推理思维</zh>
    <en>Reasoning</en>
    <definition>
      在本协议中，#推理思维 专指 <reasoning> 标签及其结构单元，表示用于承载因果分析、逻辑推理内容的提示词片段。
    </definition>
    <examples>
      <example>"所有因果链条建议写入 #推理思维 单元（即 <reasoning> 标签）。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>计划思维</zh>
    <en>Plan</en>
    <definition>
      在本协议中，#计划思维 专指 <plan> 标签及其结构单元，表示用于承载行动方案、结构规划内容的提示词片段。
    </definition>
    <examples>
      <example>"最终方案请整理进 #计划思维 单元（即 <plan> 标签）。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>挑战思维</zh>
    <en>Challenge</en>
    <definition>
      在本协议中，#挑战思维 专指 <challenge> 标签及其结构单元，表示用于承载批判性、风险识别内容的提示词片段。
    </definition>
    <examples>
      <example>"请用 #挑战思维 单元（即 <challenge> 标签）补充反例和风险点。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>思维模式</zh>
    <en>Thinking Mode</en>
    <definition>
      在本协议中，#思维模式 指不同类型的思考方式，如 #探索思维、#推理思维、#计划思维、#挑战思维 等，分别由 <exploration>、<reasoning>、<plan>、<challenge> 标签实现。
    </definition>
    <examples>
      <example>"可根据任务需要切换不同 #思维模式。"</example>
    </examples>
  </terminology>
</terminologies> 