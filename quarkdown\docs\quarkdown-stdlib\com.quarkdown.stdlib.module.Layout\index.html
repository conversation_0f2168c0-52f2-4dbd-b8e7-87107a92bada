<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>com.quarkdown.stdlib.module.Layout</title>
<link href="../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer></script>
<link href="../../images/logo-icon.svg">
<link href="../../styles/stylesheet.css" rel="Stylesheet"></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../index.html">
                    quarkdown
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">1.6.3
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":quarkdown-stdlib/main">jvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":quarkdown-stdlib/main" data-filter=":quarkdown-stdlib/main">
                                <span class="checkbox--icon"></span>
                                jvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    quarkdown
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="package" id="content" pageids="quarkdown-stdlib::com.quarkdown.stdlib.module.Layout////PointingToDeclaration//742850071">
  <div class="breadcrumbs"><a href="../index.html">quarkdown-stdlib</a><span class="delimiter">/</span><span class="current">com.quarkdown.stdlib.module.Layout</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="FUNCTION,EXTENSION_FUNCTION">Functions</button></div>
    <div class="tabs-section-body">
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="-1589831657%2FFunctions%2F742850071" anchor-label="align" id="-1589831657%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="align.html"><span><span>align</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1589831657%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">align</span><span class="token punctuation"> </span><span class="token constant">alignment</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.Alignment///PointingToDeclaration/">Container.Alignment</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Aligns content and text within its parent.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1696100019%2FFunctions%2F742850071" anchor-label="box" id="-1696100019%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="box.html"><span><span>box</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1696100019%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">box</span><span class="token punctuation"> </span><span class="token punctuation"> </span><span class="token constant">title</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/InlineMarkdownContent///PointingToDeclaration/">InlineMarkdownContent</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">      </span><span class="token punctuation"> </span><span class="token constant">type</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Box.Type///PointingToDeclaration/">Box.Type</span><span class="token operator"> = </span>Box.Type.CALLOUT<span class="token punctuation">}</span><br><span class="token punctuation">   </span><span class="token punctuation"> </span><span class="token constant">padding</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation"> </span><span class="token constant">background</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.misc.color/Color///PointingToDeclaration/">Color</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation"> </span><span class="token constant">foreground</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.misc.color/Color///PointingToDeclaration/">Color</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">      </span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><br><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Inserts content in a box.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="482071249%2FFunctions%2F742850071" anchor-label="center" id="482071249%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="center.html"><span><span>center</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="482071249%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">center</span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Centers content and text within its parent.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1317561791%2FFunctions%2F742850071" anchor-label="clip" id="-1317561791%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="clip.html"><span><span>clip</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1317561791%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">clip</span><span class="token punctuation"> </span><span class="token constant">clip</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Clipped.Clip///PointingToDeclaration/">Clipped.Clip</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Applies a clipping path to its content.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-363380517%2FFunctions%2F742850071" anchor-label="collapse" id="-363380517%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="collapse.html"><span><span>collapse</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-363380517%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">collapse</span><span class="token punctuation"> </span><span class="token constant">title</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/InlineMarkdownContent///PointingToDeclaration/">InlineMarkdownContent</span><span class="token punctuation">}</span><br><span class="token punctuation">          </span><span class="token punctuation"> </span><span class="token constant">open</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-boolean/index.html">Boolean</a><span class="token operator"> = </span><span class="token boolean">false</span><span class="token punctuation">}</span><br><span class="token punctuation">          </span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><br><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Inserts content in a collapsible block, whose content can be hidden or shown by interacting with it.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="154908800%2FFunctions%2F742850071" anchor-label="column" id="154908800%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="column.html"><span><span>column</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="154908800%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">column</span><span class="token punctuation"> </span><span class="token constant">alignment</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Stacked.MainAxisAlignment///PointingToDeclaration/">Stacked.MainAxisAlignment</span><span class="token operator"> = </span>Stacked.MainAxisAlignment.START<span class="token punctuation">}</span><br><span class="token punctuation">           </span><span class="token punctuation"> </span><span class="token constant">cross</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Stacked.CrossAxisAlignment///PointingToDeclaration/">Stacked.CrossAxisAlignment</span><span class="token operator"> = </span>Stacked.CrossAxisAlignment.CENTER<span class="token punctuation">}</span><br><span class="token punctuation">             </span><span class="token punctuation"> </span><span class="token constant">gap</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">            </span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><br><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Stacks content vertically.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="755926471%2FFunctions%2F742850071" anchor-label="container" id="755926471%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="container.html"><span><span>container</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="755926471%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">container</span><span class="token punctuation"> </span><span class="token constant">width</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">height</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">      </span><span class="token punctuation"> </span><span class="token constant">fullwidth</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-boolean/index.html">Boolean</a><span class="token operator"> = </span><span class="token boolean">false</span><span class="token punctuation">}</span><br><span class="token punctuation">     </span><span class="token punctuation"> </span><span class="token constant">foreground</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.misc.color/Color///PointingToDeclaration/">Color</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">     </span><span class="token punctuation"> </span><span class="token constant">background</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.misc.color/Color///PointingToDeclaration/">Color</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">border</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.misc.color/Color///PointingToDeclaration/">Color</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">    </span><span class="token punctuation"> </span><span class="token constant">borderwidth</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Sizes///PointingToDeclaration/">Sizes</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">    </span><span class="token punctuation"> </span><span class="token constant">borderstyle</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.BorderStyle///PointingToDeclaration/">Container.BorderStyle</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">margin</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Sizes///PointingToDeclaration/">Sizes</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">        </span><span class="token punctuation"> </span><span class="token constant">padding</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Sizes///PointingToDeclaration/">Sizes</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">radius</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Sizes///PointingToDeclaration/">Sizes</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">      </span><span class="token punctuation"> </span><span class="token constant">alignment</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.Alignment///PointingToDeclaration/">Container.Alignment</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">  </span><span class="token punctuation"> </span><span class="token constant">textalignment</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment///PointingToDeclaration/">Container.TextAlignment</span><span class="token operator">?</span><span class="token operator"> = </span>alignment?.let(Container.TextAlignment::fromAlignment)<span class="token punctuation">}</span><br><span class="token punctuation">          </span><span class="token punctuation"> </span><span class="token constant">float</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.FloatAlignment///PointingToDeclaration/">Container.FloatAlignment</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">           </span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">A general-purpose container that groups content. Any layout rules (e.g. from <a href="align.html">align</a>, <a href="row.html">row</a>, <a href="column.html">column</a>, <a href="grid.html">grid</a>) are ignored inside this container.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-844987142%2FFunctions%2F742850071" anchor-label="figure" id="-844987142%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="figure.html"><span><span>figure</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-844987142%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">figure</span><span class="token punctuation"> </span><span class="token constant">caption</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Inserts content in a figure block, which features an optional caption and can be numbered according to the <code class="lang-kotlin">figures</code> numbering rules.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2027512332%2FFunctions%2F742850071" anchor-label="float" id="-2027512332%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="float.html"><span><span>float</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2027512332%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">float</span><span class="token punctuation"> </span><span class="token constant">alignment</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.FloatAlignment///PointingToDeclaration/">Container.FloatAlignment</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Turns content into a floating element, allowing subsequent content to wrap around it.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="711136567%2FFunctions%2F742850071" anchor-label="fullspan" id="711136567%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="fullspan.html"><span><span>fullspan</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="711136567%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">fullspan</span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">If the document has a multi-column layout (set via <a href="../com.quarkdown.stdlib.module.Document/pageformat.html">pageformat</a>), makes content span across all columns in a multi-column layout. If the document has a single-column layout, the effect is the same as <a href="container.html">container</a>.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1528237427%2FFunctions%2F742850071" anchor-label="grid" id="-1528237427%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="grid.html"><span><span>grid</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1528237427%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">grid</span><span class="token punctuation"> </span><span class="token constant">columns</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token punctuation">}</span><br><span class="token punctuation">   </span><span class="token punctuation"> </span><span class="token constant">alignment</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Stacked.MainAxisAlignment///PointingToDeclaration/">Stacked.MainAxisAlignment</span><span class="token operator"> = </span>Stacked.MainAxisAlignment.CENTER<span class="token punctuation">}</span><br><span class="token punctuation">       </span><span class="token punctuation"> </span><span class="token constant">cross</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Stacked.CrossAxisAlignment///PointingToDeclaration/">Stacked.CrossAxisAlignment</span><span class="token operator"> = </span>Stacked.CrossAxisAlignment.CENTER<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">gap</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">        </span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><br><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Stacks content in a grid layout. Each child is placed in a cell in a row, and a row ends when its cell count reaches <a href="grid.html">columns</a>.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2109024371%2FFunctions%2F742850071" anchor-label="numbered" id="2109024371%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="numbered.html"><span><span>numbered</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2109024371%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">numbered</span><span class="token punctuation"> </span><span class="token constant">key</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.function.value.data/Lambda///PointingToDeclaration/">Lambda</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Node that can be numbered depending on its location in the document and the amount of occurrences according to its <a href="numbered.html">key</a>.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1614514650%2FFunctions%2F742850071" anchor-label="row" id="-1614514650%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="row.html"><span><span>row</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1614514650%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">row</span><span class="token punctuation"> </span><span class="token constant">alignment</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Stacked.MainAxisAlignment///PointingToDeclaration/">Stacked.MainAxisAlignment</span><span class="token operator"> = </span>Stacked.MainAxisAlignment.START<span class="token punctuation">}</span><br><span class="token punctuation">        </span><span class="token punctuation"> </span><span class="token constant">cross</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Stacked.CrossAxisAlignment///PointingToDeclaration/">Stacked.CrossAxisAlignment</span><span class="token operator"> = </span>Stacked.CrossAxisAlignment.CENTER<span class="token punctuation">}</span><br><span class="token punctuation">          </span><span class="token punctuation"> </span><span class="token constant">gap</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><br><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Stacks content horizontally.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1836083785%2FFunctions%2F742850071" anchor-label="table" id="1836083785%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="table.html"><span><span>table</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1836083785%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">table</span><span class="token punctuation"> </span><span class="token constant">subTables</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin.collections/-iterable/index.html">Iterable</a><span class="token operator">&lt;</span><span data-unresolved-link="com.quarkdown.core.function.value/Any///PointingToDeclaration/">Any</span><span class="token operator">&gt;</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Creates a table out of a collection of columns.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1889137527%2FFunctions%2F742850071" anchor-label="textcollapse" id="1889137527%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="textcollapse.html"><span><span>textcollapse</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1889137527%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">textcollapse</span><span class="token punctuation"> </span><span class="token constant">full</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/InlineMarkdownContent///PointingToDeclaration/">InlineMarkdownContent</span><span class="token punctuation">}</span><br><span class="token punctuation">            </span><span class="token punctuation"> </span><span class="token constant">short</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/InlineMarkdownContent///PointingToDeclaration/">InlineMarkdownContent</span><span class="token punctuation">}</span><br><span class="token punctuation">             </span><span class="token punctuation"> </span><span class="token constant">open</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-boolean/index.html">Boolean</a><span class="token operator"> = </span><span class="token boolean">false</span><span class="token punctuation">}</span><br><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Inserts content in a collapsible text span, whose content can be expanded or collapsed by interacting with it.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="339045407%2FFunctions%2F742850071" anchor-label="todo" id="339045407%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="todo.html"><span><span>todo</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="339045407%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">todo</span><span class="token punctuation"> </span><span class="token constant">body</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/MarkdownContent///PointingToDeclaration/">MarkdownContent</span><span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">Creates a <i>to do</i> box, to mark content that needs to be done later, and logs it. The title is localized according to the current locale (<a href="../com.quarkdown.stdlib.module.Document/doclang.html">doclang</a>), or English as a fallback.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1075265884%2FFunctions%2F742850071" anchor-label="whitespace" id="-1075265884%2FFunctions%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="whitespace.html"><span><span>whitespace</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1075265884%2FFunctions%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">whitespace</span><span class="token punctuation"> </span><span class="token constant">width</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token constant">height</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><span class="token punctuation"> </span><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><div class="brief "><p class="paragraph">An empty square that adds whitespace to the layout. If at least one of the dimensions is set, the square will have a fixed size. If both dimensions are unset, a blank character is used, which can be useful for spacing and adding line breaks.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Quarkdown</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
