# 用户界面管理器
import pygame
from config import GAME_CONFIG, CUTE_COLORS

class UI:
    """用户界面管理器，处理界面渲染和用户交互"""
    
    def __init__(self, screen):
        """初始化UI管理器
        
        Args:
            screen: pygame屏幕对象
        """
        self.screen = screen
        self.block_size = GAME_CONFIG['BLOCK_SIZE']
        self.board_x = 50  # 游戏板在屏幕上的X偏移
        self.board_y = 50  # 游戏板在屏幕上的Y偏移
        
        # 初始化字体
        pygame.font.init()
        try:
            # 尝试使用系统中文字体
            self.font_large = pygame.font.SysFont('microsoftyaheibold', 36)
            self.font_medium = pygame.font.SysFont('microsoftyaheibold', 24)
            self.font_small = pygame.font.SysFont('microsoftyaheibold', 18)
        except:
            # 如果找不到中文字体，尝试使用其他常见中文字体
            try:
                self.font_large = pygame.font.SysFont('simhei', 36)
                self.font_medium = pygame.font.SysFont('simhei', 24)
                self.font_small = pygame.font.SysFont('simhei', 18)
            except:
                # 如果仍然找不到，使用默认字体
                print("警告：无法加载中文字体，界面可能显示乱码")
                self.font_large = pygame.font.Font(None, 36)
                self.font_medium = pygame.font.Font(None, 24)
                self.font_small = pygame.font.Font(None, 18)
    
    def draw_board(self, board):
        """绘制游戏板
        
        Args:
            board: Board对象
        """
        # 绘制背景
        board_width = board.width * self.block_size
        board_height = board.height * self.block_size
        
        # 绘制游戏板背景
        pygame.draw.rect(self.screen, CUTE_COLORS['BACKGROUND'],
                        (self.board_x, self.board_y, board_width, board_height))
        
        # 绘制网格线
        for x in range(board.width + 1):
            pygame.draw.line(self.screen, CUTE_COLORS['GRID'],
                           (self.board_x + x * self.block_size, self.board_y),
                           (self.board_x + x * self.block_size, self.board_y + board_height))
        
        for y in range(board.height + 1):
            pygame.draw.line(self.screen, CUTE_COLORS['GRID'],
                           (self.board_x, self.board_y + y * self.block_size),
                           (self.board_x + board_width, self.board_y + y * self.block_size))
        
        # 绘制已放置的方块
        for y in range(board.height):
            for x in range(board.width):
                if board.grid[y][x] is not None:
                    self.draw_block(x, y, board.grid[y][x])
    
    def draw_block(self, x, y, color, offset_x=0, offset_y=0, glow_effect=False):
        """绘制单个方块（带增强的可爱风格）
        
        Args:
            x: 网格X坐标
            y: 网格Y坐标
            color: 方块颜色
            offset_x: X偏移
            offset_y: Y偏移
            glow_effect: 是否添加发光效果
        """
        block_x = self.board_x + x * self.block_size + offset_x
        block_y = self.board_y + y * self.block_size + offset_y
        
        # 增强的阴影效果 - 更柔和的渐变阴影
        shadow_size = 4
        for i in range(shadow_size, 0, -1):
            alpha = 120 - (i * 20)  # 从内到外渐变透明度
            shadow_color = tuple(max(0, c - 60) for c in color[:3]) + (alpha,)
            shadow_surface = pygame.Surface((self.block_size - 2 + i, self.block_size - 2 + i), pygame.SRCALPHA)
            pygame.draw.rect(shadow_surface, shadow_color, 
                           (0, 0, self.block_size - 2 + i, self.block_size - 2 + i), 
                           border_radius=6 + i//2)
            self.screen.blit(shadow_surface, (block_x + 2 - i//2, block_y + 2 - i//2))
        
        # 增强的发光效果（如果启用）
        if glow_effect:
            glow_size = 6  # 更大的发光范围
            for i in range(glow_size, 0, -1):
                alpha = 100 - (i * 10)  # 从内到外渐变透明度
                glow_color = tuple(min(255, c + 60) for c in color[:3]) + (alpha,)
                glow_surface = pygame.Surface((self.block_size + i * 2, self.block_size + i * 2), pygame.SRCALPHA)
                pygame.draw.rect(glow_surface, glow_color, 
                               (0, 0, self.block_size + i * 2, self.block_size + i * 2), 
                               border_radius=8 + i//2)
                self.screen.blit(glow_surface, (block_x - i, block_y - i))
        
        # 绘制方块主体（更圆润的圆角矩形）
        rect = pygame.Rect(block_x + 1, block_y + 1, 
                          self.block_size - 2, self.block_size - 2)
        pygame.draw.rect(self.screen, color, rect, border_radius=7)  # 增加圆角半径
        
        # 增强的渐变高光效果 - 更平滑的渐变
        gradient_height = 10  # 增加渐变高度
        for i in range(gradient_height):
            # 计算渐变颜色和透明度
            factor = 1 - (i / gradient_height)
            highlight_color = tuple(min(255, int(c + 60 * factor)) for c in color[:3])
            highlight_rect = pygame.Rect(block_x + 2, block_y + 2 + i, 
                                       self.block_size - 4, 1)  # 每行1像素高
            pygame.draw.rect(self.screen, highlight_color, highlight_rect)
        
        # 添加内部装饰 - 小圆点
        dot_color = tuple(min(255, c + 80) for c in color[:3])
        dot_pos = (block_x + self.block_size // 4, block_y + self.block_size // 4)
        pygame.draw.circle(self.screen, dot_color, dot_pos, 2)
        
        # 绘制边框装饰 - 更精细的边框
        border_color = tuple(max(0, c - 30) for c in color[:3])
        pygame.draw.rect(self.screen, border_color, rect, width=1, border_radius=7)
    
    def draw_tetromino(self, tetromino, x, y, animation_data=None):
        """绘制俄罗斯方块
        
        Args:
            tetromino: Tetromino对象
            x: X坐标
            y: Y坐标
            animation_data: 动画数据，包含类型和进度
        """
        blocks = tetromino.get_shape_blocks()
        color = tetromino.get_color()
        
        # 检查是否有动画
        if animation_data and animation_data.get('type') == 'place' and animation_data.get('progress') < 1.0:
            self.draw_tetromino_place_animation(tetromino, x, y, blocks, color, animation_data['progress'])
        else:
            # 正常绘制
            for block_x, block_y in blocks:
                draw_x = x + block_x
                draw_y = y + block_y
                
                # 只绘制在游戏板范围内的方块
                if draw_y >= 0:
                    self.draw_block(draw_x, draw_y, color)
    
    def draw_tetromino_place_animation(self, tetromino, x, y, blocks, color, progress):
        """绘制方块放置动画
        
        Args:
            tetromino: Tetromino对象
            x: X坐标
            y: Y坐标
            blocks: 方块形状
            color: 方块颜色
            progress: 动画进度 (0.0 到 1.0)
        """
        import math
        
        # 动画阶段
        if progress < 0.3:
            # 第一阶段：震动效果
            phase_progress = progress / 0.3
            shake_amount = 3 * (1 - phase_progress)
            
            for block_x, block_y in blocks:
                # 计算震动偏移
                shake_x = math.sin(progress * 30) * shake_amount
                
                draw_x = x + block_x + shake_x
                draw_y = y + block_y
                
                # 只绘制在游戏板范围内的方块
                if draw_y >= 0:
                    self.draw_block(int(draw_x), draw_y, color, glow_effect=True)
        
        elif progress < 0.7:
            # 第二阶段：闪光效果
            phase_progress = (progress - 0.3) / 0.4
            flash_intensity = math.sin(phase_progress * math.pi) * 100
            
            # 调整颜色亮度
            flash_color = tuple(min(255, c + int(flash_intensity)) for c in color[:3])
            
            for block_x, block_y in blocks:
                draw_x = x + block_x
                draw_y = y + block_y
                
                # 只绘制在游戏板范围内的方块
                if draw_y >= 0:
                    self.draw_block(draw_x, draw_y, flash_color, glow_effect=True)
        
        else:
            # 第三阶段：恢复正常
            phase_progress = (progress - 0.7) / 0.3
            
            for block_x, block_y in blocks:
                draw_x = x + block_x
                draw_y = y + block_y
                
                # 只绘制在游戏板范围内的方块
                if draw_y >= 0:
                    self.draw_block(draw_x, draw_y, color)
    
    def draw_score_panel(self, score_manager):
        """绘制分数面板
        
        Args:
            score_manager: ScoreManager对象
        """
        panel_x = self.board_x + GAME_CONFIG['BOARD_WIDTH'] * self.block_size + 30
        panel_y = self.board_y
        
        # 绘制面板背景
        panel_width = 200
        panel_height = 350
        pygame.draw.rect(self.screen, CUTE_COLORS['BACKGROUND'],
                        (panel_x, panel_y, panel_width, panel_height),
                        border_radius=10)
        pygame.draw.rect(self.screen, CUTE_COLORS['GRID'],
                        (panel_x, panel_y, panel_width, panel_height),
                        width=2, border_radius=10)
        
        # 绘制分数信息
        y_offset = panel_y + 20
        
        # 标题
        title_text = self.font_medium.render("游戏信息", True, CUTE_COLORS['TEXT'])
        title_rect = title_text.get_rect(centerx=panel_x + panel_width // 2)
        title_rect.y = y_offset
        self.screen.blit(title_text, title_rect)
        y_offset += 40
        
        # 当前分数
        score_text = self.font_medium.render(f"分数: {score_manager.get_current_score()}", 
                                           True, CUTE_COLORS['TEXT'])
        self.screen.blit(score_text, (panel_x + 10, y_offset))
        y_offset += 30
        
        # 等级
        level_text = self.font_medium.render(f"等级: {score_manager.get_current_level()}", 
                                           True, CUTE_COLORS['TEXT'])
        self.screen.blit(level_text, (panel_x + 10, y_offset))
        y_offset += 30
        
        # 消除行数
        lines_text = self.font_medium.render(f"行数: {score_manager.get_lines_cleared()}", 
                                           True, CUTE_COLORS['TEXT'])
        self.screen.blit(lines_text, (panel_x + 10, y_offset))
        y_offset += 30
        
        # 距离下一级
        lines_to_next = score_manager.get_lines_to_next_level()
        next_level_text = self.font_small.render(f"升级还需: {lines_to_next} 行", 
                                               True, CUTE_COLORS['TEXT'])
        self.screen.blit(next_level_text, (panel_x + 10, y_offset))
        y_offset += 25
        
        # 方块数量
        pieces_text = self.font_small.render(f"方块数: {score_manager.get_total_pieces_placed()}", 
                                           True, CUTE_COLORS['TEXT'])
        self.screen.blit(pieces_text, (panel_x + 10, y_offset))
        y_offset += 25
        
        # 游戏时间
        game_time = int(score_manager.get_game_time())
        minutes = game_time // 60
        seconds = game_time % 60
        time_text = self.font_small.render(f"时间: {minutes:02d}:{seconds:02d}", 
                                         True, CUTE_COLORS['TEXT'])
        self.screen.blit(time_text, (panel_x + 10, y_offset))
        y_offset += 30
        
        # 分隔线
        pygame.draw.line(self.screen, CUTE_COLORS['GRID'],
                        (panel_x + 10, y_offset), (panel_x + panel_width - 10, y_offset), 2)
        y_offset += 15
        
        # 最高分
        high_score_text = self.font_small.render(f"最高分: {score_manager.get_high_score()}", 
                                               True, CUTE_COLORS['TEXT'])
        self.screen.blit(high_score_text, (panel_x + 10, y_offset))
        y_offset += 20
        
        # 等级评价
        grade = score_manager.calculate_grade()
        grade_color = self._get_grade_color(grade)
        grade_text = self.font_small.render(f"评价: {grade}", True, grade_color)
        self.screen.blit(grade_text, (panel_x + 10, y_offset))
    
    def draw_next_piece(self, tetromino):
        """绘制下一个方块预览
        
        Args:
            tetromino: 下一个Tetromino对象
        """
        panel_x = self.board_x + GAME_CONFIG['BOARD_WIDTH'] * self.block_size + 30
        panel_y = self.board_y + 250
        
        # 绘制预览区域背景
        preview_width = 120
        preview_height = 80
        pygame.draw.rect(self.screen, CUTE_COLORS['BACKGROUND'],
                        (panel_x, panel_y, preview_width, preview_height),
                        border_radius=8)
        pygame.draw.rect(self.screen, CUTE_COLORS['GRID'],
                        (panel_x, panel_y, preview_width, preview_height),
                        width=2, border_radius=8)
        
        # 绘制预览区域标题
        next_text = self.font_medium.render("下一个:", True, CUTE_COLORS['TEXT'])
        title_rect = next_text.get_rect(centerx=panel_x + preview_width // 2)
        title_rect.y = panel_y + 5
        self.screen.blit(next_text, title_rect)
        
        # 计算方块预览的居中位置
        blocks = tetromino.get_shape_blocks()
        color = tetromino.get_color()
        
        if blocks:
            # 计算方块的边界框
            min_x = min(block[0] for block in blocks)
            max_x = max(block[0] for block in blocks)
            min_y = min(block[1] for block in blocks)
            max_y = max(block[1] for block in blocks)
            
            # 计算方块的实际尺寸
            block_width = (max_x - min_x + 1) * (self.block_size // 2)
            block_height = (max_y - min_y + 1) * (self.block_size // 2)
            
            # 计算居中偏移
            center_x = panel_x + (preview_width - block_width) // 2 - min_x * (self.block_size // 2)
            center_y = panel_y + 25 + (preview_height - 25 - block_height) // 2 - min_y * (self.block_size // 2)
            
            # 绘制预览方块
            for block_x, block_y in blocks:
                draw_x = center_x + block_x * (self.block_size // 2)
                draw_y = center_y + block_y * (self.block_size // 2)
                
                # 绘制小尺寸的预览方块（带可爱风格）
                self.draw_preview_block(draw_x, draw_y, color)
    
    def draw_preview_block(self, x, y, color):
        """绘制预览方块
        
        Args:
            x: X坐标
            y: Y坐标
            color: 方块颜色
        """
        block_size = self.block_size // 2
        
        # 绘制方块主体
        rect = pygame.Rect(x, y, block_size - 1, block_size - 1)
        pygame.draw.rect(self.screen, color, rect, border_radius=3)
        
        # 绘制高光效果
        highlight_color = tuple(min(255, c + 40) for c in color[:3])
        highlight_rect = pygame.Rect(x + 1, y + 1, block_size - 3, 3)
        pygame.draw.rect(self.screen, highlight_color, highlight_rect, border_radius=2)
    
    def draw_multiple_next_pieces(self, next_pieces):
        """绘制多个下一个方块预览（可选功能）
        
        Args:
            next_pieces: 下一个方块列表
        """
        if not next_pieces:
            return
        
        panel_x = self.board_x + GAME_CONFIG['BOARD_WIDTH'] * self.block_size + 30
        panel_y = self.board_y + 250
        
        # 绘制扩展预览区域背景
        preview_width = 120
        preview_height = min(200, 80 + len(next_pieces) * 40)
        pygame.draw.rect(self.screen, CUTE_COLORS['BACKGROUND'],
                        (panel_x, panel_y, preview_width, preview_height),
                        border_radius=8)
        pygame.draw.rect(self.screen, CUTE_COLORS['GRID'],
                        (panel_x, panel_y, preview_width, preview_height),
                        width=2, border_radius=8)
        
        # 绘制标题
        next_text = self.font_medium.render("接下来:", True, CUTE_COLORS['TEXT'])
        title_rect = next_text.get_rect(centerx=panel_x + preview_width // 2)
        title_rect.y = panel_y + 5
        self.screen.blit(next_text, title_rect)
        
        # 绘制每个预览方块
        for i, tetromino in enumerate(next_pieces[:5]):  # 最多显示5个
            preview_y_offset = panel_y + 30 + i * 35
            
            # 绘制方块名称
            name_text = self.font_small.render(f"{i+1}. {tetromino.get_shape_name()}", 
                                             True, CUTE_COLORS['TEXT'])
            self.screen.blit(name_text, (panel_x + 5, preview_y_offset))
            
            # 绘制小方块预览
            blocks = tetromino.get_shape_blocks()
            color = tetromino.get_color()
            
            if blocks:
                # 简化的小预览
                preview_x = panel_x + 70
                for j, (block_x, block_y) in enumerate(blocks[:3]):  # 只显示前3个方块
                    small_x = preview_x + j * 8
                    small_y = preview_y_offset + 2
                    
                    small_rect = pygame.Rect(small_x, small_y, 6, 6)
                    pygame.draw.rect(self.screen, color, small_rect, border_radius=1)
    
    def draw_hold_piece(self, tetromino):
        """绘制保留方块预览（可选功能）
        
        Args:
            tetromino: 保留的Tetromino对象，可以为None
        """
        panel_x = self.board_x + GAME_CONFIG['BOARD_WIDTH'] * self.block_size + 30
        panel_y = self.board_y + 350
        
        # 绘制保留区域背景
        hold_width = 120
        hold_height = 80
        pygame.draw.rect(self.screen, CUTE_COLORS['BACKGROUND'],
                        (panel_x, panel_y, hold_width, hold_height),
                        border_radius=8)
        pygame.draw.rect(self.screen, CUTE_COLORS['GRID'],
                        (panel_x, panel_y, hold_width, hold_height),
                        width=2, border_radius=8)
        
        # 绘制标题
        hold_text = self.font_medium.render("保留:", True, CUTE_COLORS['TEXT'])
        title_rect = hold_text.get_rect(centerx=panel_x + hold_width // 2)
        title_rect.y = panel_y + 5
        self.screen.blit(hold_text, title_rect)
        
        if tetromino:
            # 绘制保留方块（类似下一个方块的绘制）
            blocks = tetromino.get_shape_blocks()
            color = tetromino.get_color()
            
            if blocks:
                # 计算居中位置
                min_x = min(block[0] for block in blocks)
                max_x = max(block[0] for block in blocks)
                min_y = min(block[1] for block in blocks)
                max_y = max(block[1] for block in blocks)
                
                block_width = (max_x - min_x + 1) * (self.block_size // 2)
                block_height = (max_y - min_y + 1) * (self.block_size // 2)
                
                center_x = panel_x + (hold_width - block_width) // 2 - min_x * (self.block_size // 2)
                center_y = panel_y + 25 + (hold_height - 25 - block_height) // 2 - min_y * (self.block_size // 2)
                
                # 绘制保留方块（稍微透明一些）
                for block_x, block_y in blocks:
                    draw_x = center_x + block_x * (self.block_size // 2)
                    draw_y = center_y + block_y * (self.block_size // 2)
                    
                    # 创建半透明效果
                    faded_color = tuple(int(c * 0.7) for c in color[:3])
                    self.draw_preview_block(draw_x, draw_y, faded_color)
        else:
            # 显示空保留槽
            empty_text = self.font_small.render("空", True, CUTE_COLORS['TEXT'])
            empty_rect = empty_text.get_rect(center=(panel_x + hold_width // 2, panel_y + hold_height // 2))
            self.screen.blit(empty_text, empty_rect)
    
    def draw_game_over_screen(self, score_manager):
        """绘制游戏结束界面
        
        Args:
            score_manager: ScoreManager对象
        """
        # 半透明覆盖层
        overlay = pygame.Surface((GAME_CONFIG['WINDOW_WIDTH'], GAME_CONFIG['WINDOW_HEIGHT']))
        overlay.set_alpha(150)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))
        
        # 游戏结束面板
        panel_width = 400
        panel_height = 350
        panel_x = (GAME_CONFIG['WINDOW_WIDTH'] - panel_width) // 2
        panel_y = (GAME_CONFIG['WINDOW_HEIGHT'] - panel_height) // 2
        
        # 绘制面板背景
        pygame.draw.rect(self.screen, CUTE_COLORS['BACKGROUND'],
                        (panel_x, panel_y, panel_width, panel_height),
                        border_radius=15)
        pygame.draw.rect(self.screen, CUTE_COLORS['GRID'],
                        (panel_x, panel_y, panel_width, panel_height),
                        width=3, border_radius=15)
        
        # 游戏结束标题
        title_text = self.font_large.render("游戏结束", True, CUTE_COLORS['TEXT'])
        title_rect = title_text.get_rect(centerx=panel_x + panel_width // 2)
        title_rect.y = panel_y + 20
        self.screen.blit(title_text, title_rect)
        
        # 分数信息
        y_offset = panel_y + 80
        
        # 最终分数
        final_score = score_manager.get_current_score()
        score_text = self.font_medium.render(f"最终分数: {final_score}", True, CUTE_COLORS['TEXT'])
        score_rect = score_text.get_rect(centerx=panel_x + panel_width // 2)
        score_rect.y = y_offset
        self.screen.blit(score_text, score_rect)
        y_offset += 35
        
        # 等级和行数
        level_text = self.font_small.render(f"达到等级: {score_manager.get_current_level()}", True, CUTE_COLORS['TEXT'])
        level_rect = level_text.get_rect(centerx=panel_x + panel_width // 2)
        level_rect.y = y_offset
        self.screen.blit(level_text, level_rect)
        y_offset += 25
        
        lines_text = self.font_small.render(f"消除行数: {score_manager.get_lines_cleared()}", True, CUTE_COLORS['TEXT'])
        lines_rect = lines_text.get_rect(centerx=panel_x + panel_width // 2)
        lines_rect.y = y_offset
        self.screen.blit(lines_text, lines_rect)
        y_offset += 25
        
        # 游戏时间
        game_time = int(score_manager.get_game_time())
        minutes = game_time // 60
        seconds = game_time % 60
        time_text = self.font_small.render(f"游戏时间: {minutes:02d}:{seconds:02d}", True, CUTE_COLORS['TEXT'])
        time_rect = time_text.get_rect(centerx=panel_x + panel_width // 2)
        time_rect.y = y_offset
        self.screen.blit(time_text, time_rect)
        y_offset += 35
        
        # 最高分比较
        high_score = score_manager.get_high_score()
        if score_manager.is_new_high_score():
            new_record_text = self.font_medium.render("🎉 新纪录！ 🎉", True, (255, 215, 0))  # 金色
            new_record_rect = new_record_text.get_rect(centerx=panel_x + panel_width // 2)
            new_record_rect.y = y_offset
            self.screen.blit(new_record_text, new_record_rect)
            y_offset += 30
        else:
            high_score_text = self.font_small.render(f"最高分: {high_score}", True, CUTE_COLORS['TEXT'])
            high_score_rect = high_score_text.get_rect(centerx=panel_x + panel_width // 2)
            high_score_rect.y = y_offset
            self.screen.blit(high_score_text, high_score_rect)
            y_offset += 25
        
        # 等级评价
        grade = score_manager.calculate_grade()
        grade_color = self._get_grade_color(grade)
        grade_text = self.font_medium.render(f"评价: {grade}", True, grade_color)
        grade_rect = grade_text.get_rect(centerx=panel_x + panel_width // 2)
        grade_rect.y = y_offset
        self.screen.blit(grade_text, grade_rect)
        y_offset += 40
        
        # 分隔线
        line_y = y_offset
        pygame.draw.line(self.screen, CUTE_COLORS['GRID'],
                        (panel_x + 20, line_y), (panel_x + panel_width - 20, line_y), 2)
        y_offset += 20
        
        # 控制提示
        restart_text = self.font_small.render("按 R 重新开始", True, CUTE_COLORS['TEXT'])
        restart_rect = restart_text.get_rect(centerx=panel_x + panel_width // 2)
        restart_rect.y = y_offset
        self.screen.blit(restart_text, restart_rect)
        y_offset += 20
        
        quit_text = self.font_small.render("按 ESC 退出游戏", True, CUTE_COLORS['TEXT'])
        quit_rect = quit_text.get_rect(centerx=panel_x + panel_width // 2)
        quit_rect.y = y_offset
        self.screen.blit(quit_text, quit_rect)
    
    def draw_game_over_screen_simple(self, final_score):
        """绘制简单的游戏结束界面（向后兼容）
        
        Args:
            final_score: 最终分数
        """
        # 半透明覆盖层
        overlay = pygame.Surface((GAME_CONFIG['WINDOW_WIDTH'], GAME_CONFIG['WINDOW_HEIGHT']))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))
        
        # 游戏结束文本
        game_over_text = self.font_large.render("游戏结束", True, CUTE_COLORS['TEXT'])
        text_rect = game_over_text.get_rect(center=(GAME_CONFIG['WINDOW_WIDTH'] // 2, 
                                                   GAME_CONFIG['WINDOW_HEIGHT'] // 2 - 50))
        self.screen.blit(game_over_text, text_rect)
        
        # 最终分数
        score_text = self.font_medium.render(f"最终分数: {final_score}", True, CUTE_COLORS['TEXT'])
        score_rect = score_text.get_rect(center=(GAME_CONFIG['WINDOW_WIDTH'] // 2, 
                                               GAME_CONFIG['WINDOW_HEIGHT'] // 2))
        self.screen.blit(score_text, score_rect)
        
        # 重新开始提示
        restart_text = self.font_small.render("按 R 重新开始", True, CUTE_COLORS['TEXT'])
        restart_rect = restart_text.get_rect(center=(GAME_CONFIG['WINDOW_WIDTH'] // 2, 
                                                   GAME_CONFIG['WINDOW_HEIGHT'] // 2 + 50))
        self.screen.blit(restart_text, restart_rect)
    
    def draw_pause_screen(self):
        """绘制暂停界面"""
        # 半透明覆盖层
        overlay = pygame.Surface((GAME_CONFIG['WINDOW_WIDTH'], GAME_CONFIG['WINDOW_HEIGHT']))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))
        
        # 暂停文本
        pause_text = self.font_large.render("游戏暂停", True, CUTE_COLORS['TEXT'])
        text_rect = pause_text.get_rect(center=(GAME_CONFIG['WINDOW_WIDTH'] // 2, 
                                              GAME_CONFIG['WINDOW_HEIGHT'] // 2))
        self.screen.blit(pause_text, text_rect)
        
        # 继续提示
        continue_text = self.font_small.render("按 P 继续游戏", True, CUTE_COLORS['TEXT'])
        continue_rect = continue_text.get_rect(center=(GAME_CONFIG['WINDOW_WIDTH'] // 2, 
                                                     GAME_CONFIG['WINDOW_HEIGHT'] // 2 + 50))
        self.screen.blit(continue_text, continue_rect)
    
    def _get_grade_color(self, grade):
        """根据等级评价获取颜色
        
        Args:
            grade: 等级评价 (S, A, B, C, D)
            
        Returns:
            tuple: RGB颜色值
        """
        grade_colors = {
            'S': (255, 215, 0),    # 金色
            'A': (192, 192, 192),  # 银色
            'B': (205, 127, 50),   # 铜色
            'C': (128, 128, 128),  # 灰色
            'D': (139, 69, 19)     # 棕色
        }
        return grade_colors.get(grade, CUTE_COLORS['TEXT'])
    
    def draw_ghost_piece(self, tetromino, x, y, board):
        """绘制幽灵方块（显示下落位置）
        
        Args:
            tetromino: Tetromino对象
            x: 当前X坐标
            y: 当前Y坐标
            board: Board对象
        """
        # 获取下落位置
        drop_y = board.get_drop_position(tetromino, x, y)
        
        # 如果下落位置和当前位置相同，不绘制幽灵方块
        if drop_y == y:
            return
        
        blocks = tetromino.get_shape_blocks()
        color = tetromino.get_color()
        
        # 创建半透明的幽灵颜色
        ghost_color = (*color[:3], 80)  # 添加透明度
        
        for block_x, block_y in blocks:
            draw_x = x + block_x
            draw_y = drop_y + block_y
            
            # 只绘制在游戏板范围内的方块
            if draw_y >= 0 and draw_x >= 0 and draw_x < board.width and draw_y < board.height:
                self.draw_ghost_block(draw_x, draw_y, ghost_color)
    
    def draw_ghost_block(self, x, y, color):
        """绘制幽灵方块
        
        Args:
            x: 网格X坐标
            y: 网格Y坐标
            color: 方块颜色（包含透明度）
        """
        block_x = self.board_x + x * self.block_size
        block_y = self.board_y + y * self.block_size
        
        # 创建透明表面
        ghost_surface = pygame.Surface((self.block_size - 2, self.block_size - 2), pygame.SRCALPHA)
        
        # 绘制透明方块轮廓
        rect = pygame.Rect(0, 0, self.block_size - 2, self.block_size - 2)
        pygame.draw.rect(ghost_surface, color[:3], rect, width=2, border_radius=5)
        
        # 绘制到屏幕
        self.screen.blit(ghost_surface, (block_x + 1, block_y + 1))
    
    def draw_controls_help(self):
        """绘制控制说明"""
        help_x = 20
        help_y = GAME_CONFIG['WINDOW_HEIGHT'] - 120
        
        # 绘制帮助背景
        help_width = 200
        help_height = 100
        pygame.draw.rect(self.screen, CUTE_COLORS['BACKGROUND'],
                        (help_x, help_y, help_width, help_height),
                        border_radius=8)
        pygame.draw.rect(self.screen, CUTE_COLORS['GRID'],
                        (help_x, help_y, help_width, help_height),
                        width=1, border_radius=8)
        
        # 控制说明文本
        controls = [
            "控制说明:",
            "← → 移动",
            "↓ 软下落",
            "↑ 旋转",
            "P 暂停"
        ]
        
        y_offset = help_y + 10
        for control in controls:
            if control == "控制说明:":
                text = self.font_small.render(control, True, CUTE_COLORS['TEXT'])
            else:
                text = self.font_small.render(control, True, CUTE_COLORS['TEXT'])
            self.screen.blit(text, (help_x + 10, y_offset))
            y_offset += 16
    
    def draw_level_progress_bar(self, score_manager):
        """绘制等级进度条
        
        Args:
            score_manager: ScoreManager对象
        """
        bar_x = self.board_x + GAME_CONFIG['BOARD_WIDTH'] * self.block_size + 40
        bar_y = self.board_y + 200
        bar_width = 180
        bar_height = 10
        
        # 计算进度
        lines_for_current_level = (score_manager.get_current_level() - 1) * GAME_CONFIG['LINES_PER_LEVEL']
        lines_for_next_level = score_manager.get_current_level() * GAME_CONFIG['LINES_PER_LEVEL']
        current_progress = score_manager.get_lines_cleared() - lines_for_current_level
        total_needed = lines_for_next_level - lines_for_current_level
        
        progress_ratio = current_progress / total_needed if total_needed > 0 else 0
        
        # 绘制进度条背景
        pygame.draw.rect(self.screen, CUTE_COLORS['GRID'],
                        (bar_x, bar_y, bar_width, bar_height),
                        border_radius=5)
        
        # 绘制进度条填充
        if progress_ratio > 0:
            fill_width = int(bar_width * progress_ratio)
            pygame.draw.rect(self.screen, CUTE_COLORS['J'],  # 使用浅蓝色
                            (bar_x, bar_y, fill_width, bar_height),
                            border_radius=5)
        
        # 进度文本
        progress_text = self.font_small.render(f"等级进度: {int(progress_ratio * 100)}%", 
                                             True, CUTE_COLORS['TEXT'])
        self.screen.blit(progress_text, (bar_x, bar_y - 20))
    
    def draw_background(self):
        """绘制游戏背景"""
        # 填充背景色
        self.screen.fill(CUTE_COLORS['BACKGROUND'])
        
        # 可以添加更多背景装饰，如渐变、图案等
        # 这里保持简洁的可爱风格
    
    def draw_title(self):
        """绘制游戏标题"""
        title_text = self.font_large.render("可爱俄罗斯方块", True, CUTE_COLORS['TEXT'])
        title_rect = title_text.get_rect(centerx=GAME_CONFIG['WINDOW_WIDTH'] // 2)
        title_rect.y = 10
        self.screen.blit(title_text, title_rect)
    
    def draw_line_clear_animation(self, animation_data, animation_progress):
        """
        绘制增强的行消除动画
        
        Args:
            animation_data: 动画数据
            animation_progress: 动画进度 (0.0 到 1.0)
        """
        if not animation_data or animation_progress >= 1.0:
            return
        
        # 多阶段动画效果
        if animation_progress < 0.3:
            # 第一阶段：闪烁效果
            phase_progress = animation_progress / 0.3
            self._draw_line_clear_flash(animation_data, phase_progress)
        elif animation_progress < 0.6:
            # 第二阶段：星星粒子效果
            phase_progress = (animation_progress - 0.3) / 0.3
            self._draw_line_clear_particles(animation_data, phase_progress)
        else:
            # 第三阶段：收缩效果
            phase_progress = (animation_progress - 0.6) / 0.4
            self._draw_line_clear_shrink(animation_data, phase_progress)
    
    def _draw_line_clear_flash(self, animation_data, progress):
        """绘制行消除的闪烁效果"""
        # 彩虹闪烁效果
        rainbow_colors = [
            (255, 105, 180),  # 热粉红
            (255, 165, 0),   # 橙色
            (255, 255, 0),   # 黄色
            (0, 255, 127),   # 春绿
            (0, 191, 255),   # 深天蓝
            (138, 43, 226)   # 紫罗兰
        ]
        
        # 根据进度选择颜色
        color_index = int(progress * 10) % len(rainbow_colors)
        next_index = (color_index + 1) % len(rainbow_colors)
        blend_factor = (progress * 10) % 1.0
        
        # 混合两个颜色
        current_color = rainbow_colors[color_index]
        next_color = rainbow_colors[next_index]
        flash_color = tuple(
            int(current_color[i] * (1 - blend_factor) + next_color[i] * blend_factor)
            for i in range(3)
        ) + (200,)  # 添加透明度
        
        for line_num in animation_data['full_lines']:
            # 绘制闪烁的行
            line_y = self.board_y + line_num * self.block_size
            line_rect = pygame.Rect(self.board_x, line_y, 
                                  GAME_CONFIG['BOARD_WIDTH'] * self.block_size, 
                                  self.block_size)
            
            # 创建闪烁表面
            flash_surface = pygame.Surface((line_rect.width, line_rect.height), pygame.SRCALPHA)
            flash_surface.fill(flash_color)
            self.screen.blit(flash_surface, (line_rect.x, line_rect.y))
    
    def _draw_line_clear_particles(self, animation_data, progress):
        """绘制行消除的粒子效果"""
        import random
        
        for line_num in animation_data['full_lines']:
            line_y = self.board_y + line_num * self.block_size
            line_width = GAME_CONFIG['BOARD_WIDTH'] * self.block_size
            
            # 生成粒子
            num_particles = 20
            for i in range(num_particles):
                # 随机位置
                particle_x = self.board_x + random.randint(0, line_width)
                particle_y = line_y + random.randint(0, self.block_size)
                
                # 随机大小和颜色
                size = random.randint(2, 5)
                color = (255, 255, 255, int(255 * (1 - progress)))
                
                # 绘制星星形状
                self._draw_star(particle_x, particle_y, size, color)
    
    def _draw_star(self, x, y, size, color):
        """绘制星星形状"""
        points = []
        for i in range(10):
            angle = i * 36 * (3.14159 / 180)  # 转换为弧度
            radius = size if i % 2 == 0 else size / 2
            point_x = x + radius * pygame.math.Vector2.from_polar((1, angle))[0]
            point_y = y + radius * pygame.math.Vector2.from_polar((1, angle))[1]
            points.append((point_x, point_y))
        
        pygame.draw.polygon(self.screen, color, points)
    
    def _draw_line_clear_shrink(self, animation_data, progress):
        """绘制行消除的收缩效果"""
        for line_num in animation_data['full_lines']:
            line_y = self.board_y + line_num * self.block_size
            full_width = GAME_CONFIG['BOARD_WIDTH'] * self.block_size
            
            # 计算收缩宽度
            current_width = full_width * (1 - progress)
            x_offset = (full_width - current_width) / 2
            
            # 绘制收缩的行
            line_rect = pygame.Rect(self.board_x + x_offset, line_y, current_width, self.block_size)
            pygame.draw.rect(self.screen, (255, 255, 255, 100), line_rect, border_radius=int(progress * 10))
            
            # 添加发光效果
            glow_color = (255, 255, 255, int(100 * (1 - progress)))
            glow_size = int(10 * (1 - progress))
            glow_rect = pygame.Rect(
                line_rect.x - glow_size, 
                line_rect.y - glow_size // 2,
                line_rect.width + glow_size * 2, 
                line_rect.height + glow_size
            )
            pygame.draw.rect(self.screen, glow_color, glow_rect, border_radius=int(progress * 15))
    
    def draw_score_popup(self, score, x, y, animation_progress):
        """绘制增强的分数弹出动画
        
        Args:
            score: 获得的分数
            x: X坐标
            y: Y坐标
            animation_progress: 动画进度 (0.0 到 1.0)
        """
        if animation_progress >= 1.0:
            return
        
        import random
        import math
        
        # 动画阶段划分
        bounce_phase = min(1.0, animation_progress * 2)  # 0-0.5 => 0-1.0
        fade_phase = max(0.0, (animation_progress - 0.5) * 2)  # 0.5-1.0 => 0-1.0
        
        # 弹跳效果 - 使用弹性函数
        if bounce_phase < 1.0:
            # 弹跳函数：先快速上升，然后轻微回弹
            bounce_progress = 1 - math.pow(1 - bounce_phase, 3)  # 缓动函数
            bounce_height = 40 * bounce_progress
            
            # 添加轻微的回弹
            if bounce_phase > 0.7:
                bounce_height += math.sin((bounce_phase - 0.7) * 10) * 5
        else:
            bounce_height = 40
        
        # 计算弹出位置
        popup_y = y - bounce_height
        
        # 缩放效果 - 先放大后恢复
        if bounce_phase < 0.3:
            # 快速放大到1.5倍
            scale = 1.0 + (bounce_phase / 0.3) * 0.5
        elif bounce_phase < 0.5:
            # 缓慢恢复到正常大小
            scale = 1.5 - ((bounce_phase - 0.3) / 0.2) * 0.3
        else:
            # 保持轻微放大状态
            scale = 1.2 - fade_phase * 0.2
        
        # 颜色变化 - 根据分数大小选择颜色
        if score < 100:
            base_color = CUTE_COLORS['SCORE_LOW']
        elif score < 800:
            base_color = CUTE_COLORS['SCORE_MID']
        else:
            base_color = CUTE_COLORS['SCORE_HIGH']
        
        # 颜色脉动效果
        pulse = (math.sin(animation_progress * 10) + 1) * 0.5  # 0-1范围内脉动
        color = tuple(min(255, c + int(pulse * 50)) for c in base_color[:3])
        
        # 透明度
        alpha = int(255 * (1 - fade_phase * fade_phase))  # 使用平方函数使淡出更自然
        
        # 创建分数文本
        font_size = int(self.font_medium.get_height() * scale)
        dynamic_font = pygame.font.Font(None, font_size)
        score_text = dynamic_font.render(f"+{score}", True, color)
        
        # 创建带透明度的表面
        text_surface = pygame.Surface(score_text.get_size(), pygame.SRCALPHA)
        text_surface.fill((0, 0, 0, 0))  # 清空表面
        text_surface.blit(score_text, (0, 0))
        
        # 应用透明度
        for x_pos in range(text_surface.get_width()):
            for y_pos in range(text_surface.get_height()):
                pixel = text_surface.get_at((x_pos, y_pos))
                if pixel[3] > 0:  # 如果像素不是完全透明的
                    text_surface.set_at((x_pos, y_pos), 
                                       (pixel[0], pixel[1], pixel[2], 
                                        min(pixel[3], alpha)))
        
        # 绘制到屏幕
        text_rect = text_surface.get_rect(center=(x, popup_y))
        self.screen.blit(text_surface, text_rect)
        
        # 添加粒子效果 - 只在前半段动画显示
        if animation_progress < 0.5:
            self._draw_score_particles(x, popup_y, score, animation_progress)
    
    def _draw_score_particles(self, x, y, score, progress):
        """绘制分数粒子效果"""
        import random
        import math
        
        # 粒子数量根据分数变化
        num_particles = min(20, 5 + int(score / 100))
        
        for i in range(num_particles):
            # 计算粒子位置 - 从中心向外扩散
            angle = random.uniform(0, math.pi * 2)
            distance = random.uniform(10, 30) * progress
            particle_x = x + math.cos(angle) * distance
            particle_y = y + math.sin(angle) * distance
            
            # 粒子大小和颜色
            size = random.randint(2, 4)
            r = random.randint(200, 255)
            g = random.randint(200, 255)
            b = random.randint(100, 255)
            alpha = int(255 * (1 - progress * 2))
            color = (r, g, b, alpha)
            
            # 绘制粒子 - 小星星或圆形
            if random.random() > 0.7:
                # 星星
                self._draw_star(particle_x, particle_y, size, color)
            else:
                # 圆形
                pygame.draw.circle(self.screen, color, (int(particle_x), int(particle_y)), size)
    
    def get_board_rect(self):
        """获取游戏板的矩形区域
        
        Returns:
            pygame.Rect: 游戏板矩形
        """
        return pygame.Rect(self.board_x, self.board_y,
                          GAME_CONFIG['BOARD_WIDTH'] * self.block_size,
                          GAME_CONFIG['BOARD_HEIGHT'] * self.block_size)
    
    def screen_to_board_coords(self, screen_x, screen_y):
        """将屏幕坐标转换为游戏板坐标
        
        Args:
            screen_x: 屏幕X坐标
            screen_y: 屏幕Y坐标
            
        Returns:
            tuple: (board_x, board_y) 或 None（如果不在游戏板内）
        """
        board_rect = self.get_board_rect()
        if not board_rect.collidepoint(screen_x, screen_y):
            return None
        
        board_x = (screen_x - self.board_x) // self.block_size
        board_y = (screen_y - self.board_y) // self.block_size
        
        return (board_x, board_y)