# 分数管理器
import json
import os
import time
from datetime import datetime
from config import GAME_CONFIG

class ScoreManager:
    """分数管理器，处理分数计算和等级系统"""
    
    def __init__(self):
        """初始化分数管理器"""
        self.score = 0
        self.lines_cleared = 0
        self.level = 1
        self.total_pieces_placed = 0
        self.game_start_time = time.time()
        self.level_up_scores = []  # 记录每次升级时的分数
        self.high_score_data = self.load_high_score_data()
        self.high_score = self.high_score_data.get('score', 0)
        
        # 分数计算配置
        self.base_scores = {
            1: 100,   # 单行
            2: 300,   # 双行
            3: 500,   # 三行
            4: 800    # 四行（Tetris）
        }
        
        # 额外奖励分数
        self.soft_drop_points = 1      # 软下落每格1分
        self.hard_drop_points = 2      # 硬下落每格2分
        self.placement_bonus = 10      # 方块放置奖励
    
    def add_line_score(self, lines_cleared):
        """根据消除的行数添加分数
        
        Args:
            lines_cleared: 消除的行数
            
        Returns:
            int: 本次获得的分数
        """
        if lines_cleared == 0:
            return 0
        
        # 基础分数计算
        base_score = self.base_scores.get(lines_cleared, 100 * lines_cleared)
        
        # 等级奖励乘数
        level_multiplier = self.level
        
        # 连击奖励（如果有的话，这里简化处理）
        combo_bonus = 1.0
        
        # 计算最终分数
        earned_score = int(base_score * level_multiplier * combo_bonus)
        
        # 更新分数和统计
        self.score += earned_score
        self.lines_cleared += lines_cleared
        
        # 检查是否升级
        old_level = self.level
        self.check_level_up()
        
        # 如果升级了，记录升级时的分数
        if self.level > old_level:
            self.level_up_scores.append({
                'level': self.level,
                'score': self.score,
                'lines': self.lines_cleared,
                'time': time.time() - self.game_start_time
            })
        
        return earned_score
    
    def add_drop_score(self, drop_distance, is_hard_drop=False):
        """添加下落分数
        
        Args:
            drop_distance: 下落距离
            is_hard_drop: 是否为硬下落
            
        Returns:
            int: 获得的分数
        """
        if drop_distance <= 0:
            return 0
        
        points_per_cell = self.hard_drop_points if is_hard_drop else self.soft_drop_points
        earned_score = drop_distance * points_per_cell
        self.score += earned_score
        
        return earned_score
    
    def add_placement_bonus(self):
        """添加方块放置奖励分数
        
        Returns:
            int: 获得的奖励分数
        """
        self.score += self.placement_bonus
        self.total_pieces_placed += 1
        return self.placement_bonus
    
    def check_level_up(self):
        """检查并处理等级提升"""
        new_level = (self.lines_cleared // GAME_CONFIG['LINES_PER_LEVEL']) + 1
        if new_level > self.level:
            self.level = new_level
            return True
        return False
    
    def get_current_score(self):
        """获取当前分数"""
        return self.score
    
    def get_current_level(self):
        """获取当前等级"""
        return self.level
    
    def get_lines_cleared(self):
        """获取已消除的行数"""
        return self.lines_cleared
    
    def get_total_pieces_placed(self):
        """获取已放置的方块总数"""
        return self.total_pieces_placed
    
    def get_lines_to_next_level(self):
        """获取距离下一级还需要消除的行数"""
        lines_for_next_level = self.level * GAME_CONFIG['LINES_PER_LEVEL']
        return max(0, lines_for_next_level - self.lines_cleared)
    
    def get_game_time(self):
        """获取游戏时间（秒）"""
        return time.time() - self.game_start_time
    
    def get_lines_per_minute(self):
        """获取每分钟消除行数"""
        game_time_minutes = self.get_game_time() / 60
        if game_time_minutes > 0:
            return self.lines_cleared / game_time_minutes
        return 0
    
    def get_pieces_per_minute(self):
        """获取每分钟放置方块数"""
        game_time_minutes = self.get_game_time() / 60
        if game_time_minutes > 0:
            return self.total_pieces_placed / game_time_minutes
        return 0
    
    def get_fall_speed(self):
        """根据等级获取下落速度（毫秒）"""
        speed = GAME_CONFIG['INITIAL_FALL_SPEED']
        for _ in range(self.level - 1):
            speed = int(speed * GAME_CONFIG['SPEED_INCREASE_RATE'])
        return max(speed, 50)  # 最快50毫秒
    
    def get_score_breakdown(self):
        """获取分数详细信息
        
        Returns:
            dict: 分数详细信息
        """
        return {
            'total_score': self.score,
            'level': self.level,
            'lines_cleared': self.lines_cleared,
            'pieces_placed': self.total_pieces_placed,
            'game_time': self.get_game_time(),
            'lines_per_minute': self.get_lines_per_minute(),
            'pieces_per_minute': self.get_pieces_per_minute(),
            'lines_to_next_level': self.get_lines_to_next_level(),
            'level_up_history': self.level_up_scores[:]
        }
    
    def save_high_score(self):
        """保存高分记录"""
        current_data = {
            'score': self.score,
            'level': self.level,
            'lines_cleared': self.lines_cleared,
            'pieces_placed': self.total_pieces_placed,
            'game_time': self.get_game_time(),
            'date': datetime.now().isoformat(),
            'level_up_history': self.level_up_scores
        }
        
        # 如果当前分数更高，更新高分记录
        if self.score > self.high_score:
            self.high_score = self.score
            self.high_score_data = current_data
            
            try:
                with open('high_score.json', 'w', encoding='utf-8') as f:
                    json.dump(self.high_score_data, f, indent=2, ensure_ascii=False)
                return True
            except Exception as e:
                print(f"保存高分失败: {e}")
                return False
        
        return False
    
    def load_high_score_data(self):
        """加载高分记录数据"""
        try:
            if os.path.exists('high_score.json'):
                with open('high_score.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载高分失败: {e}")
        
        # 返回默认数据
        return {
            'score': 0,
            'level': 1,
            'lines_cleared': 0,
            'pieces_placed': 0,
            'game_time': 0,
            'date': None,
            'level_up_history': []
        }
    
    def get_high_score(self):
        """获取历史最高分"""
        return self.high_score
    
    def get_high_score_data(self):
        """获取完整的高分记录数据"""
        return self.high_score_data.copy()
    
    def is_new_high_score(self):
        """检查当前分数是否为新的最高分"""
        return self.score > self.high_score
    
    def calculate_grade(self):
        """根据表现计算等级评价
        
        Returns:
            str: 等级评价 (S, A, B, C, D)
        """
        # 基于多个因素计算等级
        score_factor = min(self.score / 100000, 1.0)  # 分数因子（最高10万分）
        level_factor = min(self.level / 20, 1.0)      # 等级因子（最高20级）
        efficiency_factor = min(self.get_lines_per_minute() / 60, 1.0)  # 效率因子
        
        # 综合评分
        total_factor = (score_factor + level_factor + efficiency_factor) / 3
        
        if total_factor >= 0.9:
            return 'S'
        elif total_factor >= 0.7:
            return 'A'
        elif total_factor >= 0.5:
            return 'B'
        elif total_factor >= 0.3:
            return 'C'
        else:
            return 'D'
    
    def reset(self):
        """重置分数（保留高分记录）"""
        self.score = 0
        self.lines_cleared = 0
        self.level = 1
        self.total_pieces_placed = 0
        self.game_start_time = time.time()
        self.level_up_scores = []
    
    def get_statistics(self):
        """获取游戏统计信息"""
        return {
            'current_session': self.get_score_breakdown(),
            'high_score_record': self.get_high_score_data(),
            'performance_grade': self.calculate_grade(),
            'is_new_record': self.is_new_high_score()
        }
    
    def __str__(self):
        """字符串表示"""
        return f"Score: {self.score}, Level: {self.level}, Lines: {self.lines_cleared}"
    
    def __repr__(self):
        """详细字符串表示"""
        return f"ScoreManager(score={self.score}, level={self.level}, lines={self.lines_cleared})"