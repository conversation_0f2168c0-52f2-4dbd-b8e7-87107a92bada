# 行消除系统的单元测试
import unittest
from board import Board
from tetromino import Tetromino

class TestLineClearing(unittest.TestCase):
    """行消除系统的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.board = Board()
    
    def test_single_line_clear(self):
        """测试单行消除"""
        # 填满底部一行
        for x in range(self.board.width):
            self.board.set_cell(x, 19, (255, 0, 0))
        
        # 在上方放一些方块
        self.board.set_cell(5, 18, (0, 255, 0))
        self.board.set_cell(6, 17, (0, 0, 255))
        
        initial_filled = self.board.get_filled_cells_count()
        lines_cleared, cleared_lines = self.board.clear_lines()
        
        # 应该清除了1行
        self.assertEqual(lines_cleared, 1)
        self.assertEqual(cleared_lines, [19])
        
        # 填充的格子数应该减少10个（一行）
        self.assertEqual(self.board.get_filled_cells_count(), initial_filled - 10)
        
        # 上方的方块应该下移
        self.assertIsNotNone(self.board.get_cell(5, 19))  # 原来在18行的方块下移到19行
        self.assertIsNotNone(self.board.get_cell(6, 18))  # 原来在17行的方块下移到18行
        
        # 底部行应该为空（新添加的空行）
        self.assertTrue(self.board.is_line_empty(0))
    
    def test_multiple_line_clear(self):
        """测试多行消除"""
        # 填满底部三行
        for y in [17, 18, 19]:
            for x in range(self.board.width):
                self.board.set_cell(x, y, (255, 0, 0))
        
        # 在上方放一些方块
        self.board.set_cell(3, 16, (0, 255, 0))
        self.board.set_cell(4, 15, (0, 0, 255))
        
        lines_cleared, cleared_lines = self.board.clear_lines()
        
        # 应该清除了3行
        self.assertEqual(lines_cleared, 3)
        self.assertEqual(set(cleared_lines), {17, 18, 19})
        
        # 上方的方块应该下移3行
        self.assertIsNotNone(self.board.get_cell(3, 19))  # 从16行下移到19行
        self.assertIsNotNone(self.board.get_cell(4, 18))  # 从15行下移到18行
    
    def test_tetris_clear(self):
        """测试四行消除（Tetris）"""
        # 填满底部四行
        for y in [16, 17, 18, 19]:
            for x in range(self.board.width):
                self.board.set_cell(x, y, (255, 0, 0))
        
        lines_cleared, cleared_lines = self.board.clear_lines()
        
        # 应该清除了4行
        self.assertEqual(lines_cleared, 4)
        self.assertEqual(len(cleared_lines), 4)
        
        # 底部四行应该都为空
        for y in [16, 17, 18, 19]:
            self.assertTrue(self.board.is_line_empty(y))
    
    def test_non_consecutive_line_clear(self):
        """测试非连续行消除"""
        # 填满第17行和第19行，第18行留空
        for x in range(self.board.width):
            self.board.set_cell(x, 17, (255, 0, 0))
            self.board.set_cell(x, 19, (0, 255, 0))
        
        # 在第18行放一些方块（不填满）
        for x in range(5):
            self.board.set_cell(x, 18, (0, 0, 255))
        
        lines_cleared, cleared_lines = self.board.clear_lines()
        
        # 应该清除了2行（17和19）
        self.assertEqual(lines_cleared, 2)
        self.assertEqual(set(cleared_lines), {17, 19})
        
        # 第18行的部分方块应该下移到第19行
        for x in range(5):
            self.assertIsNotNone(self.board.get_cell(x, 19))
        for x in range(5, 10):
            self.assertIsNone(self.board.get_cell(x, 19))
    
    def test_get_full_lines(self):
        """测试获取完整行列表"""
        # 初始状态没有完整行
        full_lines = self.board.get_full_lines()
        self.assertEqual(full_lines, [])
        
        # 填满几行
        for x in range(self.board.width):
            self.board.set_cell(x, 18, (255, 0, 0))
            self.board.set_cell(x, 19, (0, 255, 0))
        
        full_lines = self.board.get_full_lines()
        self.assertEqual(set(full_lines), {18, 19})
    
    def test_clear_specific_lines(self):
        """测试清除指定行"""
        # 填满几行
        for y in [17, 18, 19]:
            for x in range(self.board.width):
                self.board.set_cell(x, y, (255, 0, 0))
        
        # 只清除指定的行
        cleared_count = self.board.clear_specific_lines([18, 19])
        
        # 应该清除了2行
        self.assertEqual(cleared_count, 2)
        
        # 第17行应该还在，但下移到第19行
        self.assertTrue(self.board.is_line_full(19))
        self.assertTrue(self.board.is_line_empty(17))
        self.assertTrue(self.board.is_line_empty(18))
    
    def test_simulate_line_clear(self):
        """测试模拟行消除"""
        # 填满底部两行
        for y in [18, 19]:
            for x in range(self.board.width):
                self.board.set_cell(x, y, (255, 0, 0))
        
        # 模拟行消除
        lines_to_clear = self.board.simulate_line_clear()
        
        # 应该返回会被清除的行
        self.assertEqual(set(lines_to_clear), {18, 19})
        
        # 但实际上没有清除
        self.assertTrue(self.board.is_line_full(18))
        self.assertTrue(self.board.is_line_full(19))
    
    def test_line_clear_animation_data(self):
        """测试行消除动画数据"""
        # 填满底部两行，使用不同颜色
        for x in range(self.board.width):
            self.board.set_cell(x, 18, (255, 0, 0))  # 红色
            self.board.set_cell(x, 19, (0, 255, 0))  # 绿色
        
        animation_data = self.board.get_line_clear_animation_data()
        
        # 检查动画数据结构
        self.assertIn('full_lines', animation_data)
        self.assertIn('line_count', animation_data)
        self.assertIn('is_tetris', animation_data)
        self.assertIn('affected_blocks', animation_data)
        
        # 检查数据内容
        self.assertEqual(set(animation_data['full_lines']), {18, 19})
        self.assertEqual(animation_data['line_count'], 2)
        self.assertFalse(animation_data['is_tetris'])  # 不是四行消除
        self.assertEqual(len(animation_data['affected_blocks']), 2)  # 两行数据
        
        # 检查方块数据
        for line_blocks in animation_data['affected_blocks']:
            self.assertEqual(len(line_blocks), 10)  # 每行10个方块
            for block in line_blocks:
                self.assertIn('x', block)
                self.assertIn('y', block)
                self.assertIn('color', block)
    
    def test_tetris_animation_data(self):
        """测试四行消除的动画数据"""
        # 填满底部四行
        for y in [16, 17, 18, 19]:
            for x in range(self.board.width):
                self.board.set_cell(x, y, (255, 0, 0))
        
        animation_data = self.board.get_line_clear_animation_data()
        
        # 应该标记为Tetris
        self.assertTrue(animation_data['is_tetris'])
        self.assertEqual(animation_data['line_count'], 4)
    
    def test_partial_line_no_clear(self):
        """测试部分填充的行不会被清除"""
        # 部分填充一行
        for x in range(5):  # 只填充一半
            self.board.set_cell(x, 19, (255, 0, 0))
        
        lines_cleared, cleared_lines = self.board.clear_lines()
        
        # 不应该清除任何行
        self.assertEqual(lines_cleared, 0)
        self.assertEqual(cleared_lines, [])
        
        # 方块应该还在
        for x in range(5):
            self.assertIsNotNone(self.board.get_cell(x, 19))
    
    def test_line_clear_with_tetromino_placement(self):
        """测试方块放置后的行消除"""
        # 填充底行，留几个空位
        for x in range(8):
            self.board.set_cell(x, 19, (255, 0, 0))
        
        # 直接填补剩余空位
        self.board.set_cell(8, 19, (0, 255, 0))
        self.board.set_cell(9, 19, (0, 0, 255))
        
        # 现在应该有完整的行
        self.assertTrue(self.board.is_line_full(19))
        
        # 清除行
        lines_cleared, cleared_lines = self.board.clear_lines()
        self.assertGreater(lines_cleared, 0)
    
    def test_empty_board_no_clear(self):
        """测试空板不会清除任何行"""
        lines_cleared, cleared_lines = self.board.clear_lines()
        
        self.assertEqual(lines_cleared, 0)
        self.assertEqual(cleared_lines, [])
        
        full_lines = self.board.get_full_lines()
        self.assertEqual(full_lines, [])

if __name__ == '__main__':
    unittest.main()