# -*- coding: utf-8 -*-
# @Time    : 2019-11-04 20:14
# <AUTHOR> xiayk10729
# @Email   : <EMAIL>
# @File    : qryjira.py
# @Software: PyCharm
#打包命令：  cd /D E:\python379\Scripts     pyinstaller.exe -F E:\FineReport_10.0\workspace\py_workspace\zgReport\jira_count.py

import time
import re
import requests
from jira import JIRA
import pandas as pd
import configParser
import oracle as oracle
from datetime import datetime, timedelta
import myLogger


# userName = load_workbook(filename='test.xlsx', read_only=True)['config']['D2'].value
# passwd = load_workbook(filename='test.xlsx', read_only=True)['config']['D3'].value
config = configParser.myConfigParser()
userName = config.getConfigValue("common", "hs_account")
passwd = config.getConfigValue("common", "hs_account_passwd")
jira_query_month = config.getConfigValue("JIRA", "jira_query_month")
db_oracle_ip = config.getConfigValue("DB", "db_oracle_ip")
db_oracle_port = config.getConfigValue("DB", "db_oracle_port")
db_oracle_SID = config.getConfigValue("DB", "db_oracle_SID")
db_oracle_user = config.getConfigValue("DB", "db_oracle_user")
db_oracle_passwd = config.getConfigValue("DB", "db_oracle_passwd")
# OracleUtil = oracle.OracleUtil();
ora = oracle.OracleUtil(db_oracle_ip, db_oracle_SID, db_oracle_port, db_oracle_user, db_oracle_passwd)

def getCookieOptionDict(userName, passwd):
    session = requests.Session()
    loginUrl = "https://hs-cas.hundsun.com/cas/login?service=https%3A%2F%2Fse.hundsun.com%2F"
    get = session.get(loginUrl)
    login_url = get.url
    text = get.text
    lt = re.findall('name="lt" value="(.*?)" />', text)[0]
    # 登录数据
    data = {"username": userName, "password": passwd, "lt": lt, "execution": 'e1s1',
            "_eventId": "submit", "submit": ""}
    res = session.post(login_url, data=data)
    resd = requests.utils.dict_from_cookiejar(session.cookies)


    cookieStr = "UM_distinctid=16bd4960dbc127-0fc98489b208bb-e343166-1fa400-16bd4960dc0538;JSESSIONID=" + \
                resd['JSESSIONID']  + "; atlassian.xsrf.token=" + resd['atlassian.xsrf.token']
    # cookieStr = "UM_distinctid=16a534833c0182-07bdab4c0f487-e323069-100200-16a534833c134d; _ga=GA1.2.1533132202.1565322716;atlassian.xsrf.token=" + \
    #               resd['atlassian.xsrf.token'] + "; jira.editor.user.mode=wysiwyg;JSESSIONID=" + resd['JSESSIONID']

    print("登录成功")
    return {"headers": {"Cookie": cookieStr}}

def getCookieOptionDict2(userName, passwd):
    session = requests.Session()
    loginUrl = "https://hs-cas.hundsun.com/cas/login?service=https%3A%2F%2Fse.hundsun.com%2F"
    get = session.get(loginUrl)
    login_url = get.url
    text = get.text
    lt = re.findall('name="lt" value="(.*?)" />', text)[0]
    # 登录数据
    data = {"username": userName, "password": passwd, "lt": lt, "execution": 'e1s1',
            "_eventId": "submit", "submit": ""}
    res = session.post(login_url, data=data)
    resd = requests.utils.dict_from_cookiejar(session.cookies)
    # print(resd)

    cookieStr = "UM_distinctid=16bd4960dbc127-0fc98489b208bb-e343166-1fa400-16bd4960dc0538;jira.editor.user.mode=wysiwyg;JSESSIONID=" + \
                resd['JSESSIONID'] + "; atlassian.xsrf.token=" + resd['atlassian.xsrf.token']
    # cookieStr = "UM_distinctid=16a534833c0182-07bdab4c0f487-e323069-100200-16a534833c134d; _ga=GA1.2.1533132202.1565322716;atlassian.xsrf.token=" + \
    #               resd['atlassian.xsrf.token'] + "; jira.editor.user.mode=wysiwyg;JSESSIONID=" + resd['JSESSIONID']

    return {"Cookie": cookieStr}


def search_bugs(JQL, start_date, end_date, jira):

    bugjql =f"{JQL} AND created >= {start_date} AND created <= {end_date} ORDER BY priority DESC, updated DESC"
    #bugjql =f"key in (FAISRDL-23743,FAISRDL-23747,FAISRDL-23748,FAISRDL-23742)"

    print(bugjql)
    write_log.info(f"执行SQL: {bugjql}")
    # issue_object.raw 可以返回所查issue 的所有filed
    # 一个bugs里面几列分别是id,严重级别,测试人员,状态,开发人员,提交日期,版本
    all_bugs = []

    # print(time.asctime(time.localtime(time.time())) + '获取BUG开始')
    # iss = jira.search_issues(bugjql, maxResults=False)
    # write_log.info(f"查询JIRA结束")
    exceptions = []
    #每次查1000条记录，最多查100次，实际一年缺陷不会超过10W，每年差不多6-8W缺陷
    stepCount = 1000
    for i in range(0,100):
        write_log.info(f"开始查询jira{i*stepCount+1}到{(i+1)*stepCount}的数据")
        iss = jira.search_issues(bugjql, startAt=i*stepCount,maxResults=stepCount)
        write_log.info(f"开始查询jira{i*stepCount+1}到{(i+1)*stepCount}的数据完成")
        if(len(iss)<1):
            break
        for issue in iss:
            try:
                bugs = []
                # bugs.append(str(issue.key)[-5:])  # 只保留缺陷编号，前面的IPS-去掉
                # print(issue.key)
                bugs.append(issue.key)  # 缺陷编号
                bugs.append(issue.fields.priority.name)  # 优先级
                bugs.append(issue.fields.reporter.displayName)  # 报告人
                bugs.append(issue.fields.status.name)  # 缺陷状态

                # 经办人空的情况，把经办人置为未分配
                if issue.fields.assignee is None:
                    bugs.append("未分配")
                else:
                    bugs.append(issue.fields.assignee.displayName)

                bugs.append(issue.fields.created[:10])  # 创建日期
                if issue.raw['fields']['customfield_11029']:  # 版本
                    bugs.append(issue.raw['fields']['customfield_11029'][0]['name'])
                else:
                    bugs.append("空")
                bugs.append(issue.raw['fields']['customfield_11021']['value'])  # 发现阶段
                bugs.append(issue.raw['fields']['customfield_11023']['value'])  # 发现方式
                # bugs.append(issue.raw['fields']['customfield_11783']['value'])  # 前台OR后台
                if issue.raw['fields']['customfield_11783']:  # 前台OR后台
                    bugs.append(issue.raw['fields']['customfield_11783']['value'])
                else:
                    bugs.append("后台修改")

                bugs.append(issue.raw['fields']['updated'][:10])  # 更新日期

                # bugs.append(issue.raw['fields']['customfield_11681'].get('displayName'))  # 测试验证人
                if issue.raw['fields']['customfield_11681'] is None:  # 测试验证人为空的情况，把验证人置为报告人
                    bugs.append(issue.fields.reporter.displayName)
                else:
                    bugs.append(issue.raw['fields']['customfield_11681'].get('displayName'))

                # 2024年3月18日09:09:12  增加 customfield_11087 = 缺陷来源字段
                if issue.raw['fields']['customfield_11087']:  # 缺陷来源字段
                    bugs.append(issue.raw['fields']['customfield_11087']['value'])
                    # 2024年4月8日15:09:53  增加缺陷来源子集
                    if issue.raw['fields']['customfield_11087'].get('child'):
                        bugs.append(issue.raw['fields']['customfield_11087']['child']['value'])
                    else:
                        bugs.append("空")  # 缺陷来源字段子集
                else:
                    bugs.append("空")  # 缺陷来源字段
                    bugs.append("空")  # 缺陷来源字段子集

                # 2024年4月10日21:04:56  增加 customfield_12381= 日构建缺陷字段
                if issue.raw['fields']['customfield_12381']:  # 日构建缺陷字段
                    bugs.append(issue.raw['fields']['customfield_12381']['value'])
                else:
                    bugs.append("否")  # 日构建缺陷字段

                # 2024年5月14日20:05:14 增加 resolution 解决结果字段，状态是未解决(Unresolved)的resolution字段是None
                if issue.raw['fields']['resolution']:
                    bugs.append(issue.raw['fields']['resolution']['name'])
                else:
                    bugs.append("未解决")
                all_bugs.append(bugs)
            except Exception as e:
                exceptions.append((issue.key, str(e)))


    if exceptions:
        for issue_key, error in exceptions:
            print(f"执行SQL报错: {e}")
            write_log.info(f"执行SQL报错: {e}")
            print(f"出错的缺陷是：{issue.key}")
            write_log.info(f"出错的缺陷是：{issue.key}")

    # print(time.asctime(time.localtime(time.time())) + '获取BUG结束')
    print('BUG记录数：' + str(len(all_bugs)))
    write_log.info('BUG记录数：' + str(len(all_bugs)))

    return all_bugs

def searchBugsByUpdate(JQL, update_date, jira):

    bugjql =f"{JQL} AND updated >= {update_date} ORDER BY priority DESC, updated DESC"

    print(bugjql)
    write_log.info(f"执行SQL: {bugjql}")
    # issue_object.raw 可以返回所查issue 的所有filed
    # 一个bugs里面几列分别是id,严重级别,测试人员,状态,开发人员,提交日期,版本
    all_bugs = []

    # print(time.asctime(time.localtime(time.time())) + '获取BUG开始')
    # iss = jira.search_issues(bugjql, maxResults=False)
    # write_log.info(f"查询JIRA结束")
    exceptions = []
    #每次查1000条记录，最多查100次，实际一年缺陷不会超过10W，每年差不多6-8W缺陷
    stepCount = 1000
    for i in range(0,100):
        write_log.info(f"开始查询jira{i*stepCount+1}到{(i+1)*stepCount}的数据")
        iss = jira.search_issues(bugjql, startAt=i*stepCount,maxResults=stepCount)
        write_log.info(f"开始查询jira{i*stepCount+1}到{(i+1)*stepCount}的数据完成")
        if(len(iss)<1):
            break
        for issue in iss:
            try:
                bugs = []
                # bugs.append(str(issue.key)[-5:])  # 只保留缺陷编号，前面的IPS-去掉
                # print(issue.key)
                bugs.append(issue.key)  # 缺陷编号
                bugs.append(issue.fields.priority.name)  # 优先级
                bugs.append(issue.fields.reporter.displayName)  # 报告人
                bugs.append(issue.fields.status.name)  # 缺陷状态

                # 经办人空的情况，把经办人置为未分配
                if issue.fields.assignee is None:
                    bugs.append("未分配")
                else:
                    bugs.append(issue.fields.assignee.displayName)

                bugs.append(issue.fields.created[:10])  # 创建日期
                if issue.raw['fields']['customfield_11029']:  # 版本
                    bugs.append(issue.raw['fields']['customfield_11029'][0]['name'])
                else:
                    bugs.append("空")
                bugs.append(issue.raw['fields']['customfield_11021']['value'])  # 发现阶段
                bugs.append(issue.raw['fields']['customfield_11023']['value'])  # 发现方式
                # bugs.append(issue.raw['fields']['customfield_11783']['value'])  # 前台OR后台
                if issue.raw['fields']['customfield_11783']:  # 前台OR后台
                    bugs.append(issue.raw['fields']['customfield_11783']['value'])
                else:
                    bugs.append("后台修改")

                bugs.append(issue.raw['fields']['updated'][:10])  # 更新日期

                # bugs.append(issue.raw['fields']['customfield_11681'].get('displayName'))  # 测试验证人
                if issue.raw['fields']['customfield_11681'] is None:  # 测试验证人为空的情况，把验证人置为报告人
                    bugs.append(issue.fields.reporter.displayName)
                else:
                    bugs.append(issue.raw['fields']['customfield_11681'].get('displayName'))

                # 2024年3月18日09:09:12  增加 customfield_11087 = 缺陷来源字段
                if issue.raw['fields']['customfield_11087']:  # 缺陷来源字段
                    bugs.append(issue.raw['fields']['customfield_11087']['value'])
                    # 2024年4月8日15:09:53  增加缺陷来源子集
                    if issue.raw['fields']['customfield_11087'].get('child'):
                        bugs.append(issue.raw['fields']['customfield_11087']['child']['value'])
                    else:
                        bugs.append("空")  # 缺陷来源字段子集
                else:
                    bugs.append("空")  # 缺陷来源字段
                    bugs.append("空")  # 缺陷来源字段子集

                # 2024年4月10日21:04:56  增加 customfield_12381= 日构建缺陷字段
                if issue.raw['fields']['customfield_12381']:  # 日构建缺陷字段
                    bugs.append(issue.raw['fields']['customfield_12381']['value'])
                else:
                    bugs.append("否")  # 日构建缺陷字段

                # 2024年5月14日20:05:14 增加 resolution 解决结果字段，状态是未解决(Unresolved)的resolution字段是None
                if issue.raw['fields']['resolution']:
                    bugs.append(issue.raw['fields']['resolution']['name'])
                else:
                    bugs.append("未解决")
                all_bugs.append(bugs)
            except Exception as e:
                exceptions.append((issue.key, str(e)))


    if exceptions:
        for issue_key, error in exceptions:
            print(f"执行SQL报错: {e}")
            write_log.info(f"执行SQL报错: {e}")
            print(f"出错的缺陷是：{issue.key}")
            write_log.info(f"出错的缺陷是：{issue.key}")

    # print(time.asctime(time.localtime(time.time())) + '获取BUG结束')
    print('BUG记录数：' + str(len(all_bugs)))
    write_log.info('BUG记录数：' + str(len(all_bugs)))
    return all_bugs


def getdashboard() :
    jira = JIRA(server="https://se.hundsun.com", options=getCookieOptionDict(userName, passwd))

    # boards = jira.boards()
    # for board in boards:
    #     print('BOARD: ', board)

    # dashboards = jira.dashboards()
    # for dash in dashboards:
    #     print(dash.name, dash.id)

    cizo_dashboard = jira.dashboard(id=19259)
    # curago_dashboard = jira.dashboard('CURAGO')
    print(cizo_dashboard.view)
    print(getCookieOptionDict2(userName, passwd))
    cizo_dash_page = requests.get(cizo_dashboard.view, cookies=getCookieOptionDict2(userName, passwd))

    print(cizo_dash_page.text)


# 夏总之前写的分析缺陷还有多少没完成的
def analysis_bug(buglist):
    # print(buglist)
    # 统计总个数 list.count(obj) 返回元素在列表中出现的次数。
    bug_count = len(buglist)
    # 统计各个严重级别缺陷个数
    bug_zm = 0
    bug_yz = 0
    bug_yb = 0
    bug_xqx = 0
    bug_yh = 0
    for i in range(0, len(buglist)):
        if buglist[i][1] == '致命':
            bug_zm += 1
        elif buglist[i][1] == '严重':
            bug_yz += 1
        elif buglist[i][1] == '一般':
            bug_yb += 1
        elif buglist[i][1] == '小缺陷':
            bug_xqx += 1
        elif buglist[i][1] == '优化':
            bug_yh += 1
    print(bug_yb)
    # 统计各个测试人员及缺陷个数
    dict_tester = {}
    for i in range(0, len(buglist)):
        if dict_tester.get(buglist[i][2]):
            dict_tester[buglist[i][2]] += 1
        else:
            dict_tester[buglist[i][2]] = 1

    # 统计各个状态的缺陷个数
    dict_status = {}
    for i in range(0, len(buglist)):
        if dict_status.get(buglist[i][3]):
            dict_status[buglist[i][3]] += 1
        else:
            dict_status[buglist[i][3]] = 1
    print(dict_status)

    # 统计各个开发人员及缺陷个数
    dict_dev = {}
    for i in range(0, len(buglist)):
        if dict_dev.get(buglist[i][4]):
            dict_dev[buglist[i][4]] += 1
        else:
            dict_dev[buglist[i][4]] = 1
    print(dict_dev)

    # 统计各个时间段缺陷数， 当前时间和提交时间的差值
    dict_createtime = {}
    for i in range(0, len(buglist)):
        d1 = datetime.datetime.strptime(buglist[i][5], '%Y-%m-%d')
        d2 = datetime.datetime.today()
        delay = (d2 - d1).days
        if dict_createtime.get(str(delay) + '天'):
            dict_createtime[str(delay) + '天'] += 1
        else:
            dict_createtime[str(delay) + '天'] = 1
    print(dict_createtime)

    # 统计各个版本缺陷个数
    dict_verion = {}
    for i in range(0, len(buglist)):
        if dict_verion.get(buglist[i][6]):
            dict_verion[buglist[i][6]] += 1
        else:
            dict_verion[buglist[i][6]] = 1
    print(dict_verion)


# 分析自动化缺陷数量比例的数据，保存到CSV里就可以了
def analysis_bug2(buglist):
    df = pd.DataFrame(buglist, columns=['缺陷编号', '优先级', '报告人', '缺陷状态', '经办人', '创建日期', '版本','发现阶段','发现方式', '前台OR后台', '报告日期', '报告月份'], dtype=object)
    df.to_csv("BUGdetail.csv", index=False, sep=',')


# 从导出的明细数据表里，提取一个数据透视表，没有EXCEL里好用
def analysis_bug3():
    datadetail = pd.read_csv("BUGdetail.csv")
    a = pd.pivot_table(datadetail, index='前台后台', columns='发现方式', values='缺陷编号', aggfunc='count', fill_value=0,
                       margins=True, margins_name='汇总')
    a.to_csv("BUGpivot.csv", index=True, sep=',')
    print(a)


# 分析自动化缺陷数量比例的数据，保存到CSV里就可以了
def analysis_bug_count1(buglist):
    df = pd.DataFrame(buglist, columns=['缺陷编号', '优先级', '报告人', '缺陷状态', '经办人', '创建日期', '版本','发现阶段','发现方式', '前台OR后台', '报告日期', '报告月份'], dtype=object)

# 数据透视表
    a = pd.pivot_table(df, index=['前台OR后台','报告月份'], columns='发现方式', values='缺陷编号', aggfunc='count',
                       fill_value=0,
                       margins=True, margins_name='汇总')
    a.to_csv("BUGcountpivot.csv", index=True, sep=',')
    # print(a)
    # 从导出的明细数据表里，提取一个数据透视表，没有EXCEL里好用

def analysis_bug_count2(buglist):
    df = pd.DataFrame(buglist, columns=['缺陷编号', '优先级', '报告人', '缺陷状态', '经办人', '创建日期', '版本','发现阶段','发现方式', '前台OR后台', '报告日期', '报告月份'], dtype=object)
# 数据透视表
    a = pd.pivot_table(df, index='版本', columns='发现阶段', values='缺陷编号', aggfunc='count',
                       fill_value=0,
                       margins=True, margins_name='汇总')
    a.to_csv("BUGversionpivot.csv", index=True, sep=',')
    # print(a)
    # 从导出的明细数据表里，提取一个数据透视表，没有EXCEL里好用

#分析自动化比例
def analysis_bug_auto():
    JQL = 'project in (AMIV, TZXTOIVV)  AND issuetype = Bug AND resolution in (Unresolved, "未解决(尚未处理)", "未解决(延后解决)", "未解决(无法解决)", "未解决(不需解决)", "解决(修复成功)") AND reporter not in (jirasync) '
    # UCIII AM4, TZXTOIVV GAMS3.0 UFXI
    # lt = search_bugs(JQL)
    # getdashboard()
    # analysis_bug(lt)
    # analysis_bug2(lt)
    # analysis_bug3()

    # analysis_bug_count1(lt)

    start_date = '2024-01-01'
    end_date = '2024-02-29'

    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
    end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')

    while start_date_obj <= end_date_obj:
        next_month_start = start_date_obj + timedelta(days=32)
        next_month_start = next_month_start.replace(day=1)

        bugjql =f"{JQL} AND 报告日期 >= {start_date_obj.strftime('%Y-%m-%d')} AND 报告日期 < {next_month_start.strftime('%Y-%m-%d')} ORDER BY priority DESC, updated DESC"
        print(start_date_obj.strftime('%Y-%m-%d'))
        start_date_obj = next_month_start

        lt = search_bugs(bugjql)

        analysis_bug_count1(lt)
    pass

def write_db(tablename,table_columns,values):
    write_log.info(f"开始批量插入数据库数据,开始时间:{time.time()}")
    # ora.InsertBatch(tablename,table_columns,values)
    # ora.InsertSingle(tablename,table_columns,values)
    ora.InsertBatchNew(tablename,table_columns,values)
    ora.commit()
    write_log.info(f"批量插入数据库数据完成,结束时间:{time.time()}")

def writeDbByIndex(tablename,table_columns,values):
    write_log.info(f"开始批量插入数据库数据,开始时间:{time.time()}")
    ora.InsertOrUpdateBatch(tablename,table_columns,values)
    ora.commit()
    write_log.info(f"批量更新插入数据库数据完成,结束时间:{time.time()}")

def clear_db(sql):
    write_log.info("开始批量清理数据库数据")
    ora.delete(sql)
    ora.commit()
    write_log.info("批量清理数据库数据完成")

def clearDbByIndex(index_name,index_value):
    write_log.info("开始批量根据主键清理数据库数据")
    ora.deleteByIndexBatch("bug_list",index_name,index_value)
    ora.commit()
    write_log.info("批量根据主键清理数据库数据完成")


def process_bugs_for_month(year, month, jira):
    # 计算当前月份的第一天和最后一天
    first_day = datetime(year, month, 1)
    if month == 12:
        #jira 有bug， create<=  处理成了<，故把当月最后一天改为下月第一天
        # last_day = datetime(year + 1, 1, 1) - timedelta(days=1)
        last_day = datetime(year + 1, 1, 1)
    else:
        # last_day = datetime(year, month + 1, 1) - timedelta(days=1)
        last_day = datetime(year, month + 1, 1)

    # 查询当前月份的缺陷数据
    print(f"开始查询{year}年{month}月的jira数据")
    write_log.info(f"开始查询{year}年{month}月的jira数据")
    bugs_month = search_bugs(JQL, first_day.strftime('%Y-%m-%d'), last_day.strftime('%Y-%m-%d'),jira)

    #清除当前月份的数据库记录
    clear_month_sql = f"delete from bug_list where create_date >= '{first_day.strftime('%Y-%m-%d')}' and create_date <= '{last_day.strftime('%Y-%m-%d')}'"
    clear_db(clear_month_sql)

    # 将当前月份的缺陷数据写入数据库
    write_db("bug_list", table_columns, bugs_month)
    write_log.info(f"处理{year}年{month}月的jira数据完成,记录条数{len(bugs_month)}")


def process_bugs_by_key(jira):
    yesterday = datetime.today() - timedelta(days=1)
    print(f"开始查询{yesterday}的jira数据")
    write_log.info(f"开始查询{yesterday}的jira数据")
    updateBugs = searchBugsByUpdate(JQL,yesterday.strftime('%Y-%m-%d'),jira)
    #更新数据库
    writeDbByIndex("BUG_LIST", table_columns, updateBugs)
    write_log.info(f"落库更新日期大于{yesterday}的数据完成")

if __name__ == "__main__":
    write_log = myLogger.get_my_logger("server_log")
    # 查询缺陷
    JQL = 'project in (AMIV,TZXTOIVV,UFXI,UCIII,UTII,FUS,TZXTOSR,TW,QQUST,ETFII,AIMS,FAISRDL,CWTZOIS,KT,CLJRII,FBIII)  AND issuetype = Bug AND resolution in (Unresolved, "未解决(尚未处理)", "未解决(延后解决)", "未解决(无法解决)", "未解决(不需解决)", "解决(修复成功)") AND reporter not in (jirasync) '

    # end_date = datetime.now().strftime('%Y-%m-%d')
    # start_date = (datetime.now()-timedelta(days=1)).strftime('%Y-%m-%d')

    table_columns_descripe = ['缺陷编号', '优先级', '报告人', '缺陷状态', '经办人', '创建日期', '版本', '发现阶段', '发现方式', '前台OR后台', '更新日期','测试验证人','缺陷来源','缺陷来源子集','日构建缺陷','解决结果']
    table_columns = ['BUG_KEY', 'PRIORITY', 'REPOTER', 'BUG_STATUS', 'ASSIGNEE', 'CREATE_DATE', 'VERSION', 'DISCOVERY_PHASE', 'DISCOVERY_TYPE', 'CLIENT_SERVER', 'UPDATE_DATE','VERIFIER','BUG_ORIGIN','BUG_ORIGIN_CHILD','DAILY_STRUCTURE','RESOLUTION']

    #登录jira
    jira = JIRA(server="https://se.hundsun.com", options=getCookieOptionDict(userName, passwd))

    #每周日统计23年的缺陷数据
    # if(datetime.now().weekday() == 6):
    #     print("开始查询23年jira数据")
    #     write_log.info("开始查询23年jira数据")
    #     # 查询23年所有缺陷,缺陷统计的字段columns = ['缺陷编号', '优先级', '报告人', '缺陷状态', '经办人', '创建日期', '版本', '发现阶段', '发现方式', '前台OR后台', '更新日期','测试验证人']
    #     bugs_pre_year = search_bugs(JQL, "2023-01-01", "2023-12-31",jira)
    #     clear_preyear_sql = f"delete from bug_list where create_date >= '2023-01-01' and create_date <= '2023-12-31' "
    #     clear_db(clear_preyear_sql)
    #     write_db("bug_list", table_columns, bugs_pre_year)
    #     write_log.info("处理23年jira数据完成")


    # 每天统计24年的缺陷数据
    # 查询24年所有缺陷,缺陷统计的字段columns = ['缺陷编号', '优先级', '报告人', '缺陷状态', '经办人', '创建日期', '版本', '发现阶段', '发现方式', '前台OR后台', '更新日期','测试验证人']
    # print("开始查询24年jira数据")
    # write_log.info("开始查询24年jira数据")
    # bugs_cur_year = search_bugs(JQL,"2024-01-01",datetime.now().strftime('%Y-%m-%d'))
    # clear_curyear_sql = f"delete from bug_list where create_date >= '2024-01-01'"
    # clear_db(clear_curyear_sql)
    # write_db("bug_list",table_columns,bugs_cur_year)
    # analysis_bug_count1(lt)
    # analysis_bug_count2(lt)

    # 程序优化，按月处理24年的缺陷数据，避免因为量大而占用过多内存
    # current_year = datetime.now().year
    # current_month = datetime.now().month
    # # 如果是2025年，则先查2024年的数据，循环12次后，再接着查25年的数据
    # if(current_year > 2024):
    #     for month in range(1, 13):
    #         process_bugs_for_month(2024, month, jira)
    #         process_bugs_for_month(2023, month, jira)
    # for month in range(1, current_month + 1):
    #     process_bugs_for_month(current_year, month, jira)

    # 只查询更新日期大于等于昨天的数据，并落库
    process_bugs_by_key(jira)

    ora.close()
    write_log.info("处理jira数据完成")

    pass
