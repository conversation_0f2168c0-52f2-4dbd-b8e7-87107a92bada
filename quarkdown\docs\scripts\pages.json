[{"name": "ACM", "description": "com.quarkdown.stdlib.BibliographyStyle.ACM", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/-a-c-m/index.html", "searchKeys": ["ACM", "ACM", "com.quarkdown.stdlib.BibliographyStyle.ACM"]}, {"name": "ASCENDING", "description": "com.quarkdown.stdlib.TableSortOrder.ASCENDING", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-table-sort-order/-a-s-c-e-n-d-i-n-g/index.html", "searchKeys": ["ASCENDING", "ASCENDING", "com.quarkdown.stdlib.TableSortOrder.ASCENDING"]}, {"name": "DESCENDING", "description": "com.quarkdown.stdlib.TableSortOrder.DESCENDING", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-table-sort-order/-d-e-s-c-e-n-d-i-n-g/index.html", "searchKeys": ["DESCENDING", "DESCENDING", "com.quarkdown.stdlib.TableSortOrder.DESCENDING"]}, {"name": "IEEETR", "description": "com.quarkdown.stdlib.BibliographyStyle.IEEETR", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/-i-e-e-e-t-r/index.html", "searchKeys": ["IEEETR", "IEEETR", "com.quarkdown.stdlib.BibliographyStyle.IEEETR"]}, {"name": "PLAIN", "description": "com.quarkdown.stdlib.BibliographyStyle.PLAIN", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/-p-l-a-i-n/index.html", "searchKeys": ["PLAIN", "PLAIN", "com.quarkdown.stdlib.BibliographyStyle.PLAIN"]}, {"name": "abs", "description": "com.quarkdown.stdlib.abs", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/abs.html", "searchKeys": ["abs", "abs", "com.quarkdown.stdlib.abs"]}, {"name": "align", "description": "com.quarkdown.stdlib.align", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/align.html", "searchKeys": ["align", "align", "com.quarkdown.stdlib.align"]}, {"name": "apply", "description": "com.quarkdown.stdlib.TableSortOrder.apply", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-table-sort-order/apply.html", "searchKeys": ["apply", "apply", "com.quarkdown.stdlib.TableSortOrder.apply"]}, {"name": "autopagebreak", "description": "com.quarkdown.stdlib.autoPageBreak", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/autopagebreak.html", "searchKeys": ["autoPageBreak", "autopagebreak", "com.quarkdown.stdlib.autoPageBreak"]}, {"name": "average", "description": "com.quarkdown.stdlib.collectionAverage", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/average.html", "searchKeys": ["collectionAverage", "average", "com.quarkdown.stdlib.collectionAverage"]}, {"name": "bibliography", "description": "com.quarkdown.stdlib.bibliography", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Bibliography/bibliography.html", "searchKeys": ["bibliography", "bibliography", "com.quarkdown.stdlib.bibliography"]}, {"name": "box", "description": "com.quarkdown.stdlib.box", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/box.html", "searchKeys": ["box", "box", "com.quarkdown.stdlib.box"]}, {"name": "capitalize", "description": "com.quarkdown.stdlib.capitalize", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.String/capitalize.html", "searchKeys": ["capitalize", "capitalize", "com.quarkdown.stdlib.capitalize"]}, {"name": "captionposition", "description": "com.quarkdown.stdlib.captionPosition", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/captionposition.html", "searchKeys": ["captionPosition", "captionposition", "com.quarkdown.stdlib.captionPosition"]}, {"name": "center", "description": "com.quarkdown.stdlib.center", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/center.html", "searchKeys": ["center", "center", "com.quarkdown.stdlib.center"]}, {"name": "cite", "description": "com.quarkdown.stdlib.cite", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Bibliography/cite.html", "searchKeys": ["cite", "cite", "com.quarkdown.stdlib.cite"]}, {"name": "class QdLibraryExporter(name: String, reader: Reader) : LibraryExporter", "description": "com.quarkdown.stdlib.external.QdLibraryExporter", "location": "quarkdown-stdlib/com.quarkdown.stdlib.external/-qd-library-exporter/index.html", "searchKeys": ["QdLibraryExporter", "class QdLibraryExporter(name: String, reader: Reader) : LibraryExporter", "com.quarkdown.stdlib.external.QdLibraryExporter"]}, {"name": "clip", "description": "com.quarkdown.stdlib.clip", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/clip.html", "searchKeys": ["clip", "clip", "com.quarkdown.stdlib.clip"]}, {"name": "code", "description": "com.quarkdown.stdlib.code", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Text/code.html", "searchKeys": ["code", "code", "com.quarkdown.stdlib.code"]}, {"name": "collapse", "description": "com.quarkdown.stdlib.collapse", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/collapse.html", "searchKeys": ["collapse", "collapse", "com.quarkdown.stdlib.collapse"]}, {"name": "column", "description": "com.quarkdown.stdlib.column", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/column.html", "searchKeys": ["column", "column", "com.quarkdown.stdlib.column"]}, {"name": "concatenate", "description": "com.quarkdown.stdlib.concatenate", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.String/concatenate.html", "searchKeys": ["concatenate", "concatenate", "com.quarkdown.stdlib.concatenate"]}, {"name": "constructor(name: String, reader: Reader)", "description": "com.quarkdown.stdlib.external.QdLibraryExporter.QdLibraryExporter", "location": "quarkdown-stdlib/com.quarkdown.stdlib.external/-qd-library-exporter/-qd-library-exporter.html", "searchKeys": ["QdLibraryExporter", "constructor(name: String, reader: Reader)", "com.quarkdown.stdlib.external.QdLibraryExporter.QdLibraryExporter"]}, {"name": "container", "description": "com.quarkdown.stdlib.container", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/container.html", "searchKeys": ["container", "container", "com.quarkdown.stdlib.container"]}, {"name": "cos", "description": "com.quarkdown.stdlib.cos", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/cos.html", "searchKeys": ["cos", "cos", "com.quarkdown.stdlib.cos"]}, {"name": "csv", "description": "com.quarkdown.stdlib.csv", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Data/csv.html", "searchKeys": ["csv", "csv", "com.quarkdown.stdlib.csv"]}, {"name": "currentpage", "description": "com.quarkdown.stdlib.currentPage", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/currentpage.html", "searchKeys": ["currentPage", "currentpage", "com.quarkdown.stdlib.currentPage"]}, {"name": "debug", "description": "com.quarkdown.stdlib.debug", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Logger/debug.html", "searchKeys": ["debug", "debug", "com.quarkdown.stdlib.debug"]}, {"name": "dictionary", "description": "com.quarkdown.stdlib.dictionary", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Dictionary/dictionary.html", "searchKeys": ["dictionary", "dictionary", "com.quarkdown.stdlib.dictionary"]}, {"name": "distinct", "description": "com.quarkdown.stdlib.collectionDistinct", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/distinct.html", "searchKeys": ["collectionDistinct", "distinct", "com.quarkdown.stdlib.collectionDistinct"]}, {"name": "divide", "description": "com.quarkdown.stdlib.divide", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/divide.html", "searchKeys": ["divide", "divide", "com.quarkdown.stdlib.divide"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.docAuthor", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/docauthor.html", "searchKeys": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.docAuthor"]}, {"name": "docauthors", "description": "com.quarkdown.stdlib.docAuthors", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/docauthors.html", "searchKeys": ["docAuthors", "docauthors", "com.quarkdown.stdlib.docAuthors"]}, {"name": "doclang", "description": "com.quarkdown.stdlib.docLanguage", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/doclang.html", "searchKeys": ["docLanguage", "doclang", "com.quarkdown.stdlib.docLanguage"]}, {"name": "docname", "description": "com.quarkdown.stdlib.docName", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/docname.html", "searchKeys": ["doc<PERSON>ame", "docname", "com.quarkdown.stdlib.docName"]}, {"name": "doctype", "description": "com.quarkdown.stdlib.docType", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/doctype.html", "searchKeys": ["docType", "doctype", "com.quarkdown.stdlib.docType"]}, {"name": "enum BibliographyStyle : Enum<BibliographyStyle> ", "description": "com.quarkdown.stdlib.BibliographyStyle", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/index.html", "searchKeys": ["BibliographyStyle", "enum BibliographyStyle : Enum<BibliographyStyle> ", "com.quarkdown.stdlib.BibliographyStyle"]}, {"name": "enum TableSortOrder : Enum<TableSortOrder> ", "description": "com.quarkdown.stdlib.TableSortOrder", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-table-sort-order/index.html", "searchKeys": ["TableSortOrder", "enum TableSortOrder : Enum<TableSortOrder> ", "com.quarkdown.stdlib.TableSortOrder"]}, {"name": "equals", "description": "com.quarkdown.stdlib.equals", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Logical/equals.html", "searchKeys": ["equals", "equals", "com.quarkdown.stdlib.equals"]}, {"name": "error", "description": "com.quarkdown.stdlib.error", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Logger/error.html", "searchKeys": ["error", "error", "com.quarkdown.stdlib.error"]}, {"name": "figure", "description": "com.quarkdown.stdlib.figure", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/figure.html", "searchKeys": ["figure", "figure", "com.quarkdown.stdlib.figure"]}, {"name": "first", "description": "com.quarkdown.stdlib.collectionFirst", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/first.html", "searchKeys": ["collectionFirs<PERSON>", "first", "com.quarkdown.stdlib.collectionFirst"]}, {"name": "float", "description": "com.quarkdown.stdlib.float", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/float.html", "searchKeys": ["float", "float", "com.quarkdown.stdlib.float"]}, {"name": "footer", "description": "com.quarkdown.stdlib.footer", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/footer.html", "searchKeys": ["footer", "footer", "com.quarkdown.stdlib.footer"]}, {"name": "foreach", "description": "com.quarkdown.stdlib.forEach", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/foreach.html", "searchKeys": ["for<PERSON>ach", "foreach", "com.quarkdown.stdlib.forEach"]}, {"name": "fragment", "description": "com.quarkdown.stdlib.fragment", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Slides/fragment.html", "searchKeys": ["fragment", "fragment", "com.quarkdown.stdlib.fragment"]}, {"name": "fullspan", "description": "com.quarkdown.stdlib.fullColumnSpan", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/fullspan.html", "searchKeys": ["fullColumnSpan", "fullspan", "com.quarkdown.stdlib.fullColumnSpan"]}, {"name": "function", "description": "com.quarkdown.stdlib.function", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/function.html", "searchKeys": ["function", "function", "com.quarkdown.stdlib.function"]}, {"name": "functionexists", "description": "com.quarkdown.stdlib.functionExists", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Library/functionexists.html", "searchKeys": ["functionExists", "functionexists", "com.quarkdown.stdlib.functionExists"]}, {"name": "get", "description": "com.quarkdown.stdlib.dictionaryGet", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Dictionary/get.html", "searchKeys": ["dictionaryGet", "get", "com.quarkdown.stdlib.dictionaryGet"]}, {"name": "getat", "description": "com.quarkdown.stdlib.collectionGet", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/getat.html", "searchKeys": ["collectionGet", "getat", "com.quarkdown.stdlib.collectionGet"]}, {"name": "grid", "description": "com.quarkdown.stdlib.grid", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/grid.html", "searchKeys": ["grid", "grid", "com.quarkdown.stdlib.grid"]}, {"name": "groupvalues", "description": "com.quarkdown.stdlib.collectionGroup", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/groupvalues.html", "searchKeys": ["collectionGroup", "groupvalues", "com.quarkdown.stdlib.collectionGroup"]}, {"name": "html", "description": "com.quarkdown.stdlib.html", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Injection/html.html", "searchKeys": ["html", "html", "com.quarkdown.stdlib.html"]}, {"name": "if", "description": "com.quarkdown.stdlib.if", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/if.html", "searchKeys": ["if", "if", "com.quarkdown.stdlib.if"]}, {"name": "ifnot", "description": "com.quarkdown.stdlib.ifNot", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/ifnot.html", "searchKeys": ["ifNot", "ifnot", "com.quarkdown.stdlib.ifNot"]}, {"name": "ifpresent", "description": "com.quarkdown.stdlib.ifPresent", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/ifpresent.html", "searchKeys": ["ifPresent", "ifpresent", "com.quarkdown.stdlib.ifPresent"]}, {"name": "include", "description": "com.quarkdown.stdlib.include", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Ecosystem/include.html", "searchKeys": ["include", "include", "com.quarkdown.stdlib.include"]}, {"name": "includeall", "description": "com.quarkdown.stdlib.includeAll", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Ecosystem/includeall.html", "searchKeys": ["includeAll", "includeall", "com.quarkdown.stdlib.includeAll"]}, {"name": "isempty", "description": "com.quarkdown.stdlib.isEmpty", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.String/isempty.html", "searchKeys": ["isEmpty", "isempty", "com.quarkdown.stdlib.isEmpty"]}, {"name": "iseven", "description": "com.quarkdown.stdlib.isEven", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/iseven.html", "searchKeys": ["isEven", "iseven", "com.quarkdown.stdlib.isEven"]}, {"name": "isgreater", "description": "com.quarkdown.stdlib.isGreater", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Logical/isgreater.html", "searchKeys": ["isGreater", "isgreater", "com.quarkdown.stdlib.isGreater"]}, {"name": "islower", "description": "com.quarkdown.stdlib.isLower", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Logical/islower.html", "searchKeys": ["isLower", "islower", "com.quarkdown.stdlib.isLower"]}, {"name": "isnone", "description": "com.quarkdown.stdlib.isNone", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/isnone.html", "searchKeys": ["isNone", "isnone", "com.quarkdown.stdlib.isNone"]}, {"name": "isnotempty", "description": "com.quarkdown.stdlib.isNotEmpty", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.String/isnotempty.html", "searchKeys": ["isNotEmpty", "isnotempty", "com.quarkdown.stdlib.isNotEmpty"]}, {"name": "last", "description": "com.quarkdown.stdlib.collectionLast", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/last.html", "searchKeys": ["collectionLast", "last", "com.quarkdown.stdlib.collectionLast"]}, {"name": "let", "description": "com.quarkdown.stdlib.let", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/let.html", "searchKeys": ["let", "let", "com.quarkdown.stdlib.let"]}, {"name": "libexists", "description": "com.quarkdown.stdlib.libraryExists", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Library/libexists.html", "searchKeys": ["libraryExists", "libexists", "com.quarkdown.stdlib.libraryExists"]}, {"name": "libfunctions", "description": "com.quarkdown.stdlib.libraryFunctions", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Library/libfunctions.html", "searchKeys": ["libraryFunctions", "libfunctions", "com.quarkdown.stdlib.libraryFunctions"]}, {"name": "libraries", "description": "com.quarkdown.stdlib.libraries", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Library/libraries.html", "searchKeys": ["libraries", "libraries", "com.quarkdown.stdlib.libraries"]}, {"name": "localization", "description": "com.quarkdown.stdlib.localization", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Localization/localization.html", "searchKeys": ["localization", "localization", "com.quarkdown.stdlib.localization"]}, {"name": "localize", "description": "com.quarkdown.stdlib.localize", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Localization/localize.html", "searchKeys": ["localize", "localize", "com.quarkdown.stdlib.localize"]}, {"name": "log", "description": "com.quarkdown.stdlib.log", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Logger/log.html", "searchKeys": ["log", "log", "com.quarkdown.stdlib.log"]}, {"name": "logn", "description": "com.quarkdown.stdlib.logn", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/logn.html", "searchKeys": ["logn", "logn", "com.quarkdown.stdlib.logn"]}, {"name": "loremipsum", "description": "com.quarkdown.stdlib.loremIpsum", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Text/loremipsum.html", "searchKeys": ["loremIpsum", "loremipsum", "com.quarkdown.stdlib.loremIpsum"]}, {"name": "lowercase", "description": "com.quarkdown.stdlib.lowercase", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.String/lowercase.html", "searchKeys": ["lowercase", "lowercase", "com.quarkdown.stdlib.lowercase"]}, {"name": "marker", "description": "com.quarkdown.stdlib.marker", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/marker.html", "searchKeys": ["marker", "marker", "com.quarkdown.stdlib.marker"]}, {"name": "mermaid", "description": "com.quarkdown.stdlib.mermaid", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Mermaid/mermaid.html", "searchKeys": ["mermaid", "mermaid", "com.quarkdown.stdlib.mermaid"]}, {"name": "multiply", "description": "com.quarkdown.stdlib.multiply", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/multiply.html", "searchKeys": ["multiply", "multiply", "com.quarkdown.stdlib.multiply"]}, {"name": "negate", "description": "com.quarkdown.stdlib.negate", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/negate.html", "searchKeys": ["negate", "negate", "com.quarkdown.stdlib.negate"]}, {"name": "noautopagebreak", "description": "com.quarkdown.stdlib.disableAutoPageBreak", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/noautopagebreak.html", "searchKeys": ["disableAutoPageBreak", "noautopagebreak", "com.quarkdown.stdlib.disableAutoPageBreak"]}, {"name": "node", "description": "com.quarkdown.stdlib.node", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/node.html", "searchKeys": ["node", "node", "com.quarkdown.stdlib.node"]}, {"name": "none", "description": "com.quarkdown.stdlib.none", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/none.html", "searchKeys": ["none", "none", "com.quarkdown.stdlib.none"]}, {"name": "nonumbering", "description": "com.quarkdown.stdlib.disableNumbering", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/nonumbering.html", "searchKeys": ["disableNumbering", "nonumbering", "com.quarkdown.stdlib.disableNumbering"]}, {"name": "not", "description": "com.quarkdown.stdlib.not", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Logical/not.html", "searchKeys": ["not", "not", "com.quarkdown.stdlib.not"]}, {"name": "numbered", "description": "com.quarkdown.stdlib.numbered", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/numbered.html", "searchKeys": ["numbered", "numbered", "com.quarkdown.stdlib.numbered"]}, {"name": "numbering", "description": "com.quarkdown.stdlib.numbering", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/numbering.html", "searchKeys": ["numbering", "numbering", "com.quarkdown.stdlib.numbering"]}, {"name": "object Stdlib : LibraryExporter", "description": "com.quarkdown.stdlib.Stdlib", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-stdlib/index.html", "searchKeys": ["Stdlib", "object Stdlib : LibraryExporter", "com.quarkdown.stdlib.Stdlib"]}, {"name": "open override val library: Library", "description": "com.quarkdown.stdlib.Stdlib.library", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-stdlib/library.html", "searchKeys": ["library", "open override val library: Library", "com.quarkdown.stdlib.Stdlib.library"]}, {"name": "open override val library: Library", "description": "com.quarkdown.stdlib.external.QdLibraryExporter.library", "location": "quarkdown-stdlib/com.quarkdown.stdlib.external/-qd-library-exporter/library.html", "searchKeys": ["library", "open override val library: Library", "com.quarkdown.stdlib.external.QdLibraryExporter.library"]}, {"name": "otherwise", "description": "com.quarkdown.stdlib.otherwise", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/otherwise.html", "searchKeys": ["otherwise", "otherwise", "com.quarkdown.stdlib.otherwise"]}, {"name": "pageformat", "description": "com.quarkdown.stdlib.pageFormat", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/pageformat.html", "searchKeys": ["pageFormat", "pageformat", "com.quarkdown.stdlib.pageFormat"]}, {"name": "pagemargin", "description": "com.quarkdown.stdlib.pageMarginContent", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/pagemargin.html", "searchKeys": ["page<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pagemargin", "com.quarkdown.stdlib.pageMarginContent"]}, {"name": "pair", "description": "com.quarkdown.stdlib.pair", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/pair.html", "searchKeys": ["pair", "pair", "com.quarkdown.stdlib.pair"]}, {"name": "paragraphstyle", "description": "com.quarkdown.stdlib.paragraphStyle", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/paragraphstyle.html", "searchKeys": ["paragraphStyle", "paragraphstyle", "com.quarkdown.stdlib.paragraphStyle"]}, {"name": "pi", "description": "com.quarkdown.stdlib.pi", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/pi.html", "searchKeys": ["pi", "pi", "com.quarkdown.stdlib.pi"]}, {"name": "pow", "description": "com.quarkdown.stdlib.pow", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/pow.html", "searchKeys": ["pow", "pow", "com.quarkdown.stdlib.pow"]}, {"name": "range", "description": "com.quarkdown.stdlib.range", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/range.html", "searchKeys": ["range", "range", "com.quarkdown.stdlib.range"]}, {"name": "read", "description": "com.quarkdown.stdlib.read", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Data/read.html", "searchKeys": ["read", "read", "com.quarkdown.stdlib.read"]}, {"name": "rem", "description": "com.quarkdown.stdlib.rem", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/rem.html", "searchKeys": ["rem", "rem", "com.quarkdown.stdlib.rem"]}, {"name": "repeat", "description": "com.quarkdown.stdlib.repeat", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/repeat.html", "searchKeys": ["repeat", "repeat", "com.quarkdown.stdlib.repeat"]}, {"name": "reversed", "description": "com.quarkdown.stdlib.collectionReverse", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/reversed.html", "searchKeys": ["collectionReverse", "reversed", "com.quarkdown.stdlib.collectionReverse"]}, {"name": "round", "description": "com.quarkdown.stdlib.round", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/round.html", "searchKeys": ["round", "round", "com.quarkdown.stdlib.round"]}, {"name": "row", "description": "com.quarkdown.stdlib.row", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/row.html", "searchKeys": ["row", "row", "com.quarkdown.stdlib.row"]}, {"name": "second", "description": "com.quarkdown.stdlib.collectionSecond", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/second.html", "searchKeys": ["collectionSecond", "second", "com.quarkdown.stdlib.collectionSecond"]}, {"name": "sin", "description": "com.quarkdown.stdlib.sin", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/sin.html", "searchKeys": ["sin", "sin", "com.quarkdown.stdlib.sin"]}, {"name": "size", "description": "com.quarkdown.stdlib.collectionSize", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/size.html", "searchKeys": ["collectionSize", "size", "com.quarkdown.stdlib.collectionSize"]}, {"name": "slides", "description": "com.quarkdown.stdlib.setSlidesConfiguration", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Slides/slides.html", "searchKeys": ["setSlidesConfiguration", "slides", "com.quarkdown.stdlib.setSlidesConfiguration"]}, {"name": "sorted", "description": "com.quarkdown.stdlib.collectionSorted", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/sorted.html", "searchKeys": ["collectionSorted", "sorted", "com.quarkdown.stdlib.collectionSorted"]}, {"name": "sqrt", "description": "com.quarkdown.stdlib.sqrt", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/sqrt.html", "searchKeys": ["sqrt", "sqrt", "com.quarkdown.stdlib.sqrt"]}, {"name": "string", "description": "com.quarkdown.stdlib.string", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.String/string.html", "searchKeys": ["string", "string", "com.quarkdown.stdlib.string"]}, {"name": "subtract", "description": "com.quarkdown.stdlib.subtract", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/subtract.html", "searchKeys": ["subtract", "subtract", "com.quarkdown.stdlib.subtract"]}, {"name": "sum", "description": "com.quarkdown.stdlib.sum", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/sum.html", "searchKeys": ["sum", "sum", "com.quarkdown.stdlib.sum"]}, {"name": "sumall", "description": "com.quarkdown.stdlib.collectionSumAll", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/sumall.html", "searchKeys": ["collectionSumAll", "sumall", "com.quarkdown.stdlib.collectionSumAll"]}, {"name": "table", "description": "com.quarkdown.stdlib.table", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/table.html", "searchKeys": ["table", "table", "com.quarkdown.stdlib.table"]}, {"name": "tablecolumn", "description": "com.quarkdown.stdlib.tableColumn", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/tablecolumn.html", "searchKeys": ["tableColumn", "tablecolumn", "com.quarkdown.stdlib.tableColumn"]}, {"name": "tablecolumns", "description": "com.quarkdown.stdlib.tableColumns", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/tablecolumns.html", "searchKeys": ["tableColumns", "tablecolumns", "com.quarkdown.stdlib.tableColumns"]}, {"name": "tablecompute", "description": "com.quarkdown.stdlib.tableCompute", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/tablecompute.html", "searchKeys": ["tableCompute", "tablecompute", "com.quarkdown.stdlib.tableCompute"]}, {"name": "tablefilter", "description": "com.quarkdown.stdlib.tableFilter", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/tablefilter.html", "searchKeys": ["tableFilter", "tablefilter", "com.quarkdown.stdlib.tableFilter"]}, {"name": "tableofcontents", "description": "com.quarkdown.stdlib.tableOfContents", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/tableofcontents.html", "searchKeys": ["tableOfContents", "tableofcontents", "com.quarkdown.stdlib.tableOfContents"]}, {"name": "tablesort", "description": "com.quarkdown.stdlib.tableSort", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/tablesort.html", "searchKeys": ["tableSort", "tablesort", "com.quarkdown.stdlib.tableSort"]}, {"name": "take<PERSON>", "description": "com.quarkdown.stdlib.takeIf", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/takeif.html", "searchKeys": ["takeIf", "take<PERSON>", "com.quarkdown.stdlib.takeIf"]}, {"name": "tan", "description": "com.quarkdown.stdlib.tan", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/tan.html", "searchKeys": ["tan", "tan", "com.quarkdown.stdlib.tan"]}, {"name": "texmacro", "description": "com.quarkdown.stdlib.texMacro", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/texmacro.html", "searchKeys": ["texMacro", "texmacro", "com.quarkdown.stdlib.texMacro"]}, {"name": "text", "description": "com.quarkdown.stdlib.text", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Text/text.html", "searchKeys": ["text", "text", "com.quarkdown.stdlib.text"]}, {"name": "textcollapse", "description": "com.quarkdown.stdlib.inlineCollapse", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/textcollapse.html", "searchKeys": ["inlineCollapse", "textcollapse", "com.quarkdown.stdlib.inlineCollapse"]}, {"name": "theme", "description": "com.quarkdown.stdlib.theme", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/theme.html", "searchKeys": ["theme", "theme", "com.quarkdown.stdlib.theme"]}, {"name": "third", "description": "com.quarkdown.stdlib.collectionThird", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/third.html", "searchKeys": ["collectionThird", "third", "com.quarkdown.stdlib.collectionThird"]}, {"name": "todo", "description": "com.quarkdown.stdlib.toDo", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/todo.html", "searchKeys": ["toDo", "todo", "com.quarkdown.stdlib.toDo"]}, {"name": "totalpages", "description": "com.quarkdown.stdlib.totalPages", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Document/totalpages.html", "searchKeys": ["totalPages", "totalpages", "com.quarkdown.stdlib.totalPages"]}, {"name": "truncate", "description": "com.quarkdown.stdlib.truncate", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Math/truncate.html", "searchKeys": ["truncate", "truncate", "com.quarkdown.stdlib.truncate"]}, {"name": "uppercase", "description": "com.quarkdown.stdlib.uppercase", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.String/uppercase.html", "searchKeys": ["uppercase", "uppercase", "com.quarkdown.stdlib.uppercase"]}, {"name": "val Bibliography: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Bibliography", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-bibliography.html", "searchKeys": ["Bibliography", "val Bibliography: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Bibliography"]}, {"name": "val Collection: Mo<PERSON>le", "description": "com.quarkdown.stdlib.Collection", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-collection.html", "searchKeys": ["Collection", "val Collection: Mo<PERSON>le", "com.quarkdown.stdlib.Collection"]}, {"name": "val Data: Module", "description": "com.quarkdown.stdlib.Data", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-data.html", "searchKeys": ["Data", "val Data: Module", "com.quarkdown.stdlib.Data"]}, {"name": "val Dictionary: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Dictionary", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-dictionary.html", "searchKeys": ["Dictionary", "val Dictionary: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Dictionary"]}, {"name": "val Document: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Document", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-document.html", "searchKeys": ["Document", "val Document: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Document"]}, {"name": "val Ecosystem: Module", "description": "com.quarkdown.stdlib.Ecosystem", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-ecosystem.html", "searchKeys": ["Ecosystem", "val Ecosystem: Module", "com.quarkdown.stdlib.Ecosystem"]}, {"name": "val Flow: Module", "description": "com.quarkdown.stdlib.Flow", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-flow.html", "searchKeys": ["Flow", "val Flow: Module", "com.quarkdown.stdlib.Flow"]}, {"name": "val Injection: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Injection", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-injection.html", "searchKeys": ["Injection", "val Injection: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Injection"]}, {"name": "val Layout: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Layout", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-layout.html", "searchKeys": ["Layout", "val Layout: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Layout"]}, {"name": "val Library: Module", "description": "com.quarkdown.stdlib.Library", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-library.html", "searchKeys": ["Library", "val Library: Module", "com.quarkdown.stdlib.Library"]}, {"name": "val Localization: Module", "description": "com.quarkdown.stdlib.Localization", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-localization.html", "searchKeys": ["Localization", "val Localization: Module", "com.quarkdown.stdlib.Localization"]}, {"name": "val Logger: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Logger", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-logger.html", "searchKeys": ["<PERSON><PERSON>", "val Logger: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Logger"]}, {"name": "val Logical: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Logical", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-logical.html", "searchKeys": ["Logical", "val Logical: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Logical"]}, {"name": "val Math: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Math", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-math.html", "searchKeys": ["Math", "val Math: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Math"]}, {"name": "val Mermaid: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Mermaid", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-mermaid.html", "searchKeys": ["Mermaid", "val Mermaid: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Mermaid"]}, {"name": "val NOT_FOUND: Any", "description": "com.quarkdown.stdlib.NOT_FOUND", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-n-o-t_-f-o-u-n-d.html", "searchKeys": ["NOT_FOUND", "val NOT_FOUND: Any", "com.quarkdown.stdlib.NOT_FOUND"]}, {"name": "val Optionality: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Optionality", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-optionality.html", "searchKeys": ["Optionality", "val Optionality: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Optionality"]}, {"name": "val Slides: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Slides", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-slides.html", "searchKeys": ["Slides", "val Slides: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Slides"]}, {"name": "val String: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.String", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-string.html", "searchKeys": ["String", "val String: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.String"]}, {"name": "val TableComputation: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.TableComputation", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-table-computation.html", "searchKeys": ["TableComputation", "val TableComputation: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.TableComputation"]}, {"name": "val Text: <PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.Text", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-text.html", "searchKeys": ["Text", "val Text: <PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.Text"]}, {"name": "val entries: EnumEntries<BibliographyStyle>", "description": "com.quarkdown.stdlib.BibliographyStyle.entries", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/entries.html", "searchKeys": ["entries", "val entries: EnumEntries<BibliographyStyle>", "com.quarkdown.stdlib.BibliographyStyle.entries"]}, {"name": "val entries: EnumEntries<TableSortOrder>", "description": "com.quarkdown.stdlib.TableSortOrder.entries", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-table-sort-order/entries.html", "searchKeys": ["entries", "val entries: EnumEntries<TableSortOrder>", "com.quarkdown.stdlib.TableSortOrder.entries"]}, {"name": "valueOf", "description": "com.quarkdown.stdlib.BibliographyStyle.valueOf", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/value-of.html", "searchKeys": ["valueOf", "valueOf", "com.quarkdown.stdlib.BibliographyStyle.valueOf"]}, {"name": "valueOf", "description": "com.quarkdown.stdlib.TableSortOrder.valueOf", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-table-sort-order/value-of.html", "searchKeys": ["valueOf", "valueOf", "com.quarkdown.stdlib.TableSortOrder.valueOf"]}, {"name": "values", "description": "com.quarkdown.stdlib.BibliographyStyle.values", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/values.html", "searchKeys": ["values", "values", "com.quarkdown.stdlib.BibliographyStyle.values"]}, {"name": "values", "description": "com.quarkdown.stdlib.TableSortOrder.values", "location": "quarkdown-stdlib/com.quarkdown.stdlib/-table-sort-order/values.html", "searchKeys": ["values", "values", "com.quarkdown.stdlib.TableSortOrder.values"]}, {"name": "var", "description": "com.quarkdown.stdlib.variable", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/var.html", "searchKeys": ["variable", "var", "com.quarkdown.stdlib.variable"]}, {"name": "whitespace", "description": "com.quarkdown.stdlib.whitespace", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/whitespace.html", "searchKeys": ["whitespace", "whitespace", "com.quarkdown.stdlib.whitespace"]}, {"name": "<PERSON><PERSON><PERSON>", "description": "com.quarkdown.stdlib.xyChart", "location": "quarkdown-stdlib/com.quarkdown.stdlib.module.Mermaid/xychart.html", "searchKeys": ["xyChart", "<PERSON><PERSON><PERSON>", "com.quarkdown.stdlib.xyChart"]}]