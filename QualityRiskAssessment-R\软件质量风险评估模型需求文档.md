**软件质量风险评估模型需求文档（完整版）**

---

### **1. 文档概述**
本方案建立层次化质量风险评估体系，通过模块化分解与量化指标相结合，为需求质量风险提供科学评估依据。系统采用三级评估模型（总需求→业务模块→子模块→指标维度），包含风险可视化看板与配置管理两大核心界面，支持动态权重配置、实时计算与风险预警。

---

### **2. 评估模型架构**
```
总需求质量风险度
└─ 9大业务模块（加权计算）
   └─ 子模块（5-15个/模块，加权计算）
      └─ 功能点（支持无限级扩展）
         └─ 质量指标（用例执行、分支覆盖、业务回归）
```

---

### **3. 核心业务模块定义**
| 模块编号 | 模块名称       | 权重标识 | 标准权重范围 |
|----------|----------------|----------|--------------|
| M1       | 指令系统       | W1       | 8%-15%       |
| M2       | 委托管理       | W2       | 10%-18%      |
| ...      | ...            | ...      | ...          |
| M9       | 行情资讯       | W9       | 5%-10%       |

---

### **4. 子模块评估规范**
#### **4.1 拆分原则**
- 每个业务模块拆分为5-15个功能子模块  
- 示例：交易通道模块可拆解为订单路由、通道切换、异常重试等子模块

#### **4.2 权重配置**
- 子模块权重总和=100%，支持动态版本化管理

---

### **5. 质量风险评估指标**
#### **5.1 指标定义**
| 指标名称       | 计算公式                            | 数据来源               |
|----------------|-------------------------------------|------------------------|
| 用例执行率(p1) | 已执行用例数/模块用例总数×100%     | 测试管理系统           |
| 分支覆盖率(p2) | 覆盖分支数/新增分支总数×100%       | 代码扫描工具           |
| 业务覆盖度(p3) | 回归用例数/业务线总用例数×100%     | 回归测试报告           |

#### **5.2 权重配置**
- 默认权重：用例执行率（40%）、分支覆盖（35%）、业务覆盖（25%）
- 支持按模块特性自定义调整

---

### **6. 评估计算模型**
#### **6.1 功能点风险度**
```
R_func = (p1×w1) + (p2×w2) + (p3×w3)
```

#### **6.2 子模块风险度**
```
R_sub = Σ(功能点风险度×功能点权重)
```

#### **6.3 业务模块风险度**
```
R_module = Σ(子模块风险度×子模块权重)
```

#### **6.4 总需求风险度**
```
R_total = Σ(业务模块风险度×模块权重)
```

---

### **7. 风险可视化看板界面**
#### **7.1 需求维度风险总览**
- **热力图展示**  
  - 顶部横幅显示整体需求风险度，背景色按等级划分：  
    - 绿色（0-30%）：低风险  
    - 黄色（31-60%）：中风险  
    - 红色（61-100%）：高风险  
  - 九宫格展示9大业务模块风险分布，颜色规则同整体风险

- **钻取分析**  
  - 点击任意业务模块，右侧展开子模块风险列表：  
    - 按风险度降序排列  
    - 显示子模块名称、风险值、风险等级（背景色标识）  
  - 支持导出PDF/Excel格式风险报告

---

### **8. 配置管理界面（二维矩阵编辑器）**
#### **8.1 界面结构**
- **层级管理**：树形表格支持业务模块→子模块→功能点无限级扩展
- **字段类型**：包含手动输入、自动计算、权重配置三类字段

#### **8.2 关键字段定义**
| 字段名称                     | 输入方式 | 规则说明                              |
|------------------------------|----------|---------------------------------------|
| 需求对应用例数               | 手动输入 | ≥0的整数                             |
| 需求对应用例执行数           | 手动输入 | ≥0的整数，不大于需求对应用例数         |
| 测试用例覆盖率权重(w1)       | 手动输入 | 子模块内w1+w2+w3=100%                |
| 用例执行率(p1)              | 自动计算 | 需求对应用例执行数/需求对应用例数×100% |
| 需求对应代码分支数           | 手动输入 | ≥0的整数                             |
| 需求测试覆盖代码分支数       | 手动输入 | ≥0的整数，不大于需求对应代码分支数 |
| 分支覆盖率(p2)              | 自动计算 | 需求测试覆盖代码分支数/需求对应代码分支数×100% |
| 业务模块对应用例数           | 手动输入 | ≥0的整数        |
| 业务模块回归覆盖用例数       | 手动输入 | ≥0的整数，不大于业务模块对应用例数 |
| 业务覆盖度(p3)              | 自动计算 | 业务模块回归覆盖用例数/业务模块对应用例数×100% |
| 功能点风险度                | 自动计算 | (p1×w1)+(p2×w2)+(p3×w3)             |

#### **8.3 交互要求**
- **动态操作**：支持右键新增/删除层级、批量导入Excel数据  
- **实时计算**：修改输入值或权重后自动刷新风险度  
- **版本控制**：保存历史配置并支持差异对比（变更高亮显示）  

---

### **9. 风险等级与应对策略**
| 风险度区间 | 风险等级 | 应对措施                     |
|------------|----------|------------------------------|
| 0-30%      | 低风险   | 常规测试                     |
| 31-60%     | 中风险   | 补充测试+代码审查            |
| 61-100%    | 高风险   | 专项测试+架构评审+灰度发布   |

---

### **10. 实施要求**
1. **系统开发**：  
   - 开发风险评估计算引擎  
   - 实现配置界面与风险看板的数据联动  
2. **权限管理**：区分查看者、编辑者、管理员角色  
3. **数据校验**：输入非法值时阻断保存并提示错误  

---

### **11. 交付物**
1. 可配置的权重管理矩阵  
2. 风险评估计算工具（含核心界面）  
3. 风险热力图与配置管理界面原型  
4. 操作手册（含数据采集规范与公式说明）  

---

### **12. 附录**
- **附录A**：典型业务模块拆解示例  
- **附录B**：界面原型示意图（含热力图、配置编辑器）  
- **附录C**：权重调整审批流程  
- **附录D**：数据校验规则对照表  

---

### **版本记录**
| 版本 | 修订日期   | 修订内容                     | 审批人 |
|------|------------|------------------------------|--------|
| V1.2 | 2023-08-17 | 整合核心界面与完整评估逻辑   | 王五   |

---

**文档说明**  
本方案通过模块化分解、量化指标评估、可视化呈现的三位一体设计，实现质量风险的精准定位与动态管理，需配合自动化数据采集工具确保评估实时性。