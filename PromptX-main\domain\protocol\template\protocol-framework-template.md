# DPML{协议名称}模式提示词框架

> **TL;DR:** DPML{协议名称}模式提示词框架定义了{一句话描述协议核心功能和价值}，{补充说明特点或独特价值}。

### 目的与功能

DPML{协议名称}模式提示词框架{详细说明此框架的目的}，它的主要功能是：
- {功能点1}
- {功能点2}
- {功能点3}
- {功能点4}

## 📝 语法定义

```ebnf
(* EBNF形式化定义 *)
{主标签}_element ::= '<{主标签}' attributes? '>' content '</{主标签}>'
attributes ::= (' ' attribute)+ | ''
attribute ::= name '="' value '"'
name ::= [a-zA-Z][a-zA-Z0-9_-]*
value ::= [^"]*
content ::= (markdown_content | {子标签1}_element | {子标签2}_element | ... )+

{子标签1}_element ::= '<{子标签1}' attributes? '>' markdown_content '</{子标签1}>'
{子标签2}_element ::= '<{子标签2}' attributes? '>' markdown_content '</{子标签2}>'
...

markdown_content ::= (* 任何有效的Markdown文本，可包含特定语法元素 *)
```

## 🧩 语义说明

{主标签}标签表示{对主标签的核心语义描述}。标签内容可以包含{数量}种不同{类型}的子标签，每个子标签都有明确的语义：

- **{子标签1}**: {子标签1的语义描述}
- **{子标签2}**: {子标签2的语义描述}
- **{子标签3}**: {子标签3的语义描述}
- ...

{这些子标签的组合关系、层次结构或互操作方式}

{如有必要，对标签语义的补充说明}

### {补充语义说明1，如 "优先级关系"、"组合规则" 等}

{详细描述补充语义内容}

1. **{项目1}** - {描述}
2. **{项目2}** - {描述}
3. ...

{进一步说明这些补充语义的重要性或应用场景}

### {补充语义说明2，如 "子标签的可选性" 等}

{详细描述补充语义内容}

{说明这些补充语义如何影响理解和使用} 