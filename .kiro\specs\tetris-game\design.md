# 设计文档

## 概述

俄罗斯方块游戏将使用Python和Pygame库开发，采用面向对象的设计模式。游戏将具有可爱风格的视觉设计，包括柔和的颜色、圆润的形状和友好的用户界面元素。

## 架构

### 主要组件
- **Game**: 主游戏控制器，管理游戏状态和主循环
- **Board**: 游戏板，管理游戏区域和已放置的方块
- **Tetromino**: 俄罗斯方块类，定义方块形状和行为
- **UI**: 用户界面管理器，处理界面渲染和用户交互
- **ScoreManager**: 分数管理器，处理分数计算和等级系统

### 技术栈
- **Python 3.8+**: 主要编程语言
- **Pygame**: 游戏开发框架，用于图形渲染和事件处理
- **JSON**: 配置文件格式，存储游戏设置和高分记录

## 组件和接口

### Game类
```python
class Game:
    def __init__(self)
    def run(self)
    def handle_events(self)
    def update(self)
    def render(self)
    def pause_game(self)
    def resume_game(self)
    def game_over(self)
    def restart_game(self)
```

### Board类
```python
class Board:
    def __init__(self, width, height)
    def is_valid_position(self, tetromino, x, y)
    def place_tetromino(self, tetromino, x, y)
    def clear_lines(self)
    def is_line_full(self, line)
    def move_lines_down(self, cleared_line)
    def get_board_state(self)
```

### Tetromino类
```python
class Tetromino:
    def __init__(self, shape_type)
    def rotate(self)
    def get_rotated_shape(self)
    def get_shape_blocks(self)
    def get_color(self)
```

### UI类
```python
class UI:
    def __init__(self, screen)
    def draw_board(self, board)
    def draw_tetromino(self, tetromino, x, y)
    def draw_next_piece(self, tetromino)
    def draw_score_panel(self, score, lines, level)
    def draw_game_over_screen(self, final_score)
    def draw_pause_screen(self)
    def apply_cute_style(self)
```

### ScoreManager类
```python
class ScoreManager:
    def __init__(self)
    def add_line_score(self, lines_cleared)
    def get_current_score(self)
    def get_current_level(self)
    def get_fall_speed(self)
    def save_high_score(self)
    def load_high_score(self)
```

## 数据模型

### 方块形状定义
```python
TETROMINO_SHAPES = {
    'I': [['.....',
           '..#..',
           '..#..',
           '..#..',
           '..#..']],
    'O': [['.....',
           '.....',
           '.##..',
           '.##..',
           '.....']],
    'T': [['.....',
           '.....',
           '.#...',
           '###..',
           '.....']],
    'S': [['.....',
           '.....',
           '.##..',
           '##...',
           '.....']],
    'Z': [['.....',
           '.....',
           '##...',
           '.##..',
           '.....']],
    'J': [['.....',
           '.....',
           '#....',
           '###..',
           '.....']],
    'L': [['.....',
           '.....',
           '..#..',
           '###..',
           '.....']]
}
```

### 颜色配置（可爱风格）
```python
CUTE_COLORS = {
    'I': (255, 182, 193),  # 浅粉色
    'O': (255, 218, 185),  # 桃色
    'T': (221, 160, 221),  # 梅花色
    'S': (152, 251, 152),  # 浅绿色
    'Z': (255, 160, 122),  # 浅橙色
    'J': (173, 216, 230),  # 浅蓝色
    'L': (255, 255, 224),  # 浅黄色
    'BACKGROUND': (248, 248, 255),  # 幽灵白
    'GRID': (230, 230, 250),  # 薰衣草色
    'TEXT': (75, 0, 130)   # 靛青色
}
```

### 游戏配置
```python
GAME_CONFIG = {
    'BOARD_WIDTH': 10,
    'BOARD_HEIGHT': 20,
    'BLOCK_SIZE': 30,
    'WINDOW_WIDTH': 800,
    'WINDOW_HEIGHT': 600,
    'FPS': 60,
    'INITIAL_FALL_SPEED': 500,  # 毫秒
    'SPEED_INCREASE_RATE': 0.9,
    'LINES_PER_LEVEL': 10
}
```

## 错误处理

### 游戏状态错误
- **无效移动**: 当方块移动到无效位置时，保持原位置不变
- **旋转冲突**: 当方块无法旋转时，尝试踢墙算法或保持原状态
- **内存不足**: 优雅降级，减少视觉效果复杂度

### 文件操作错误
- **配置文件缺失**: 使用默认配置并创建新的配置文件
- **高分文件损坏**: 重置高分记录并记录错误日志
- **资源文件缺失**: 使用内置的基础图形元素

### 输入处理错误
- **按键冲突**: 实现按键防抖动机制
- **快速输入**: 限制输入频率防止游戏逻辑混乱

## 测试策略

### 单元测试
- **方块旋转逻辑**: 测试所有7种方块的4个旋转状态
- **碰撞检测**: 测试边界和方块间的碰撞检测
- **行消除逻辑**: 测试单行、多行消除的正确性
- **分数计算**: 验证分数和等级计算的准确性

### 集成测试
- **游戏流程**: 测试完整的游戏循环
- **用户输入**: 测试所有控制键的响应
- **状态转换**: 测试游戏状态间的正确转换

### 性能测试
- **帧率稳定性**: 确保游戏在60FPS下稳定运行
- **内存使用**: 监控长时间游戏的内存占用
- **响应延迟**: 测试用户输入的响应时间

### 用户体验测试
- **可爱风格一致性**: 确保所有UI元素符合可爱风格设计
- **颜色对比度**: 验证颜色搭配的可读性和美观性
- **动画流畅性**: 测试方块移动和消除动画的流畅度

## 可爱风格设计细节

### 视觉元素
- **圆角设计**: 所有方块和UI元素使用圆角矩形
- **柔和阴影**: 为方块添加轻微的阴影效果增加立体感
- **渐变色彩**: 使用渐变色彩让方块更有质感
- **可爱字体**: 使用圆润的字体显示分数和文本

### 动画效果
- **方块下落**: 平滑的下落动画
- **行消除**: 闪烁和淡出效果
- **方块旋转**: 平滑的旋转过渡动画
- **得分提示**: 分数增加时的弹跳动画

### 音效设计（可选扩展）
- **方块放置**: 轻柔的"叮"声
- **行消除**: 愉快的铃声
- **游戏结束**: 温和的提示音
- **背景音乐**: 轻松愉快的循环音乐