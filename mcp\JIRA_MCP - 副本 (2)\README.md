# JIRA缺陷查询 MCP

## 项目简介
这是一个基于 FastMCP 框架开发的 JIRA 缺陷查询工具，支持通过多种条件查询 JIRA 系统中的缺陷信息，并提供实时数据更新功能。

## 功能特性
1. **缺陷查询**
   - 支持通过缺陷编号直接查询
   - 支持多条件组合查询：
     - 项目名称
     - 问题类型
     - 解决结果
     - 报告人
     - 缺陷状态
     - 创建时间范围
   - 支持批量查询多个缺陷
   - 返回缺陷总数统计

2. **实时数据更新**
   - 支持通过 SSE（Server-Sent Events）实时获取缺陷更新
   - 自动推送最新缺陷信息

3. **统计功能**
   - 提供缺陷统计信息
   - 支持按优先级、状态、经办人等多维度统计

## 安装步骤
1. 克隆代码到本地
2. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```
3. 配置 config.ini 文件：
   - 设置 JIRA 服务器信息
   - 配置认证信息
   - 设置默认查询参数

## 使用方法
1. **启动服务**
   ```bash
   python mcpserver.py
   ```

2. **查询缺陷**
   ```python
   # 通过缺陷编号查询
   await get_bugs(issuekey="TZXT0IVV-116505")
   
   # 多条件组合查询
   await get_bugs(
       project="TZXTOIVV",
       issuetype="Bug",
       resolution="Unresolved",
       reporter="!jirasync",
       status="!Closed",
       created_days=7
   )
   ```

3. **实时获取更新**
   ```python
   # 订阅缺陷更新
   await stream_bugs()
   ```

4. **获取统计信息**
   ```python
   # 获取缺陷统计
   await get_bug_stats()
   ```

## 配置说明
在 `config.ini` 文件中可以配置以下参数：

1. **JIRA配置**
   - 服务器地址
   - 用户名密码
   - CAS登录URL
   - SSL验证设置

2. **查询参数**
   - 默认项目
   - 默认问题类型
   - 默认解决结果
   - 默认报告人
   - 默认状态
   - 默认查询天数

3. **服务器配置**
   - 主机地址
   - 端口号
   - 调试模式
   - 自动重载

4. **日志配置**
   - 日志级别
   - 日志格式
   - 日志文件

## 返回数据格式
1. **缺陷查询结果**
   ```json
   {
       "data": [
           {
               "key": "TZXT0IVV-116505",
               "priority": "高",
               "reporter": "张三",
               "status": "待处理",
               "assignee": "李四",
               "created": "2024-01-01",
               "updated": "2024-01-02",
               "summary": "缺陷描述",
               "description": "详细描述"
           }
       ],
       "bug_num": 1
   }
   ```

2. **统计信息**
   ```json
   {
       "data": {
           "total": 100,
           "by_priority": {
               "高": 20,
               "中": 50,
               "低": 30
           },
           "by_status": {
               "待处理": 40,
               "处理中": 30,
               "已关闭": 30
           },
           "by_assignee": {
               "张三": 25,
               "李四": 35,
               "未分配": 40
           }
       }
   }
   ```

## 注意事项
1. 确保 JIRA 服务器可以正常访问
2. 配置文件中的敏感信息（如密码）建议使用环境变量管理
3. 查询条件中的特殊字符需要使用引号括起来
4. 建议定期检查日志文件，及时处理异常情况

## 技术支持
如有问题，请联系系统管理员yumm07649。 