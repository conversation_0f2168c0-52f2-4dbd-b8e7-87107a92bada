import tkinter as tk
from tkinter import ttk, filedialog
import json
from tkinter import messagebox
import pandas as pd
from typing import Dict, List
import warnings
import os
import sys
warnings.filterwarnings("ignore", category=UserWarning)  # 添加这行来忽略警告

# matplotlib相关配置，放在导入matplotlib之前
import os
os.environ['MPLBACKEND'] = 'Agg'  # 设置matplotlib后端

import matplotlib
matplotlib.use('Agg')  # 在导入pyplot之前设置backend
matplotlib.rcParams['figure.dpi'] = 100  # 设置DPI
matplotlib.rcParams['savefig.dpi'] = 100  # 设置保存图片的DPI
matplotlib.rcParams['figure.autolayout'] = False
matplotlib.rcParams['figure.figsize'] = [6.0, 4.0]
matplotlib.rcParams['figure.max_open_warning'] = 0
matplotlib.rcParams['image.cmap'] = 'RdYlGn_r'  # 设置默认colormap
matplotlib.rcParams['figure.facecolor'] = 'white'
matplotlib.rcParams['savefig.facecolor'] = 'white'

import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import os
from datetime import datetime
import traceback
import shutil
import time
import uuid
import sys

warnings.filterwarnings("ignore", category=UserWarning)

class QualityRiskAssessment:
    def __init__(self):
        # 获取程序运行路径
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe
            self.application_path = os.path.dirname(sys.executable)
        else:
            # 如果是python脚本
            self.application_path = os.path.dirname(os.path.abspath(__file__))
        
        # 配置文件路径使用绝对路径
        self.config_file = os.path.join(self.application_path, "risk_assessment_config.json")
        
        self.root = tk.Tk()
        self.root.title("软件质量风险评估系统")
        self.root.geometry("1200x800")
        
        # 初始化默认风险阈值
        self.risk_thresholds = {
            "low": 0.3,
            "medium": 0.6,
            "high": 0.8
        }
        
        # 初始化需求数据
        self.requirements = {}
        
        # 初始化基本UI
        self.create_notebook()  # 先创建notebook和需求列表
        
        # 升级需求数据文件格式
        self.upgrade_requirements_file()
        
        # 加载需求数据
        self.load_requirements()
        
        # 加载配置文件（包含风险阈值）
        self.load_config()
        
        # 创建菜单
        self.create_menu()
        
        # 添加当前需求的跟踪
        self.current_requirement = None
        
        # 延迟加载其他组件
        self.root.after(100, self.delayed_init)

    def delayed_init(self):
        """延迟初始化其他组件"""
        # 创建界面组件
        self.create_risk_dashboard()
        self.create_config_panel()
        
    def add_new_function(self):
        """新增功能点"""
        selected = self.tree.selection()
        if not selected or '_' in selected[0]:  # 确保选中的是子模块
            messagebox.showwarning("警告", "请先选择一个子模块")
            return
        
        module_id = selected[0]
        
        # 获取当前最大功能点编号
        max_num = 0
        if 'functions' in self.business_modules[module_id]:
            for func_id in self.business_modules[module_id]['functions'].keys():
                try:
                    num = int(func_id.split('_')[-1])
                    max_num = max(max_num, num)
                except (ValueError, IndexError):
                    continue
        
        new_id = f"{module_id}_func_{max_num + 1}"
        
        # 创建新功能点，设置默认值
        func_info = {
            'name': "新功能点",
            'weight': 0.1,  # 默认权重为0.1
            'total_cases': 0,
            'executed_cases': 0,
            'w1': 0.4,  # 修改：默认权重为0.4
            'branch_total': 0,
            'branch_covered': 0,
            'w2': 0.4,  # 修改：默认权重为0.4
            'business_cases': 0,
            'business_covered': 0,
            'w3': 0.2,  # 修改：默认权重为0.2
            'case_coverage': 0,
            'branch_coverage': 0,
            'business_coverage': 0,
            'risk': 0
        }
        
        # 计算初始风险度
        risk = self.calculate_risk(0, 0, 0, 0.4, 0.4, 0.2)  # 修改：使用新的默认权重
        func_info['risk'] = risk
        
        # 更新配置数据
        if 'functions' not in self.business_modules[module_id]:
            self.business_modules[module_id]['functions'] = {}
        
        self.business_modules[module_id]['functions'][new_id] = func_info
        
        # 更新树形结构
        values = [
            func_info['name'],
            f"{func_info['weight']:.3f}",  # 直接显示小数
            str(func_info['total_cases']),
            str(func_info['executed_cases']),
            f"{func_info['case_coverage']:.1f}",
            str(func_info['w1']),  # 直接显示小数
            str(func_info['branch_total']),
            str(func_info['branch_covered']),
            f"{func_info['branch_coverage']:.1f}",
            str(func_info['w2']),  # 直接显示小数
            str(func_info['business_cases']),
            str(func_info['business_covered']),
            f"{func_info['business_coverage']:.1f}",
            str(func_info['w3']),  # 直接显示小数
            f"{func_info['risk']*100:.1f}"
        ]
        
        self.tree.insert(module_id, "end", new_id, text=new_id, values=values)
        
        # 保存配置并更新显示
        self.save_config()
        self.update_risk_dashboard()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 加载业务模块数据
                    self.business_modules = data.get('business_modules', {})
                    # 加载风险阈值
                    self.risk_thresholds = data.get('risk_thresholds', {
                        "low": 0.3,
                        "medium": 0.6,
                        "high": 0.8
                    })
                    
                    # 打印调试信息
                    print("加载的配置数据:")
                    print(f"模块数量: {len(self.business_modules)}")
                    print(f"风险阈值: {self.risk_thresholds}")
                    
            else:
                self.business_modules = {}
                self.risk_thresholds = {
                    "low": 0.3,
                    "medium": 0.6,
                    "high": 0.8
                }
                
        except Exception as e:
            messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")
            self.business_modules = {}
            self.risk_thresholds = {
                "low": 0.3,
                "medium": 0.6,
                "high": 0.8
            }

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="风险阈值设置", command=self.show_risk_settings)
        tools_menu.add_command(label="生成风险报告", command=self.generate_risk_report)
        
    def create_notebook(self):
        """创建选项卡"""
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both')
        
        # 创建需求列表、风险看板和配置管理三个选项卡
        self.req_frame = ttk.Frame(self.notebook)
        self.risk_frame = ttk.Frame(self.notebook)
        self.config_frame = ttk.Frame(self.notebook)
        
        # 先创建需求列表界面
        self.create_requirement_list()
        
        # 再添加选项卡
        self.notebook.add(self.req_frame, text='需求列表')
        self.notebook.add(self.risk_frame, text='风险可视化看板')
        self.notebook.add(self.config_frame, text='配置管理')
        
    def create_requirement_list(self):
        """创建需求列表界面"""
        # 创建工具栏框架
        toolbar_frame = ttk.Frame(self.req_frame)
        toolbar_frame.pack(fill='x', padx=5, pady=5)
        
        # 创建搜索框
        ttk.Label(toolbar_frame, text="需求编号:").pack(side='left', padx=5)
        self.req_number_var = tk.StringVar()  # 改为 req_number_var
        ttk.Entry(toolbar_frame, textvariable=self.req_number_var).pack(side='left', padx=5)
        
        ttk.Label(toolbar_frame, text="需求标题:").pack(side='left', padx=5)
        self.req_title_var = tk.StringVar()
        ttk.Entry(toolbar_frame, textvariable=self.req_title_var).pack(side='left', padx=5)
        
        # 创建按钮
        ttk.Button(toolbar_frame, text="查询", command=self.search_requirements).pack(side='left', padx=5)
        ttk.Button(toolbar_frame, text="新增", command=self.add_requirement).pack(side='left', padx=5)
        ttk.Button(toolbar_frame, text="修改", command=self.edit_requirement).pack(side='left', padx=5)
        ttk.Button(toolbar_frame, text="删除", command=self.delete_requirement).pack(side='left', padx=5)
        
        # 创建需求列表
        columns = ("req_number", "title", "description", "version", "tester", "risk")
        self.req_tree = ttk.Treeview(self.req_frame, columns=columns, show='headings')
        
        # 设置列标题
        self.req_tree.heading("req_number", text="需求编号")
        self.req_tree.heading("title", text="需求标题")
        self.req_tree.heading("description", text="需求描述")
        self.req_tree.heading("version", text="所属版本")
        self.req_tree.heading("tester", text="测试负责人")
        self.req_tree.heading("risk", text="总体风险度")
        
        # 设置列宽
        self.req_tree.column("req_number", width=100)
        self.req_tree.column("title", width=200)
        self.req_tree.column("description", width=300)
        self.req_tree.column("version", width=100)
        self.req_tree.column("tester", width=100)
        self.req_tree.column("risk", width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.req_frame, orient="vertical", command=self.req_tree.yview)
        self.req_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.req_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # 绑定双击事件
        self.req_tree.bind("<Double-1>", self.on_requirement_double_click)

    def add_requirement(self):
        """新增需求"""
        dialog = RequirementDialog(self.root, "新增需求")
        if dialog.result:
            internal_id = dialog.result['id']
            req_number = dialog.result['req_number']
            
            # 检查需求编号是否已存在
            if any(req['req_number'] == req_number for req in self.requirements.values()):
                messagebox.showerror("错误", "需求编号已存在")
                return
            
            self.requirements[internal_id] = dialog.result
            self.refresh_requirement_list()  # 使用refresh_requirement_list确保ID一致性
            self.save_requirements()

    def edit_requirement(self):
        """修改需求"""
        selected = self.req_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个需求")
            return
        
        try:
            # 获取选中项的ID（即内部ID）
            internal_id = selected[0]
            
            # 直接从requirements字典中获取需求信息
            if internal_id not in self.requirements:
                messagebox.showerror("错误", "找不到需求信息")
                return
            
            # 创建需求数据的副本
            requirement = self.requirements[internal_id].copy()
            requirement['id'] = internal_id  # 确保ID字段存在
            
            dialog = RequirementDialog(self.root, "修改需求", requirement)
            if dialog.result:
                # 检查需求编号是否与其他需求重复
                new_req_number = dialog.result['req_number']
                if new_req_number != requirement['req_number'] and \
                   any(req['req_number'] == new_req_number 
                       for id, req in self.requirements.items() if id != internal_id):
                    messagebox.showerror("错误", "需求编号已存在")
                    return
                
                self.requirements[internal_id] = dialog.result
                self.refresh_requirement_list()  # 使用refresh_requirement_list确保ID一致性
                self.save_requirements()
        except Exception as e:
            messagebox.showerror("错误", f"修改需求失败: {str(e)}")

    def delete_requirement(self):
        """删除需求"""
        selected = self.req_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个需求")
            return
        
        try:
            # 获取选中项的ID（即内部ID）
            internal_id = selected[0]
            
            # 从requirements字典中获取需求信息
            if internal_id not in self.requirements:
                messagebox.showerror("错误", "找不到需求信息")
                return
            
            # 获取需求信息以在确认对话框中显示
            requirement = self.requirements[internal_id]
            req_number = requirement.get('req_number', '')
            req_title = requirement.get('title', '')
            
            # 显示自定义确认对话框
            confirm_message = (f"确定要删除以下需求吗？\n\n"
                             f"需求编号: {req_number}\n"
                             f"需求标题: {req_title}")
            dialog = ConfirmDialog(self.root, "确认删除", confirm_message)
            
            if dialog.result:
                # 删除需求
                del self.requirements[internal_id]
                
                # 刷新界面并保存数据
                self.refresh_requirement_list()  # 使用refresh_requirement_list确保ID一致性
                self.save_requirements()
                
                # 显示删除成功提示
                messagebox.showinfo("成功", "需求已删除")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除需求失败: {str(e)}\n{traceback.format_exc()}")

    def search_requirements(self):
        """搜索需求"""
        req_number = self.req_number_var.get().strip()
        title = self.req_title_var.get().strip()
        
        # 清空当前列表
        for item in self.req_tree.get_children():
            self.req_tree.delete(item)
        
        # 添加符合条件的需求
        for req_id, req in self.requirements.items():
            if (not req_number or req_number.lower() in req['req_number'].lower()) and \
               (not title or title.lower() in req['title'].lower()):
                self.req_tree.insert("", "end", req_id, values=(  # 修改：使用需求ID作为item的ID
                    req['req_number'],
                    req['title'],
                    req['description'],
                    req['version'],
                    req['tester'],
                    f"{req['risk']:.1%}"
                ))

    def on_requirement_double_click(self, event):
        """双击需求时的处理"""
        try:
            selected = self.req_tree.selection()
            if not selected:
                return
            
            req_id = selected[0]
            if req_id not in self.requirements:
                return
            
            # 更新当前需求
            self.current_requirement = self.requirements[req_id]
            
            print("\n[DEBUG] ====== 双击需求事件 ======")
            print(f"[DEBUG] 需求ID: {req_id}")
            print(f"[DEBUG] 需求编号: {self.current_requirement['req_number']}")
            print(f"[DEBUG] 需求标题: {self.current_requirement['title']}")
            
            # 检查并打印属于当前需求的子模块和功能点
            print("\n[DEBUG] === 查找相关模块和功能点 ===")
            has_modules = False
            has_functions = False
            
            for module_id, module_info in self.business_modules.items():
                print(f"\n检查模块 {module_id}:")
                print(f"  模块名称: {module_info.get('name')}")
                print(f"  模块需求ID: {module_info.get('requirement_id')}")
                
                if module_info.get('requirement_id') == self.current_requirement['id']:
                    has_modules = True
                    print("  [找到匹配的模块]")
                    
                    # 检查功能点
                    if 'functions' in module_info:
                        for func_id, func_info in module_info['functions'].items():
                            if func_info.get('requirement_id') == self.current_requirement['id']:
                                has_functions = True
                                break
            
            print(f"\n[DEBUG] 检查结果:")
            print(f"  有模块: {has_modules}")
            print(f"  有功能点: {has_functions}")
            
            # 更新风险看板的需求标签
            if hasattr(self, 'risk_req_label'):
                self.risk_req_label.config(
                    text=f"当前需求: {self.current_requirement['req_number']} - {self.current_requirement['title']}")
                print("[DEBUG] 风险看板需求标签已更新")
            
            # 更新配置管理面板的需求标签
            if hasattr(self, 'current_req_label'):
                self.current_req_label.config(
                    text=f"当前需求: {self.current_requirement['req_number']} - {self.current_requirement['title']}")
                print("[DEBUG] 配置管理需求标签已更新")
            
            # 刷新配置面板
            self.refresh_config_panel()
            
            # 根据是否有模块和功能点决定切换到哪个选项卡
            if has_modules and has_functions:
                self.notebook.select(1)  # 切换到风险看板
                print("[DEBUG] 已切换到风险看板")
            else:
                if not has_modules:
                    print("[DEBUG] 没有找到相关模块，请先维护子模块信息")
                    messagebox.showinfo("提示", "请先在配置管理中维护子模块信息")
                elif not has_functions:
                    print("[DEBUG] 没有找到相关功能点，请先维护功能点信息")
                    messagebox.showinfo("提示", "请先在配置管理中维护功能点信息")
                self.notebook.select(2)  # 切换到配置管理选项卡
        
        except Exception as e:
            print(f"[ERROR] 双击需求处理失败: {str(e)}")
            traceback.print_exc()

    def save_requirements(self):
        """保存需求数据"""
        try:
            with open('requirements.json', 'w', encoding='utf-8') as f:
                json.dump(self.requirements, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("错误", f"保存需求数据失败: {str(e)}")

    def load_requirements(self):
        """加载需求数据"""
        try:
            if os.path.exists('requirements.json'):
                with open('requirements.json', 'r', encoding='utf-8') as f:
                    self.requirements = json.load(f)
                self.refresh_requirement_list()
        except Exception as e:
            messagebox.showerror("错误", f"加载需求数据失败: {str(e)}")
            self.requirements = {}  # 确保初始化为空字典

    def refresh_requirement_list(self):
        """刷新需求列表"""
        # 清空当前列表
        for item in self.req_tree.get_children():
            self.req_tree.delete(item)
        
        # 重新加载所有需求
        for req_id, req in self.requirements.items():
            self.req_tree.insert("", "end", req_id, values=(  # 修改：使用需求ID作为item的ID
                req['req_number'],
                req['title'],
                req['description'],
                req['version'],
                req['tester'],
                f"{req['risk']:.1%}"
            ))

    def create_risk_dashboard(self):
        """创建风险看板"""
        try:
            # 创建上下分布的主框架
            main_frame = ttk.Frame(self.risk_frame)
            main_frame.pack(fill='both', expand=True, padx=5, pady=5)
            
            # 添加当前需求显示标签
            self.risk_req_label = ttk.Label(main_frame, text="当前需求: 无")
            self.risk_req_label.pack(fill='x', padx=5, pady=5)
            print("风险看板标签已创建")  # 调试信息
            
            # 创建上部热力图区域的框架
            heatmap_container = ttk.Frame(main_frame)
            heatmap_container.pack(fill='x', expand=False)
            
            # 创建左侧风险总结区域
            summary_frame = ttk.LabelFrame(heatmap_container, text="风险评估总结", width=200)
            summary_frame.pack(side='left', fill='y', padx=5, pady=5)
            summary_frame.pack_propagate(False)  # 防止子组件影响frame大小
            
            # 总体风险度显示
            risk_frame = ttk.Frame(summary_frame)
            risk_frame.pack(fill='x', padx=5, pady=5)
            ttk.Label(risk_frame, text="需求总体风险度：", font=('Arial', 10, 'bold')).pack()
            self.total_risk_label = ttk.Label(risk_frame, text="0%", font=('Arial', 12, 'bold'))
            self.total_risk_label.pack()
            
            # 风险总结信息
            self.risk_summary_text = tk.Text(summary_frame, wrap=tk.WORD, width=20, height=15)
            self.risk_summary_text.pack(fill='both', expand=True, padx=5, pady=5)
            
            # 创建刷新按钮容器，使用Frame包装以控制位置
            button_frame = ttk.Frame(summary_frame)
            button_frame.pack(fill='x', padx=5, pady=5, side='bottom')
            
            # 添加刷新按钮
            refresh_button = ttk.Button(
                button_frame, 
                text="刷新风险度",
                command=self.update_risk_dashboard  # 直接使用方法引用
            )
            refresh_button.pack(fill='x')
            
            # 创建热力图区域，使用Frame包装以控制大小和位置
            heatmap_frame = ttk.Frame(heatmap_container)
            heatmap_frame.pack(side='right', fill='both', expand=True, padx=5)
            
            # 保存heatmap_frame的引用
            self.heatmap_frame = heatmap_frame
            
            # 创建热力图
            self.update_heatmap(heatmap_frame)
            
            # 创建下部风险详情列表
            self.create_risk_details(main_frame)
            
        except Exception as e:
            print(f"创建风险看板失败: {str(e)}")
            traceback.print_exc()

    def update_heatmap(self, heatmap_frame):
        """更新热力图"""
        if hasattr(self, 'canvas'):
            self.canvas.get_tk_widget().destroy()
        
        try:
            # 重置matplotlib设置
            plt.rcParams.update(plt.rcParamsDefault)
            plt.rcParams['font.family'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
            plt.rcParams['axes.unicode_minus'] = False
            plt.style.use('seaborn')
            
            # 创建新的图形
            self.risk_figure = plt.figure(figsize=(6, 6.5), dpi=100)  # 修改这里，保存figure引用
            ax = self.risk_figure.add_subplot(111)
            
            # 添加调试信息
            print("\n开始更新热力图:")
            print(f"[DEBUG] 当前需求ID: {self.current_requirement['id'] if self.current_requirement else 'None'}")  # 修改调试信息
            print(f"[DEBUG] 当前需求编号: {self.current_requirement['req_number'] if self.current_requirement else 'None'}")
            print("\n所有模块信息:")
            
            # 获取当前需求相关的模块和风险数据
            current_modules = {}
            if self.current_requirement:
                for module_id, module_info in self.business_modules.items():
                    # 只处理属于当前需求的模块
                    if module_info.get('requirement_id') == self.current_requirement['id']:
                        # 添加调试信息
                        print(f"\n处理模块 {module_id}:")
                        print(f"  需求ID匹配: {module_info.get('requirement_id')} == {self.current_requirement['id']}")
                        
                        # 计算模块风险
                        risk = 0
                        total_weight = 0
                        
                        # 计算该模块下属于当前需求的功能点的风险
                        if 'functions' in module_info:
                            for func_id, func_info in module_info['functions'].items():
                                if func_info.get('requirement_id') == self.current_requirement['id']:
                                    weight = func_info.get('weight', 0)
                                    risk += weight * func_info.get('risk', 0)
                                    total_weight += weight
                                    print(f"    功能点 {func_id}:")
                                    print(f"      权重: {weight}")
                                    print(f"      风险: {func_info.get('risk', 0)}")
                        
                        if total_weight > 0:
                            current_modules[module_id] = {
                                'name': module_info.get('name', ''),
                                'risk': risk / total_weight
                            }
                            print(f"  计算得到的模块风险: {risk / total_weight:.2%}")
                        else:
                            print("  模块权重为0，跳过")
            
            print(f"\n找到的相关模块数量: {len(current_modules)}")
            
            if not current_modules:  # 如果没有相关模块
                # 创建空白图表
                ax.text(0.5, 0.5, '当前需求暂无模块数据', 
                       ha='center', va='center',
                       fontsize=12,
                       family='Microsoft YaHei')
                ax.set_xticks([])
                ax.set_yticks([])
            else:
                # 计算热力图大小
                num_modules = len(current_modules)
                size = int(np.ceil(np.sqrt(num_modules)))
                risk_matrix = np.zeros((size, size))
                name_matrix = np.empty((size, size), dtype=object)
                
                # 填充矩阵
                for i, (module_id, module_data) in enumerate(current_modules.items()):
                    row = i // size
                    col = i % size
                    risk_matrix[row, col] = module_data['risk']
                    name_matrix[row, col] = module_data['name']
                
                # 创建热力图
                heatmap = ax.imshow(risk_matrix, 
                                   cmap='RdYlGn_r',
                                   vmin=0, vmax=1,
                                   aspect='equal')
                
                # 添加模块名称和风险值
                for i in range(size):
                    for j in range(size):
                        risk = risk_matrix[i, j]
                        module_name = name_matrix[i, j]
                        if module_name:
                            try:
                                color = 'white' if risk > 0.5 else 'black'
                                fontsize = min(10, 120 / max(len(module_name), 1))
                                
                                text = f"{module_name}\n{risk:.1%}"
                                bbox_props = dict(
                                    boxstyle='round,pad=0.3',
                                    fc='white' if risk > 0.5 else 'none',
                                    ec='gray',
                                    alpha=0.7
                                )
                                
                                ax.text(j, i, text,
                                       ha='center', va='center',
                                       color=color,
                                       fontsize=fontsize,
                                       fontweight='bold',
                                       bbox=bbox_props,
                                       family='Microsoft YaHei')
                            except Exception as e:
                                print(f"添加文本失败: {str(e)}")
                
                # 设置标题
                title = "模块风险热力图"
                if self.current_requirement:
                    title += f"\n{self.current_requirement['req_number']} - {self.current_requirement['title']}"
                
                plt.title(title, 
                         fontsize=12, 
                         pad=15,
                         fontweight='bold',
                         family='Microsoft YaHei',
                         y=1.02)
                
                # 设置颜色条
                cbar = plt.colorbar(heatmap)
                cbar.set_label('风险度', 
                              fontsize=10, 
                              fontweight='bold',
                              family='Microsoft YaHei')
                cbar.ax.tick_params(labelsize=8)
                
                # 移除坐标轴
                ax.set_xticks([])
                ax.set_yticks([])
                
                # 添加网格线
                for edge in range(size + 1):
                    ax.axhline(y=edge-0.5, color='white', linewidth=1)
                    ax.axvline(x=edge-0.5, color='white', linewidth=1)
            
            # 调整布局
            plt.tight_layout(rect=[0, 0, 1, 0.95])
            
            # 创建canvas并添加边框
            self.canvas = FigureCanvasTkAgg(self.risk_figure, master=heatmap_frame)
            self.canvas.draw()
            canvas_widget = self.canvas.get_tk_widget()
            canvas_widget.pack(side=tk.TOP, fill=tk.BOTH, expand=1, padx=5, pady=5)
            
        except Exception as e:
            print(f"热力图错误详情: {str(e)}")
            traceback.print_exc()
            messagebox.showerror("错误", f"更新热力图失败: {str(e)}")

    def create_risk_details(self, main_frame):
        """创建风险详情列表"""
        details_frame = ttk.LabelFrame(main_frame, text="风险详情")
        details_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 创建详情表格
        columns = ("module", "weight", "risk", "status", "warning", "func_count")
        self.risk_tree = ttk.Treeview(details_frame, columns=columns, show='headings')
        
        # 设置列标题
        self.risk_tree.heading("module", text="模块")
        self.risk_tree.heading("weight", text="权重")
        self.risk_tree.heading("risk", text="风险度")
        self.risk_tree.heading("status", text="状态")
        self.risk_tree.heading("warning", text="预警信息")
        self.risk_tree.heading("func_count", text="功能点数")
        
        # 设置列宽
        self.risk_tree.column("module", width=150)
        self.risk_tree.column("weight", width=80)
        self.risk_tree.column("risk", width=80)
        self.risk_tree.column("status", width=80)
        self.risk_tree.column("warning", width=150)
        self.risk_tree.column("func_count", width=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(details_frame, orient="vertical", command=self.risk_tree.yview)
        self.risk_tree.configure(yscrollcommand=scrollbar.set)
        
        self.risk_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

    def create_config_panel(self):
        """创建配置面板"""
        # 创建顶部工具栏区域
        toolbar_frame = ttk.Frame(self.config_frame)
        toolbar_frame.pack(fill='x', padx=5, pady=5)
        
        # 左侧显示当前需求
        self.current_req_label = ttk.Label(toolbar_frame, text="当前需求: 无")
        self.current_req_label.pack(side='left', padx=5)
        
        # 右侧放置按钮
        right_buttons = ttk.Frame(toolbar_frame)
        right_buttons.pack(side='right', padx=5)
        
        # 添加新增子模块和功能点按钮，并设置间距
        ttk.Button(right_buttons, text="新增子模块", 
                  command=self.add_new_submodule).pack(side='left', padx=(0, 5))  # 右边距5
        ttk.Button(right_buttons, text="新增功能点", 
                  command=self.add_new_function).pack(side='left', padx=(5, 0))   # 左边距5
        
        # 创建水平分割窗口
        self.config_paned = ttk.PanedWindow(self.config_frame, orient=tk.HORIZONTAL)
        self.config_paned.pack(fill='both', expand=True)
        
        # 创建左侧树形结构面板
        tree_frame = ttk.Frame(self.config_paned)
        
        # 创建树形表格
        columns = (
            "name",          # 名称
            "weight",        # 权重
            "total_cases",   # 需求对应用例数
            "executed_cases", # 需求对应用例执行数
            "case_coverage", # 测试用例覆盖率
            "w1",           # 测试用例覆盖率权重
            "branch_total", # 需求对应代码分支数
            "branch_covered", # 需求测试覆盖代码分支数
            "branch_coverage", # 分支覆盖率
            "w2",           # 分支覆盖率权重
            "business_cases", # 业务模块对应用例数
            "business_covered", # 业务模块回归覆盖用例数
            "business_coverage", # 业务覆盖度
            "w3",           # 业务覆盖度权重
            "risk"          # 功能点风险度
        )
        
        # 列标题中文映射
        column_names = {
            "name": "名称",
            "weight": "权重",
            "total_cases": "需求用例数",
            "executed_cases": "已执行用例数",
            "case_coverage": "用例覆盖率",
            "w1": "用例权重",
            "branch_total": "分支总数",
            "branch_covered": "已覆盖分支",
            "branch_coverage": "分支覆盖率",
            "w2": "分支权重",
            "business_cases": "业务用例数",
            "business_covered": "回归覆盖数",
            "business_coverage": "业务覆盖度",
            "w3": "业务权重",
            "risk": "风险度"
        }
        
        self.tree = ttk.Treeview(tree_frame, columns=columns)
        
        # 设置列标题
        self.tree.heading("#0", text="ID")
        for col in columns:
            self.tree.heading(col, text=column_names[col])
        
        # 设置列宽
        self.tree.column("#0", width=80)
        for col in columns:
            self.tree.column(col, width=100)
        
        # 添加滚动条
        h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.tree.xview)
        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # 使用grid布局管理器
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # 配置grid权重
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # 创建右侧编辑面板，设置最小宽度
        self.edit_frame = ttk.LabelFrame(self.config_paned, text="编辑面板", width=300)
        
        # 将左右两个面板添加到PanedWindow
        self.config_paned.add(tree_frame, weight=1)
        self.config_paned.add(self.edit_frame, weight=0)
        
        # 设置编辑面板的最小宽度
        self.edit_frame.configure(width=300)
        self.edit_frame.pack_propagate(False)
        
        # 创建子模块和功能点编辑界面
        self.create_edit_interfaces()
        
        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_tree_select)
        
        # 加载初始数据
        self.load_tree_data()

    def create_edit_interfaces(self):
        """创建编辑界面"""
        # 创建子模块编辑界面
        self.submodule_frame = ttk.Frame(self.edit_frame)
        
        # 子模块编辑区域
        ttk.Label(self.submodule_frame, text="子模块配置", font=('Arial', 10, 'bold')).pack(pady=5)
        
        # 子模块输入区域
        input_frame = ttk.Frame(self.submodule_frame)
        input_frame.pack(fill='x', padx=5, pady=5)
        
        # 名称输入
        ttk.Label(input_frame, text="名称:").pack(anchor='w')
        self.submodule_name_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.submodule_name_var).pack(fill='x', pady=2)
        
        # 权重输入
        ttk.Label(input_frame, text="权重:").pack(anchor='w')
        self.submodule_weight_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.submodule_weight_var).pack(fill='x', pady=2)
        
        # 按钮区域 - 只保留保存和删除按钮
        button_frame = ttk.Frame(self.submodule_frame)
        button_frame.pack(fill='x', padx=5, pady=5)
        ttk.Button(button_frame, text="保存子模块", command=self.save_submodule).pack(side='left', padx=5)
        ttk.Button(button_frame, text="删除子模块", command=self.delete_submodule).pack(side='left', padx=5)

        
        # 创建功能点编辑界面
        self.function_frame = ttk.Frame(self.edit_frame)
        ttk.Label(self.function_frame, text="功能点配置", font=('Arial', 10, 'bold')).pack(pady=5)
        
        # 功能点编辑字段
        self.function_vars = {}
        fields = [
            ("名称:", "name"),
            ("权重:", "weight"),
            ("需求用例数:", "total_cases"),
            ("已执行用例数:", "executed_cases"),
            ("用例覆盖率权重:", "w1"),
            ("代码分支总数:", "branch_total"),
            ("已覆盖分支数:", "branch_covered"),
            ("分支覆盖率权重:", "w2"),
            ("业务用例总数:", "business_cases"),
            ("回归用例数:", "business_covered"),
            ("业务覆盖度权重:", "w3")
        ]
        
        # 创建可滚动的容器
        canvas = tk.Canvas(self.function_frame)
        scrollbar = ttk.Scrollbar(self.function_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        # 配置滚动区域
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 添加字段
        for label, key in fields:
            frame = ttk.Frame(scrollable_frame)
            frame.pack(fill='x', padx=5, pady=2)
            ttk.Label(frame, text=label).pack(side='left')
            var = tk.StringVar()
            self.function_vars[key] = var
            ttk.Entry(frame, textvariable=var).pack(side='right', expand=True, fill='x')
        
        # 按钮区域 - 只保留保存和删除按钮
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.pack(fill='x', padx=5, pady=5)
        ttk.Button(button_frame, text="保存功能点", command=self.save_function).pack(side='left', padx=5)
        ttk.Button(button_frame, text="删除功能点", command=self.delete_function).pack(side='left', padx=5)
        
        # 布局滚动区域
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 初始时隐藏所有面板
        self.submodule_frame.pack_forget()
        self.function_frame.pack_forget()

    def on_tree_select(self, event):
        """处理树形结构选择事件"""
        selected = self.tree.selection()
        if not selected:
            return
        
        item = selected[0]
        values = self.tree.item(item)['values']
        
        # 隐藏所有面板
        self.submodule_frame.pack_forget()
        self.function_frame.pack_forget()
        
        if '_func_' in item:  # 功能点
            # 显示功能点编辑界面
            self.function_frame.pack(fill='both', expand=True)
            # 加载功能点数据
            self.load_function_data(item, values)
        else:  # 子模块
            # 显示子模块编辑界面
            self.submodule_frame.pack(fill='both', expand=True)
            # 加载子模块数据
            self.submodule_name_var.set(values[0])
            self.submodule_weight_var.set(values[1])

    def load_submodules(self, module_id):
        """加载子模块数据"""
        # 清空子模块表格
        for item in self.submodule_tree.get_children():
            self.submodule_tree.delete(item)
        
        # 加载子模块数据
        if 'functions' in self.business_modules[module_id]:
            for func_id, func_info in self.business_modules[module_id]['functions'].items():
                if isinstance(func_info, dict):
                    self.submodule_tree.insert("", "end", func_id, 
                                             values=(func_info.get('name', ''), 
                                                    func_info.get('weight', 10)))

    def load_function_data(self, function_id, values):
        """加载功能点数据"""
        if not values:
            return
        
        # 从配置数据中获取完整的功能点数据
        module_id = function_id.split('_')[0]  # 获取父模块ID
        func_data = self.business_modules[module_id]['functions'].get(function_id, {})
        
        # 设置功能点数据，确保数值格式正确
        self.function_vars['name'].set(func_data.get('name', ''))
        self.function_vars['weight'].set(str(func_data.get('weight', 0)))  # 修改：直接显示小数
        self.function_vars['total_cases'].set(str(func_data.get('total_cases', 0)))
        self.function_vars['executed_cases'].set(str(func_data.get('executed_cases', 0)))
        self.function_vars['w1'].set(str(func_data.get('w1', 0.33)))  # 直接显示小数
        self.function_vars['branch_total'].set(str(func_data.get('branch_total', 0)))
        self.function_vars['branch_covered'].set(str(func_data.get('branch_covered', 0)))
        self.function_vars['w2'].set(str(func_data.get('w2', 0.33)))  # 直接显示小数
        self.function_vars['business_cases'].set(str(func_data.get('business_cases', 0)))
        self.function_vars['business_covered'].set(str(func_data.get('business_covered', 0)))
        self.function_vars['w3'].set(str(func_data.get('w3', 0.34)))  # 直接显示小数

    def add_new_submodule(self):
        """新增子模块"""
        # 生成新的模块ID - 修改ID生成逻辑
        max_num = 0
        for module_id in self.business_modules.keys():
            try:
                # 修改：检查M开头的模块ID
                if module_id.startswith('M'):
                    num = int(module_id[1:])  # 从M后面提取数字
                    max_num = max(max_num, num)
            except (ValueError, IndexError):
                continue
            
        new_id = f"M{max_num + 1}"  # 修改：使用M前缀格式
        
        # 创建新模块数据
        self.business_modules[new_id] = {
            'name': "新子模块",
            'weight': 0.1,  # 默认权重为0.1
            'functions': {}
        }
        
        # 更新树形结构
        values = [
            "新子模块",
            "0.100",  # 显示三位小数
            "", "", "", "", "", "", "", "", "", "", "", ""
        ]
        self.tree.insert("", "end", new_id, text=new_id, values=values)
        
        # 保存配置
        self.save_config()

    def delete_submodule(self):
        """删除子模块"""
        selected = self.tree.selection()
        if not selected or '_' in selected[0]:  # 确保选中的是子模块
            messagebox.showwarning("警告", "请先选择一个子模块")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的子模块吗？"):
            for item in selected:
                # 从树形结构中删除
                self.tree.delete(item)
                
                # 从配置数据中删除
                if item in self.business_modules:
                    del self.business_modules[item]
            
            # 保存配置
            self.save_config()
            
            # 清空编辑面板
            self.submodule_name_var.set("")
            self.submodule_weight_var.set("")
            
            messagebox.showinfo("成功", "子模块已删除")

    def save_submodule(self):
        """保存子模块配置"""
        try:
            selected = self.tree.selection()
            if not selected or '_' in selected[0]:  # 确保选中的是子模块
                messagebox.showwarning("警告", "请先选择一个子模块")
                return
            
            module_id = selected[0]
            
            # 获取输入值
            name = self.submodule_name_var.get().strip()
            if not name:
                raise ValueError("名称不能为空")
                
            # 获取权重 - 直接使用小数
            weight = float(self.submodule_weight_var.get())
            if not (0 <= weight <= 1):  # 修改：检查范围改为0-1
                raise ValueError("权重必须在0-1之间")  # 修改：提示信息改为0-1
            
            # 更新配置数据
            self.business_modules[module_id].update({
                'name': name,
                'weight': weight  # 直接存储小数
            })
            
            # 更新树形结构显示
            self.tree.set(module_id, 'name', name)
            self.tree.set(module_id, 'weight', f"{weight:.3f}")  # 显示三位小数
            
            # 保存配置
            self.save_config()
            messagebox.showinfo("成功", "子模块配置已保存")
            
        except ValueError as e:
            messagebox.showerror("错误", str(e))
        except Exception as e:
            messagebox.showerror("错误", f"保存子模块失败: {str(e)}")

    def save_function(self):
        """保存功能点配置"""
        try:
            selected = self.tree.selection()
            if not selected or '_func_' not in selected[0]:
                messagebox.showwarning("警告", "请先选择一个功能点")
                return
            
            module_id = selected[0].split('_func_')[0]
            func_id = selected[0]
            
            # 获取输入值
            name = self.function_vars['name'].get().strip()
            if not name:
                raise ValueError("名称不能为空")
                
            # 获取权重 - 直接使用小数
            weight = float(self.function_vars['weight'].get())
            if not (0 <= weight <= 1):  # 修改：检查范围改为0-1
                raise ValueError("权重必须在0-1之间")  # 修改：提示信息改为0-1
                
            # 获取用例数据
            total_cases = float(self.function_vars['total_cases'].get() or 0)
            executed_cases = float(self.function_vars['executed_cases'].get() or 0)
            execution_rate = (executed_cases / total_cases) if total_cases > 0 else 0
            
            # 获取分支覆盖数据
            branch_total = float(self.function_vars['branch_total'].get() or 0)
            branch_covered = float(self.function_vars['branch_covered'].get() or 0)
            coverage_rate = (branch_covered / branch_total) if branch_total > 0 else 0
            
            # 获取业务覆盖数据
            business_cases = float(self.function_vars['business_cases'].get() or 0)
            business_covered = float(self.function_vars['business_covered'].get() or 0)
            business_coverage = (business_covered / business_cases) if business_cases > 0 else 0
            
            # 直接使用小数形式的权重
            w1 = float(self.function_vars['w1'].get() or 0.33)  # 修改：直接使用小数
            w2 = float(self.function_vars['w2'].get() or 0.33)
            w3 = float(self.function_vars['w3'].get() or 0.34)
            
            # 确保权重之和为1
            total_weight = w1 + w2 + w3
            if abs(total_weight - 1.0) > 0.0001:
                w1, w2, w3 = w1/total_weight, w2/total_weight, w3/total_weight
            
            # 计算风险度
            risk = self.calculate_risk(
                execution_rate,
                coverage_rate,
                business_coverage,
                w1, w2, w3
            )
            
            # 更新配置数据
            if module_id in self.business_modules:
                if 'functions' not in self.business_modules[module_id]:
                    self.business_modules[module_id]['functions'] = {}
                    
                self.business_modules[module_id]['functions'][func_id] = {
                    'name': name,
                    'weight': weight,  # 修改：直接存储小数，不需要转换
                    'total_cases': total_cases,
                    'executed_cases': executed_cases,
                    'execution_rate': execution_rate,
                    'branch_total': branch_total,
                    'branch_covered': branch_covered,
                    'coverage_rate': coverage_rate,
                    'business_cases': business_cases,
                    'business_covered': business_covered,
                    'business_coverage': business_coverage,
                    'w1': w1,  # 直接存储小数形式
                    'w2': w2,
                    'w3': w3,
                    'risk': risk,
                    'requirement_id': self.current_requirement['id']
                }
                
                # 保存配置
                self.save_config()
                
                # 记住当前展开的节点
                expanded_items = [item for item in self.tree.get_children() 
                                if self.tree.item(item, 'open')]
                
                # 刷新配置面板
                self.refresh_config_panel()
                
                # 恢复展开状态
                for item in expanded_items:
                    self.tree.item(item, open=True)
                
                # 确保当前模块保持展开
                self.tree.item(module_id, open=True)
                
                # 选中保存的功能点
                self.tree.selection_set(func_id)
                self.tree.see(func_id)  # 确保功能点可见
                
                messagebox.showinfo("成功", "功能点配置已保存")
                
        except ValueError as e:
            messagebox.showerror("错误", str(e))
        except Exception as e:
            print(f"[ERROR] 保存功能点失败: {str(e)}")
            traceback.print_exc()
            messagebox.showerror("错误", f"保存功能点失败: {str(e)}")

    def delete_function(self):
        """删除功能点"""
        selected = self.tree.selection()
        if not selected or '_func_' not in selected[0]:
            messagebox.showwarning("警告", "请先选择一个功能点")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的功能点吗？"):
            for item in selected:
                # 从树形结构中删除
                self.tree.delete(item)
                
                # 从配置数据中删除
                module_id = item.split('_')[0]
                if 'functions' in self.business_modules[module_id]:
                    if item in self.business_modules[module_id]['functions']:
                        del self.business_modules[module_id]['functions'][item]
            
            # 保存配置
            self.save_config()
            
            # 清空编辑面板
            for var in self.function_vars.values():
                var.set("")
            
            messagebox.showinfo("成功", "功能点已删除")

    def load_tree_data(self):
        """加载树形结构数据"""
        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 加载子模块数据（一级节点）
            for module_id, module_info in self.business_modules.items():
                # 子模块值
                sub_values = [
                    module_info["name"],
                    f"{module_info.get('weight', 0):.3f}",  # 修改：直接显示小数，不乘100
                    "", "", "", "", "", "", "", "", "", "", "", ""
                ]
                
                # 插入子模块
                self.tree.insert("", "end", module_id, text=module_id, values=sub_values)
                
                # 加载功能点数据（二级节点）
                if "functions" in module_info:
                    for func_id, func_info in module_info["functions"].items():
                        if isinstance(func_info, dict):
                            # 计算覆盖率
                            total_cases = func_info.get("total_cases", 0)
                            executed_cases = func_info.get("executed_cases", 0)
                            branch_total = func_info.get("branch_total", 0)
                            branch_covered = func_info.get("branch_covered", 0)
                            business_cases = func_info.get("business_cases", 0)
                            business_covered = func_info.get("business_covered", 0)
                            
                            case_coverage = (executed_cases / total_cases * 100) if total_cases > 0 else 0
                            branch_coverage = (branch_covered / branch_total * 100) if branch_total > 0 else 0
                            business_coverage = (business_covered / business_cases * 100) if business_cases > 0 else 0
                            
                            # 使用已经存储的小数形式权重
                            w1 = func_info.get("w1", 0.33)  # 直接使用小数
                            w2 = func_info.get("w2", 0.33)
                            w3 = func_info.get("w3", 0.34)
                            
                            # 确保权重之和为1
                            weight_sum = w1 + w2 + w3
                            if abs(weight_sum - 1.0) > 0.0001:
                                w1, w2, w3 = w1/weight_sum, w2/weight_sum, w3/weight_sum
                            
                            # 计算风险度
                            risk = self.calculate_risk(
                                executed_cases/total_cases if total_cases > 0 else 0,
                                branch_covered/branch_total if branch_total > 0 else 0,
                                business_covered/business_cases if business_cases > 0 else 0,
                                w1, w2, w3
                            )
                            
                            # 获取功能点权重并显示
                            weight = func_info.get("weight", 0)
                            
                            func_values = [
                                func_info.get("name", ""),
                                f"{weight:.3f}",  # 修改：直接显示小数，不乘100
                                str(total_cases),
                                str(executed_cases),
                                f"{case_coverage:.1f}",
                                str(w1),  # 直接显示小数
                                str(branch_total),
                                str(branch_covered),
                                f"{branch_coverage:.1f}",
                                str(w2),  # 直接显示小数
                                str(business_cases),
                                str(business_covered),
                                f"{business_coverage:.1f}",
                                str(w3),  # 直接显示小数
                                f"{risk*100:.1f}"
                            ]
                            
                            if not self.tree.exists(func_id):
                                self.tree.insert(module_id, "end", func_id, text=func_id, values=func_values)
                                
        except Exception as e:
            messagebox.showerror("错误", f"加载树形结构数据失败: {str(e)}")

    def save_config(self):
        """保存配置到文件"""
        try:
            # 将当前配置保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'business_modules': self.business_modules
                }, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def import_config(self):
        """导入配置"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择配置文件",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, dict) and 'business_modules' in data:
                        self.business_modules = data['business_modules']
                        self.save_config()  # 保存到默认配置文件
                        self.load_tree_data()  # 刷新树形结构
                        self.update_risk_dashboard()  # 更新风险看板
                        messagebox.showinfo("成功", "配置导入成功")
                    else:
                        messagebox.showerror("错误", "无效的配置文件格式")
        except Exception as e:
            messagebox.showerror("错误", f"导入配置失败: {str(e)}")

    def export_config(self):
        """导出配置"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存配置文件",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if file_path:
                data = {
                    'business_modules': self.business_modules
                }
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", "配置导出成功")
        except Exception as e:
            messagebox.showerror("错误", f"导出配置失败: {str(e)}")

    def generate_risk_report(self):
        """生成风险报告"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".html",
            filetypes=[("HTML files", "*.html"), ("All files", "*.*")]
        )
        if not file_path:
            return

        try:
            risks = self.calculate_all_risks()
            total_risk = sum(risk * self.business_modules[module_id]['weight'] 
                            for module_id, risk in risks.items())
            
            # 生成HTML报告头部
            html_parts = []
            html_parts.append("""
                <html>
                <head>
                    <title>质量风险评估报告</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                        .high-risk { background-color: #ffcccc; }
                        .medium-risk { background-color: #ffffcc; }
                        .low-risk { background-color: #ccffcc; }
                        .summary { margin: 20px 0; }
                        .module-details { margin: 20px 0; }
                    </style>
                </head>
                <body>
            """)

            # 添加报告标题和总体信息
            html_parts.append(f"""
                <h1>质量风险评估报告</h1>
                <p>生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                
                <div class="summary">
                    <h2>总体风险评估</h2>
                    <p>总体风险度：<strong>{total_risk:.1%}</strong></p>
                    <p>风险状态：<strong>{self.get_risk_status(total_risk)}</strong></p>
                    <p>预警信息：<strong>{self.get_risk_warning(total_risk)}</strong></p>
                </div>
            """)

            # 添加模块风险详情表格
            html_parts.append("""
                <div class="module-details">
                    <h2>模块风险详情</h2>
                    <table>
                        <tr>
                            <th>模块</th>
                            <th>权重</th>
                            <th>风险度</th>
                            <th>状态</th>
                            <th>预警信息</th>
                            <th>功能点数量</th>
                        </tr>
            """)

            # 添加模块数据
            for module_id, risk in risks.items():
                module_info = self.business_modules[module_id]
                func_count = len(module_info.get('functions', {}))
                risk_class = 'high-risk' if risk > self.risk_thresholds['medium'] else \
                            'medium-risk' if risk > self.risk_thresholds['low'] else 'low-risk'
                
                html_parts.append(f"""
                    <tr class="{risk_class}">
                        <td>{module_info['name']}</td>
                        <td>{module_info['weight']:.1%}</td>
                        <td>{risk:.1%}</td>
                        <td>{self.get_risk_status(risk)}</td>
                        <td>{self.get_risk_warning(risk)}</td>
                        <td>{func_count}</td>
                    </tr>
                """)

            # 添加功能点详情表格头部
            html_parts.append("""
                    </table>
                    
                    <h2>功能点风险详情</h2>
                    <table>
                        <tr>
                            <th>模块</th>
                            <th>功能点</th>
                            <th>权重</th>
                            <th>用例覆盖率</th>
                            <th>分支覆盖率</th>
                            <th>业务覆盖度</th>
                            <th>风险度</th>
                        </tr>
            """)

            # 添加功能点数据
            for module_id, module_info in self.business_modules.items():
                if 'functions' in module_info:
                    for func_id, func_info in module_info['functions'].items():
                        risk = func_info.get('risk', 0)
                        risk_class = 'high-risk' if risk > self.risk_thresholds['medium'] else \
                                   'medium-risk' if risk > self.risk_thresholds['low'] else 'low-risk'
                        
                        html_parts.append(f"""
                            <tr class="{risk_class}">
                                <td>{module_info['name']}</td>
                                <td>{func_info['name']}</td>
                                <td>{func_info['weight']:.1%}</td>
                                <td>{func_info.get('case_coverage', 0):.1f}%</td>
                                <td>{func_info.get('branch_coverage', 0):.1f}%</td>
                                <td>{func_info.get('business_coverage', 0):.1f}%</td>
                                <td>{risk*100:.1f}%</td>
                            </tr>
                        """)

            # 添加HTML尾部
            html_parts.append("""
                    </table>
                </div>
            </body>
            </html>
            """)

            # 合并所有HTML部分并写入文件
            html_content = "".join(html_parts)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            messagebox.showinfo("成功", "风险报告生成成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {str(e)}")

    def show_risk_settings(self):
        """显示风险阈值设置对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("风险阈值设置")
        dialog.geometry("300x200")
        dialog.resizable(False, False)
        
        # 创建输入框架
        frame = ttk.Frame(dialog, padding="10")
        frame.pack(fill='both', expand=True)
        
        # 低风险阈值
        ttk.Label(frame, text="低风险阈值:").grid(row=0, column=0, sticky='w', pady=5)
        low_var = tk.StringVar(value=f"{self.risk_thresholds['low']:.2f}")
        ttk.Entry(frame, textvariable=low_var).grid(row=0, column=1, sticky='ew', pady=5)
        
        # 中风险阈值
        ttk.Label(frame, text="中风险阈值:").grid(row=1, column=0, sticky='w', pady=5)
        medium_var = tk.StringVar(value=f"{self.risk_thresholds['medium']:.2f}")
        ttk.Entry(frame, textvariable=medium_var).grid(row=1, column=1, sticky='ew', pady=5)
        
        # 高风险阈值
        ttk.Label(frame, text="高风险阈值:").grid(row=2, column=0, sticky='w', pady=5)
        high_var = tk.StringVar(value=f"{self.risk_thresholds['high']:.2f}")
        ttk.Entry(frame, textvariable=high_var).grid(row=2, column=1, sticky='ew', pady=5)
        
        def save_thresholds():
            try:
                # 获取并验证输入值
                low = float(low_var.get())
                medium = float(medium_var.get())
                high = float(high_var.get())
                
                # 验证阈值范围和顺序
                if not (0 <= low <= medium <= high <= 1):
                    raise ValueError("阈值必须在0-1之间，且满足 低 <= 中 <= 高")
                
                # 更新阈值
                self.risk_thresholds.update({
                    'low': low,
                    'medium': medium,
                    'high': high
                })
                
                # 保存到配置文件
                self.save_risk_thresholds()
                
                # 刷新风险看板
                self.update_risk_dashboard()
                
                messagebox.showinfo("成功", "风险阈值已更新")
                dialog.destroy()
                
            except ValueError as e:
                messagebox.showerror("错误", str(e))
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
        
        # 按钮区域
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="保存", command=save_thresholds).pack(side='left', padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side='left', padx=5)
        
        # 配置网格布局
        frame.columnconfigure(1, weight=1)

    def save_risk_thresholds(self):
        """保存风险阈值到配置文件"""
        try:
            config_data = {
                'risk_thresholds': self.risk_thresholds,
                'business_modules': self.business_modules
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
                
            print("[DEBUG] 风险阈值已保存到配置文件")
            
        except Exception as e:
            print(f"[ERROR] 保存风险阈值失败: {str(e)}")
            raise

    def calculate_risk(self, execution_rate, coverage_rate, business_coverage, w1=0.4, w2=0.35, w3=0.25):
        """计算风险度"""
        try:
            print(f"\n[DEBUG] === 计算功能点风险 ===")
            print(f"[DEBUG] 执行率: {execution_rate:.2%}")
            print(f"[DEBUG] 覆盖率: {coverage_rate:.2%}")
            print(f"[DEBUG] 业务覆盖率: {business_coverage:.2%}")
            print(f"[DEBUG] 权重: w1={w1:.2f}, w2={w2:.2f}, w3={w3:.2f}")
            
            # 确保权重之和为1
            total_weight = w1 + w2 + w3
            if abs(total_weight - 1.0) > 0.0001:  # 添加：检查权重之和是否接近1
                print(f"[WARNING] 权重之和不为1: {total_weight:.2f}，进行归一化")
                w1, w2, w3 = w1/total_weight, w2/total_weight, w3/total_weight
            
            risk = (
                (1 - execution_rate) * w1 +
                (1 - coverage_rate) * w2 +
                (1 - business_coverage) * w3
            )
            print(f"[DEBUG] 计算得到的风险度: {risk:.2%}")
            return risk
        except Exception as e:
            print(f"[ERROR] 计算风险度失败: {str(e)}")
            traceback.print_exc()
            return 0

    def calculate_all_risks(self) -> Dict[str, float]:
        """计算所有模块的风险度"""
        risks = {}
        
        print("\n[DEBUG] ====== 开始计算所有模块风险 ======")
        print(f"[DEBUG] 当前需求ID: {self.current_requirement['id'] if self.current_requirement else 'None'}")
        
        if not self.current_requirement:
            print("[DEBUG] 没有当前需求，返回空风险字典")
            return risks
        
        for module_id, module_info in self.business_modules.items():
            print(f"\n[DEBUG] === 处理模块 {module_id} ===")
            
            # 只处理属于当前需求的模块
            if module_info.get('requirement_id') != self.current_requirement['id']:
                print("[DEBUG] 模块不属于当前需求，跳过")
                continue
            
            # 获取该模块下属于当前需求的所有功能点
            current_functions = {
                func_id: func_info 
                for func_id, func_info in module_info.get('functions', {}).items()
                if func_info.get('requirement_id') == self.current_requirement['id']
            }
            
            if not current_functions:
                print("[DEBUG] 没有找到属于当前需求的功能点，风险设为0")
                risks[module_id] = 0
                continue
            
            # 获取所有功能点的权重
            weights = [f.get('weight', 0) for f in current_functions.values()]
            total_weight = sum(weights)
            
            # 归一化处理
            if total_weight > 0:
                normalized_weights = [w/total_weight for w in weights]
                risk_values = [f.get('risk', 0) for f in current_functions.values()]
                
                # 使用归一化后的权重计算模块风险度
                module_risk = sum(w * r for w, r in zip(normalized_weights, risk_values))
                print(f"[DEBUG] 计算得到的模块风险: {module_risk:.2%}")
                risks[module_id] = module_risk
            else:
                print("[DEBUG] 总权重为0，风险设为0")
                risks[module_id] = 0
        
        return risks

    def update_risk_dashboard(self, event=None):  # 添加可选的event参数
        """更新风险看板"""
        try:
            print("\n[DEBUG] === 开始更新风险看板 ===")
            print(f"[DEBUG] 调用方式: {'事件触发' if event else '直接调用'}")
            
            if not self.current_requirement:
                print("[DEBUG] 没有选中的需求")
                return
                
            # 更新热力图
            if hasattr(self, 'heatmap_frame'):
                self.update_risk_heatmap(self.current_requirement)
            
            # 更新风险总结
            self.update_risk_summary(self.current_requirement)
            
            # 更新风险详情
            self.update_risk_details()  # 不传递参数
            
            print("[DEBUG] 风险看板更新完成")
            
        except Exception as e:
            print(f"[ERROR] 更新风险看板失败: {str(e)}")
            print(f"[ERROR] 错误类型: {type(e).__name__}")
            print(f"[ERROR] 错误详情:")
            traceback.print_exc()

    def update_risk_summary(self, requirement):
        """更新风险评估总结"""
        try:
            print("\n[DEBUG] === 更新风险评估总结 ===")
            module_risks = {}
            
            # 遍历所有模块，查找当前需求相关的功能点
            for module_id, module_info in self.business_modules.items():
                module_risk = 0
                module_weight = 0
                has_requirement_functions = False
                
                # 只计算当前需求相关的功能点
                for func_id, func_info in module_info.get('functions', {}).items():
                    if func_info.get('requirement_id') == self.current_requirement['id']:
                        has_requirement_functions = True
                        risk = func_info.get('risk', 0)
                        weight = func_info.get('weight', 0)
                        module_risk += risk * weight
                        module_weight += weight
                
                # 只添加包含当前需求功能点的模块
                if has_requirement_functions and module_weight > 0:
                    # 计算模块风险度
                    module_risks[module_id] = {
                        'risk': module_risk / module_weight,  # 模块的平均风险度
                        'weight': module_info.get('weight', 0)  # 模块的权重
                    }
                    print(f"[DEBUG] 模块 {module_id} 风险度: {module_risks[module_id]['risk']:.2%}, 权重: {module_risks[module_id]['weight']:.3f}")
            
            if module_risks:
                # 计算总体风险度：各模块风险度 * 各自权重 之和 / 权重之和
                total_weighted_risk = sum(info['risk'] * info['weight'] for info in module_risks.values())
                total_weight = sum(info['weight'] for info in module_risks.values())
                
                if total_weight > 0:
                    overall_risk = total_weighted_risk / total_weight
                    print(f"[DEBUG] 总体风险度: {overall_risk:.2%} (总权重: {total_weight:.3f})")
                    
                    # 更新风险总结显示
                    summary_text = (
                        f"总体风险度: {overall_risk:.2%}\n\n"
                        f"高风险模块数: {sum(1 for info in module_risks.values() if info['risk'] > self.risk_thresholds['high'])}\n"
                        f"中风险模块数: {sum(1 for info in module_risks.values() if self.risk_thresholds['medium'] < info['risk'] <= self.risk_thresholds['high'])}\n"
                        f"低风险模块数: {sum(1 for info in module_risks.values() if info['risk'] <= self.risk_thresholds['medium'])}"
                    )
                    self.risk_summary_text.delete('1.0', tk.END)
                    self.risk_summary_text.insert('1.0', summary_text)
                    
                    # 更新总体风险度标签
                    self.total_risk_label.config(text=f"{overall_risk:.1%}")
                    
                    # 更新需求列表中的总体风险度
                    if self.current_requirement and 'id' in self.current_requirement:
                        # 更新内存中的需求数据
                        self.requirements[self.current_requirement['id']]['risk'] = overall_risk
                        
                        # 更新需求列表显示
                        for item in self.req_tree.get_children():
                            if item == self.current_requirement['id']:
                                current_values = list(self.req_tree.item(item)['values'])
                                current_values[-1] = f"{overall_risk:.1%}"  # 更新最后一列的风险度
                                self.req_tree.item(item, values=current_values)
                                break
                        
                        # 保存需求数据到文件
                        self.save_requirements()
                        print(f"[DEBUG] 已更新需求 {self.current_requirement['id']} 的总体风险度: {overall_risk:.2%}")
                
        except Exception as e:
            print(f"[ERROR] 更新风险总结失败: {str(e)}")
            traceback.print_exc()

    def update_risk_details(self):
        """更新风险详情列表"""
        try:
            print("\n[DEBUG] === 开始更新风险详情 ===")
            
            # 清空现有内容
            for item in self.risk_tree.get_children():
                self.risk_tree.delete(item)
            
            # 遍历所有模块，查找当前需求相关的模块
            for module_id, module_info in self.business_modules.items():
                module_risk = 0
                module_weight = 0
                func_count = 0
                
                # 检查模块下的功能点是否属于当前需求
                for func_info in module_info.get('functions', {}).values():
                    if func_info.get('requirement_id') == self.current_requirement['id']:
                        risk = func_info.get('risk', 0)
                        weight = func_info.get('weight', 0)
                        module_risk += risk * weight
                        module_weight += weight
                        func_count += 1
                
                # 如果模块包含当前需求的功能点
                if func_count > 0:
                    # 计算模块风险
                    avg_risk = module_risk / module_weight if module_weight > 0 else 0
                    
                    # 获取风险状态和预警信息
                    status = self.get_risk_status(avg_risk)
                    warning = self.get_risk_warning(avg_risk)
                    
                    # 设置风险等级样式
                    tags = ('high_risk',) if avg_risk > self.risk_thresholds['medium'] else \
                           ('medium_risk',) if avg_risk > self.risk_thresholds['low'] else \
                           ('low_risk',)
                    
                    # 添加模块信息到表格
                    self.risk_tree.insert("", "end", values=(
                        module_info['name'],  # 模块名称
                        f"{module_info.get('weight', 0):.3f}",  # 修改：直接显示小数，不乘100
                        f"{avg_risk:.1%}",  # 风险度
                        status,  # 状态
                        warning,  # 预警信息
                        func_count  # 功能点数量
                    ), tags=tags)
            
            # 设置不同风险等级的颜色
            self.risk_tree.tag_configure('high_risk', background='#ffcccc')  # 高风险-红色
            self.risk_tree.tag_configure('medium_risk', background='#ffffcc')  # 中风险-黄色
            self.risk_tree.tag_configure('low_risk', background='#ccffcc')  # 低风险-绿色
            
            print("[DEBUG] 风险详情更新完成")
            
        except Exception as e:
            print(f"[ERROR] 更新风险详情失败: {str(e)}")
            traceback.print_exc()

    def get_risk_status(self, risk: float) -> str:
        """获取风险状态"""
        if risk <= self.risk_thresholds['low']:
            return "正常"
        elif risk <= self.risk_thresholds['medium']:
            return "警告"
        else:
            return "危险"

    def get_risk_warning(self, risk: float) -> str:
        """获取风险预警信息"""
        if risk <= self.risk_thresholds['low']:
            return "风险可控"
        elif risk <= self.risk_thresholds['medium']:
            return "需要关注"
        elif risk <= self.risk_thresholds['high']:
            return "需要立即处理"
        else:
            return "严重风险！"

    def validate_numeric_input(self, value: str, field_name: str, 
                             min_val: float = None, max_val: float = None) -> float:
        """验证数值输入
        Args:
            value: 输入值
            field_name: 字段名称
            min_val: 最小值
            max_val: 最大值
        Returns:
            float: 验证后的数值
        """
        try:
            num = float(value.strip().rstrip('%'))
            if min_val is not None and num < min_val:
                raise ValueError(f"{field_name}不能小于{min_val}")
            if max_val is not None and num > max_val:
                raise ValueError(f"{field_name}不能大于{max_val}")
            return num
        except ValueError:
            raise ValueError(f"请输入有效的{field_name}")

    def generate_risk_summary(self, risks: Dict[str, float]) -> str:
        """生成风险总结信息"""
        try:
            summary_parts = []
            
            # 统计各风险等级的模块数量
            high_risk = []
            medium_risk = []
            low_risk = []
            
            for module_id, risk in risks.items():
                module_name = self.business_modules[module_id]['name']
                if risk > self.risk_thresholds['medium']:
                    high_risk.append(module_name)
                elif risk > self.risk_thresholds['low']:
                    medium_risk.append(module_name)
                else:
                    low_risk.append(module_name)
            
            # 生成总结信息
            if high_risk:
                summary_parts.append("高风险模块：")
                summary_parts.append(f"共{len(high_risk)}个")
                summary_parts.append("分别是：" + "、".join(high_risk))
                summary_parts.append("\n建议：需要立即处理\n")
            
            if medium_risk:
                summary_parts.append("\n中风险模块：")
                summary_parts.append(f"共{len(medium_risk)}个")
                summary_parts.append("分别是：" + "、".join(medium_risk))
                summary_parts.append("\n建议：需要持续监控\n")
            
            if low_risk:
                summary_parts.append("\n低风险模块：")
                summary_parts.append(f"共{len(low_risk)}个")
                summary_parts.append("分别是：" + "、".join(low_risk))
                summary_parts.append("\n状态：风险可控")
            
            return "".join(summary_parts)
            
        except Exception as e:
            print(f"生成风险总结失败: {str(e)}")
            return "风险总结生成失败"

    def run(self):
        """运行程序"""
        self.root.mainloop()

    def upgrade_requirements_file(self):
        """升级需求数据文件格式"""
        try:
            if os.path.exists('requirements.json'):
                # 读取现有数据
                with open('requirements.json', 'r', encoding='utf-8') as f:
                    old_data = json.load(f)
                
                # 检查是否需要升级（检查第一个记录是否已经有 id 字段）
                if old_data and isinstance(old_data, dict):
                    first_req = next(iter(old_data.values()), {})
                    if 'id' in first_req:  # 如果已经有 id 字段，说明已经是新格式
                        return True
                
                # 转换数据格式
                new_data = {}
                for old_req in old_data.values():
                    # 生成新的内部ID
                    internal_id = datetime.now().strftime("%Y%m%d%H%M%S")
                    time.sleep(0.001)  # 确保每个ID唯一
                    new_data[internal_id] = {
                        'id': internal_id,
                        'req_number': old_req.get('req_id', ''),  # 原req_id作为新的req_number
                        'title': old_req.get('title', ''),
                        'description': old_req.get('description', ''),
                        'version': old_req.get('version', ''),
                        'tester': old_req.get('tester', ''),
                        'risk': old_req.get('risk', 0.0)
                    }
                
                # 创建备份文件名（带时间戳）
                backup_time = datetime.now().strftime("%Y%m%d%H%M%S")
                backup_file = f'requirements_{backup_time}.json.bak'
                
                # 备份原文件
                if os.path.exists('requirements.json'):
                    shutil.copy2('requirements.json', backup_file)
                
                # 保存新格式数据
                with open('requirements.json', 'w', encoding='utf-8') as f:
                    json.dump(new_data, f, ensure_ascii=False, indent=2)
                
                return True
                
            return True  # 如果文件不存在，也返回True
            
        except Exception as e:
            messagebox.showerror("错误", f"升级需求数据文件失败: {str(e)}")
            return False

    def refresh_config_panel(self):
        """刷新配置面板"""
        try:
            print("\n[DEBUG] === 开始刷新配置面板 ===")
            
            # 清空树形视图
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 确保current_requirement存在且有效
            if not self.current_requirement:
                if hasattr(self, 'current_req_label'):
                    self.current_req_label.config(text="当前需求: 无")
                print("[DEBUG] 没有当前需求")
                return
            
            print(f"[DEBUG] 当前需求: {self.current_requirement['req_number']}")
            
            # 更新需求标签
            if hasattr(self, 'current_req_label'):
                self.current_req_label.config(
                    text=f"当前需求: {self.current_requirement['req_number']} - {self.current_requirement['title']}")
                print(f"[DEBUG] 配置面板需求标签已更新: {self.current_requirement['req_number']}")
            
            # 遍历所有模块，查找包含当前需求功能点的模块
            for module_id, module_info in self.business_modules.items():
                has_requirement_functions = False
                
                # 检查模块下的功能点
                if 'functions' in module_info:
                    for func_id, func_info in module_info['functions'].items():
                        if func_info.get('requirement_id') == self.current_requirement['id']:
                            has_requirement_functions = True
                            break
                
                # 如果模块包含当前需求的功能点，则显示该模块
                if has_requirement_functions:
                    print(f"[DEBUG] 找到相关模块: {module_id}")
                    
                    # 插入模块
                    values = [
                        module_info.get('name', ''),
                        f"{module_info.get('weight', 0):.3f}",  # 修改：直接显示小数，不乘100
                        '', '', '', '', '', '', '', '', '', '', '', '', ''
                    ]
                    self.tree.insert("", "end", module_id, text=module_id, values=values)
                    
                    # 插入该模块下属于当前需求的功能点
                    for func_id, func_info in module_info['functions'].items():
                        if func_info.get('requirement_id') == self.current_requirement['id']:
                            # 计算各种覆盖率
                            case_coverage = (func_info.get('executed_cases', 0) / func_info.get('total_cases', 1) * 100) if func_info.get('total_cases', 0) > 0 else 0
                            branch_coverage = (func_info.get('branch_covered', 0) / func_info.get('branch_total', 1) * 100) if func_info.get('branch_total', 0) > 0 else 0
                            business_coverage = (func_info.get('business_covered', 0) / func_info.get('business_cases', 1) * 100) if func_info.get('business_cases', 0) > 0 else 0
                            
                            values = [
                                func_info.get('name', ''),
                                f"{func_info.get('weight', 0):.1f}",
                                str(func_info.get('total_cases', 0)),
                                str(func_info.get('executed_cases', 0)),
                                f"{case_coverage:.1f}",
                                str(func_info.get('w1', 33)),
                                str(func_info.get('branch_total', 0)),
                                str(func_info.get('branch_covered', 0)),
                                f"{branch_coverage:.1f}",
                                str(func_info.get('w2', 33)),
                                str(func_info.get('business_cases', 0)),
                                str(func_info.get('business_covered', 0)),
                                f"{business_coverage:.1f}",
                                str(func_info.get('w3', 34)),
                                f"{func_info.get('risk', 0)*100:.1f}"
                            ]
                            self.tree.insert(module_id, "end", func_id, text=func_id, values=values)
                            print(f"[DEBUG] 添加功能点: {func_id}")
            
            print("[DEBUG] 配置面板刷新完成")
            
        except Exception as e:
            print(f"[ERROR] 刷新配置面板失败: {str(e)}")
            traceback.print_exc()

    def mark_associated_items(self):
        """标记已关联的模块和功能点"""
        try:
            for module_id, module_info in self.current_requirement.get('modules', {}).items():
                # 找到对应的模块项
                module_item = self.tree.item(module_id)
                if module_item:
                    # 可以通过改变样式或添加标记来显示关联状态
                    self.tree.item(module_id, tags=('associated',))
                    
                    # 标记关联的功能点
                    for func_id in module_info.get('functions', {}):
                        self.tree.item(func_id, tags=('associated',))
            
            # 定义关联项的显示样式
            self.tree.tag_configure('associated', background='#E8F0FE')
            
        except Exception as e:
            print(f"标记关联项时出错: {str(e)}")

    def update_requirement_risk(self, requirement):
        """更新需求的风险度"""
        try:
            if not requirement or not requirement.get('modules'):
                return
                
            total_risk = 0
            total_weight = 0
            
            # 计算每个模块的风险度
            for module_id, module_info in requirement['modules'].items():
                module_risk = 0
                module_weight = 0
                
                # 计算每个功能点的风险度
                for func_id, func_info in module_info.get('functions', {}).items():
                    if func_id in self.business_modules.get(module_id, {}).get('functions', {}):
                        current_func = self.business_modules[module_id]['functions'][func_id]
                        risk = self.calculate_function_risk(current_func)
                        weight = current_func.get('weight', 0)
                        module_risk += risk * weight
                        module_weight += weight
                
                if module_weight > 0:
                    total_risk += module_risk
                    total_weight += module_weight
            
            # 计算总体风险度
            if total_weight > 0:
                requirement['risk'] = total_risk / total_weight
                
                # 更新风险可视化看板
                self.update_risk_summary(requirement)
                self.update_risk_heatmap(requirement)
                self.update_risk_details(requirement)
                
                # 保存需求数据
                self.save_requirements()
                
        except Exception as e:
            messagebox.showerror("错误", f"更新需求风险度失败: {str(e)}")

    def refresh_risk_assessment(self):
        """刷新风险评估"""
        try:
            # 获取当前选中的需求
            selected = self.req_tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请先选择一个需求")
                return
                
            internal_id = selected[0]
            requirement = self.requirements.get(internal_id)
            
            if not requirement:
                return
                
            # 更新需求风险度
            self.update_requirement_risk(requirement)
            
            # 刷新需求列表显示
            self.refresh_requirement_list()
            
            messagebox.showinfo("成功", "风险评估已更新")
            
        except Exception as e:
            messagebox.showerror("错误", f"刷新风险评估失败: {str(e)}")

    def update_risk_heatmap(self, requirement):
        """更新模块风险热力图"""
        try:
            print("\n[DEBUG] === 开始更新热力图 ===")
            print(f"[DEBUG] 当前需求ID: {self.current_requirement['id']}")
            print(f"[DEBUG] 当前需求编号: {self.current_requirement['req_number']}")
            
            # 清除现有图表
            self.risk_figure.clear()
            
            # 提取当前需求相关的模块风险数据
            module_names = []
            module_risks = []
            
            # 遍历所有模块，检查是否包含当前需求的功能点
            for module_id, module_info in self.business_modules.items():
                has_requirement_functions = False
                total_risk = 0
                total_weight = 0
                
                # 检查模块下的功能点
                for func_id, func_info in module_info.get('functions', {}).items():
                    if func_info.get('requirement_id') == self.current_requirement['id']:
                        has_requirement_functions = True
                        risk = func_info.get('risk', 0)
                        weight = func_info.get('weight', 0)
                        total_risk += risk * weight
                        total_weight += weight
                
                # 如果模块包含当前需求的功能点，则添加到显示列表
                if has_requirement_functions:
                    print(f"[DEBUG] 找到相关模块: {module_id}")
                    module_names.append(module_info.get('name', module_id))
                    module_risk = total_risk / total_weight if total_weight > 0 else 0
                    module_risks.append(module_risk)
                    print(f"[DEBUG] 模块 {module_id} 风险度: {module_risk:.2%}")
            
            print(f"[DEBUG] 找到的相关模块数量: {len(module_names)}")
            print(f"[DEBUG] 模块名称列表: {module_names}")
            print(f"[DEBUG] 模块风险列表: {module_risks}")
            
            if not module_names:
                print("[DEBUG] 没有找到当前需求相关的模块")
                return
                
            # 计算矩阵大小
            num_modules = len(module_names)
            size = int(np.ceil(np.sqrt(num_modules)))
            risk_matrix = np.zeros((size, size))
            name_matrix = np.empty((size, size), dtype=object)
            
            # 填充矩阵
            for i, (name, risk) in enumerate(zip(module_names, module_risks)):
                row = i // size
                col = i % size
                risk_matrix[row, col] = risk
                name_matrix[row, col] = name
                print(f"[DEBUG] 放置模块 {name} 在位置 ({row}, {col}), 风险度: {risk:.2%}")
            
            # 创建热力图
            ax = self.risk_figure.add_subplot(111)
            im = ax.imshow(risk_matrix, 
                          cmap='RdYlGn_r',
                          vmin=0, vmax=1,
                          aspect='equal')
            
            # 在每个单元格中心显示模块名称和风险值
            for i in range(size):
                for j in range(size):
                    if name_matrix[i, j]:
                        risk = risk_matrix[i, j]
                        name = name_matrix[i, j]
                        
                        # 根据风险值选择文本颜色
                        color = 'white' if risk > 0.5 else 'black'
                        
                        # 计算合适的字体大小
                        fontsize = min(10, 120 / max(len(name), 1))
                        
                        # 创建文本框
                        text = f"{name}\n{risk:.1%}"
                        bbox_props = dict(
                            boxstyle='round,pad=0.3',
                            fc='white' if risk > 0.5 else 'none',
                            ec='gray',
                            alpha=0.7
                        )
                        
                        # 添加文本
                        ax.text(j, i, text,
                               ha='center', va='center',
                               color=color,
                               fontsize=fontsize,
                               fontweight='bold',
                               bbox=bbox_props,
                               fontname='Microsoft YaHei')
            
            # 移除坐标轴
            ax.set_xticks([])
            ax.set_yticks([])
            
            # 添加网格线
            for edge in range(size + 1):
                ax.axhline(y=edge-0.5, color='white', linewidth=1)
                ax.axvline(x=edge-0.5, color='white', linewidth=1)
            
            # 设置标题
            title = f"需求 {self.current_requirement['req_number']} 模块风险热力图"
            ax.set_title(title, 
                        fontsize=12,
                        pad=15,
                        fontweight='bold',
                        fontname='Microsoft YaHei',
                        y=1.02)
            
            # 添加颜色条
            cbar = self.risk_figure.colorbar(im)
            cbar.set_label('风险度', 
                          fontsize=10,
                          fontweight='bold',
                          fontname='Microsoft YaHei')
            cbar.ax.tick_params(labelsize=8)
            
            # 调整布局
            self.risk_figure.tight_layout(rect=[0, 0, 1, 0.95])
            
            # 刷新画布
            self.canvas.draw()
            print("[DEBUG] 热力图更新完成")
            
        except Exception as e:
            print(f"[ERROR] 更新热力图失败: {str(e)}")
            traceback.print_exc()
            messagebox.showerror("错误", f"更新热力图失败: {str(e)}")

    def save_module(self):
        """保存子模块信息"""
        try:
            # 获取输入值
            module_name = self.module_name_var.get().strip()
            module_weight = self.module_weight_var.get().strip()
            
            # 验证必填字段
            if not module_name:
                messagebox.showwarning("警告", "请输入子模块名称")
                return
                
            if not module_weight:
                messagebox.showwarning("警告", "请输入子模块权重")
                return
                
            try:
                module_weight = float(module_weight)
                if not (0 <= module_weight <= 100):
                    messagebox.showwarning("警告", "权重必须在0-100之间")
                    return
            except ValueError:
                messagebox.showwarning("警告", "权重必须是数字")
                return
            
            # 获取选中的模块
            selected = self.tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请先选择一个模块")
                return
                
            module_id = selected[0]
            
            # 更新模块信息
            if module_id not in self.business_modules:
                self.business_modules[module_id] = {
                    'name': module_name,
                    'weight': module_weight,
                    'functions': {}
                }
            else:
                self.business_modules[module_id].update({
                    'name': module_name,
                    'weight': module_weight
                })
            
            # 保存配置
            self.save_config()
            
            # 刷新显示
            self.refresh_config_panel()
            
            messagebox.showinfo("成功", "子模块信息已保存")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存子模块失败: {str(e)}")

    def update_risk_dashboard(self, event=None):  # 添加可选的event参数
        """更新风险看板"""
        try:
            print("\n[DEBUG] === 开始更新风险看板 ===")
            print(f"[DEBUG] 调用方式: {'事件触发' if event else '直接调用'}")
            
            if not self.current_requirement:
                print("[DEBUG] 没有选中的需求")
                return
                
            # 更新热力图
            if hasattr(self, 'heatmap_frame'):
                self.update_risk_heatmap(self.current_requirement)
            
            # 更新风险总结
            self.update_risk_summary(self.current_requirement)
            
            # 更新风险详情
            self.update_risk_details()  # 不传递参数
            
            print("[DEBUG] 风险看板更新完成")
            
        except Exception as e:
            print(f"[ERROR] 更新风险看板失败: {str(e)}")
            print(f"[ERROR] 错误类型: {type(e).__name__}")
            print(f"[ERROR] 错误详情:")
            traceback.print_exc()

    def load_submodule_data(self, module_id):
        """加载子模块数据"""
        if module_id not in self.business_modules:
            return
            
        module_info = self.business_modules[module_id]
        self.submodule_name_var.set(module_info.get('name', ''))
        self.submodule_weight_var.set(str(module_info.get('weight', 0)))  # 直接显示小数

class RequirementDialog:
    def __init__(self, parent, title, requirement=None):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.resizable(False, False)  # 禁止调整大小
        self.result = None
        
        # 如果是编辑模式，使用传入的需求数据
        self.requirement = requirement or {
            'id': str(uuid.uuid4()),  # 生成唯一ID
            'req_number': '',
            'title': '',
            'description': '',
            'version': '',
            'tester': '',
            'risk': 0.0,
            'modules': {}  # 初始化空的模块字典
        }
        
        # 设置对话框大小
        dialog_width = 500
        dialog_height = 400
        
        # 获取主窗口位置和大小
        parent_x = parent.winfo_x()
        parent_y = parent.winfo_y()
        parent_width = parent.winfo_width()
        parent_height = parent.winfo_height()
        
        # 计算居中位置
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        # 设置对话框位置和大小
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
        self.create_widgets()
        
        # 设置模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 确保对话框完全创建后再显示
        self.dialog.update_idletasks()
        
        # 如果位置计算有误，重新调整到屏幕中心
        if x < 0 or y < 0:
            screen_width = self.dialog.winfo_screenwidth()
            screen_height = self.dialog.winfo_screenheight()
            x = (screen_width - dialog_width) // 2
            y = (screen_height - dialog_height) // 2
            self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
        parent.wait_window(self.dialog)

    def create_widgets(self):
        # 创建输入框架
        frame = ttk.Frame(self.dialog, padding="10")
        frame.pack(fill='both', expand=True)
        
        # 需求编号
        ttk.Label(frame, text="需求编号:").grid(row=0, column=0, sticky='w', pady=5)
        self.req_number_var = tk.StringVar(value=self.requirement.get('req_number', ''))
        ttk.Entry(frame, textvariable=self.req_number_var).grid(row=0, column=1, sticky='ew', pady=5)
        
        # 需求标题
        ttk.Label(frame, text="需求标题:").grid(row=1, column=0, sticky='w', pady=5)
        self.title_var = tk.StringVar(value=self.requirement.get('title', ''))
        ttk.Entry(frame, textvariable=self.title_var).grid(row=1, column=1, sticky='ew', pady=5)
        
        # 需求描述
        ttk.Label(frame, text="需求描述:").grid(row=2, column=0, sticky='w', pady=5)
        self.description_text = tk.Text(frame, height=5, width=40)
        self.description_text.grid(row=2, column=1, sticky='ew', pady=5)
        self.description_text.insert('1.0', self.requirement.get('description', ''))
        
        # 所属版本
        ttk.Label(frame, text="所属版本:").grid(row=3, column=0, sticky='w', pady=5)
        self.version_var = tk.StringVar(value=self.requirement.get('version', ''))
        ttk.Entry(frame, textvariable=self.version_var).grid(row=3, column=1, sticky='ew', pady=5)
        
        # 测试负责人
        ttk.Label(frame, text="测试负责人:").grid(row=4, column=0, sticky='w', pady=5)
        self.tester_var = tk.StringVar(value=self.requirement.get('tester', ''))
        ttk.Entry(frame, textvariable=self.tester_var).grid(row=4, column=1, sticky='ew', pady=5)
        
        # 按钮区域
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self.save).pack(side='left', padx=5)
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side='left', padx=5)
        
        # 配置网格布局
        frame.columnconfigure(1, weight=1)

    def save(self):
        """保存需求信息"""
        try:
            # 验证必填字段
            req_number = self.req_number_var.get().strip()
            title = self.title_var.get().strip()
            
            if not req_number or not title:
                messagebox.showerror("错误", "需求编号和标题为必填项")
                return
            
            # 更新需求信息
            self.requirement.update({
                'req_number': req_number,
                'title': title,
                'description': self.description_text.get('1.0', 'end-1c').strip(),
                'version': self.version_var.get().strip(),
                'tester': self.tester_var.get().strip()
            })
            
            self.result = self.requirement
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"保存需求失败: {str(e)}")

    def cancel(self):
        """取消操作"""
        self.dialog.destroy()

# 添加一个新的确认对话框类
class ConfirmDialog:
    def __init__(self, parent, title, message):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.resizable(False, False)
        self.result = False
        
        # 设置对话框大小 - 增加宽度
        dialog_width = 450  # 从400改为450
        dialog_height = 180  # 从150改为180，给按钮留更多空间
        
        # 获取主窗口位置和大小
        parent_x = parent.winfo_x()
        parent_y = parent.winfo_y()
        parent_width = parent.winfo_width()
        parent_height = parent.winfo_height()
        
        # 计算居中位置
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        # 设置对话框位置和大小
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
        # 创建组件
        frame = ttk.Frame(self.dialog, padding="20")
        frame.pack(fill='both', expand=True)
        
        # 消息标签 - 增加换行宽度
        ttk.Label(frame, text=message, wraplength=400).pack(pady=(0, 20))  # 从350改为400
        
        # 按钮区域 - 增加按钮间距和宽度
        button_frame = ttk.Frame(frame)
        button_frame.pack(pady=(0, 10))
        
        ttk.Button(button_frame, text="确定", command=self.confirm, width=12).pack(side='left', padx=10)  # 增加按钮宽度和间距
        ttk.Button(button_frame, text="取消", command=self.cancel, width=12).pack(side='left', padx=10)
        
        # 设置模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 确保对话框完全创建后再显示
        self.dialog.update_idletasks()
        
        # 如果位置计算有误，重新调整到屏幕中心
        if x < 0 or y < 0:
            screen_width = self.dialog.winfo_screenwidth()
            screen_height = self.dialog.winfo_screenheight()
            x = (screen_width - dialog_width) // 2
            y = (screen_height - dialog_height) // 2
            self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
        parent.wait_window(self.dialog)
    
    def confirm(self):
        self.result = True
        self.dialog.destroy()
    
    def cancel(self):
        self.dialog.destroy()

if __name__ == "__main__":
    app = QualityRiskAssessment()
    app.run()
