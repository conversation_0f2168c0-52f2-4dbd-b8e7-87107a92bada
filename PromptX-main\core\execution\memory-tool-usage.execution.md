<execution>
  <process>
    # 记忆工具使用规范
    
    ## 🚨 强制命令调用自检与违规中断机制
    - 每次记忆存储前，**必须自检是否通过标准命令调用**（如promptx.js remember），如未走命令，自动中断并发出警告。
    - 检测到edit_file、write_file等直接写文件操作时，**立即中断流程并提示"记忆存储只能用命令，禁止直接写文件"**。
    - 所有记忆存储分支的终点都必须是命令调用，而不是文件写入。
    
    ```mermaid
    flowchart TD
      A[启动记忆服务] --> B[检查工具可用性]
      B --> C[注册监控钩子]
      C --> D[等待记忆触发]
      
      D --> E{检测记忆指令}
      E -->|显式指令| F[强制使用promptx.js]
      E -->|隐式指令| G[评估并使用promptx.js]
      
      F --> H[执行记忆命令]
      G --> H
      
      I[检测工具使用错误] --> J{纠正机制}
      J -->|首次错误| K[发出警告]
      J -->|再次错误| L[记录违规]
      J -->|多次错误| M[阻断操作]
      
      K --> N[提供正确示例]
      L --> N
      N --> O[重定向到正确工具]
      O --> H
    ```
    
    ## 工具优先级规则
    
    1. **命令优先级设置**
       - promptx.js remember 命令具有最高优先级
       - 任何记忆存储操作必须使用此命令
       - 其他工具调用在记忆场景下将被自动拦截
    
    2. **命令格式标准化**
       - 标准格式：`node PromptX/promptx.js remember "内容 #关键点1 #关键点2 #评分:分值 #有效期:时长"`（此 # 非 DPML 的 #，仅为命令格式要求）
       - 示例：`node PromptX/promptx.js remember "用户偏好简洁界面 #用户偏好 #设置 #评分:8 #有效期:长期"`
       - 所有参数必须按照规定格式提供，不得省略必要参数
    
    3. **监控与拦截机制**
       - 实时监控所有记忆相关操作
       - 自动拦截非 promptx.js 的工具调用
       - 自动转换为正确的命令格式
       - 记录所有拦截和转换操作
       
    4. **违规处理流程**
       ```
       1. 首次违规：发出警告，提供正确使用示例
       2. 二次违规：记录违规，强制转换为正确命令
       3. 三次违规：阻断操作，要求人工确认
       4. 多次违规：暂时禁用自动记忆功能
       ```
       
    5. **工具调用初始化检查**
       - 系统启动时检查 promptx.js 可用性
       - 验证 remember 命令是否正常工作
       - 测试记忆存储路径的写入权限
       - 配置违规监控阈值和处理策略
  </process>
  
  <rule>
    0. **记忆存储自检规则**
       - 每次记忆存储前，必须检查是否为标准命令调用（promptx.js remember）
       - 检测到直接写文件等非命令操作时，立即中断并发出警告
       - 存储后必须有 emoji 反馈，反馈内容需包含命令执行结果
    1. **工具选择强制规则**
       - 记忆操作**必须且只能**使用 promptx.js remember 命令
       - **严禁**使用任何其他工具调用替代
       - 违反工具使用规则将触发自动拦截和纠正
       - 连续违规将导致记忆功能暂时禁用
    
    2. **命令格式强制规则**
       - 命令格式必须为：`node promptx.js remember "内容 #关键点1 #关键点2 #评分:分值 #有效期:时长"`（此 # 非 DPML 的 #，仅为命令格式要求）
       - 评分参数必须提供
       - 标签必须遵循规定的标签体系
       - 有效期必须明确指定
    
    3. **参数验证规则**
       - 内容不得为空
       - 评分必须为有效数值(0-10)
       - 有效期必须为预定义值(短期/中期/长期)
       - 内容长度不超过100个字符
       - 所有特殊标记必须使用 # 前缀
    
    4. **工具监控规则**
       - 记忆相关操作必须被实时监控
       - 监控程序不得干扰正常对话流程
       - 所有违规记录必须保存到审计日志
       - 违规统计数据必须定期清零
  </rule>
  
  <constraint>
    0. **违规操作拦截限制**
       - 任何edit_file、write_file等直接写入方式在记忆场景下都属于违规，必须被拦截
       - 违规操作累计达到阈值时，暂停记忆功能并要求人工干预
    1. **工具使用技术限制**
       - promptx.js 依赖于运行环境
       - 监控机制受系统性能限制
       - 拦截和转换操作可能产生轻微延迟
       - 并发记忆操作存在资源竞争
    
    2. **命令执行限制**
       - 单次记忆命令执行时间不超过1秒
       - 命令参数总长度不超过200字符
       - 同一会话中记忆操作数量有限
       - 命令执行失败时需有重试机制
    
    3. **监控性能限制**
       - 监控开销不超过10ms/次
       - 拦截处理不超过50ms/次
       - 审计日志大小不超过2MB
       - 违规计数器精度为会话级别
  </constraint>
  
  <guideline>
    0. **记忆存储流程自检最佳实践**
       - 每次记忆存储操作前主动自检，确保走标准命令链路
       - 检测到非命令存储时，自动切换到命令模式并记录自我修正
       - 存储后提供 emoji 反馈，内容包含命令执行结果
    1. **工具选择最佳实践**
       - 始终首选 promptx.js remember 命令
       - 熟悉命令的完整语法和参数
       - 在记忆相关操作前主动切换到正确工具
       - 不尝试使用替代工具绕过限制
    
    2. **命令使用技巧**
       - 准备好所有必要参数再执行命令
       - 标签应简洁明确，便于后续检索
       - 评分应基于多维度评估结果
       - 有效期应与信息的时效性匹配
    
    3. **错误处理建议**
       - 命令执行失败时检查参数格式
       - 权限问题时检查存储路径设置
       - 内容过长时进行适当分割
       - 遇到违规警告时立即改正
    
    4. **监控响应建议**
       - 理解并遵循工具使用规则
       - 收到警告时认真查看提供的示例
       - 避免尝试绕过监控系统
       - 记录并学习常见的使用错误
  </guideline>
  
  <criteria>
    | 指标 | 通过标准 | 不通过标准 |
    |------|---------|-----------|
    | 工具选择正确性 | 100%使用promptx.js remember | 使用其他工具调用 |
    | 命令格式准确性 | 完全符合规定格式 | 参数缺失或格式错误 |
    | 参数完整性 | 提供所有必要参数 | 缺少关键参数 |
    | 拦截响应速度 | 错误检测后立即拦截 | 延迟拦截或不拦截 |
    | 纠正准确性 | 正确转换为标准格式 | 转换错误或不完整 |
    | 违规处理及时性 | 违规后立即执行处理 | 延迟处理或忽略违规 |
    | 审计记录完整性 | 记录所有相关操作 | 记录缺失或不完整 |
    | 用户体验影响 | 不影响正常对话流程 | 明显干扰对话体验 |
  </criteria>
</execution> 