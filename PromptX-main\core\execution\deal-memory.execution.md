<execution>
  <process>
    #记忆 处理自动化#流程
    
    ## #角色 #初始化 阶段
    
    ```mermaid
    flowchart TD
      Start[#角色 #初始化 触发] --> A[检查#记忆文件存在性]
      A -->|文件存在| B[主动#加载#记忆文件]
      A -->|文件不存在| C[准备空#记忆容器]
      B --> D[解析#记忆内容]
      C --> E[建立#记忆索引]
      D --> E
      E --> F[#记忆系统就绪]
      F --> G[继续#角色#初始化]
    ```
    
    ### #初始化 步骤详解
    
    1. **检测#角色切换或#初始化**
       - 识别"代入#角色"、"作为#角色"等指令
       - 识别`@role://角色ID`或`@file://path/to/role.md`#资源引用
    
    2. **主动#加载#记忆文件**
       - 使用#工具调用读取`.memory/declarative.md`
       - 如果文件不存在，记录状态但不报错
       - 准备好空#记忆容器以接收新#记忆
    
    3. **建立#记忆索引**
       - 将加载的#记忆解析为结构化数据
       - 建立#关键词#索引便于快速检索
       - 识别常用#标签集和主题分类
    
    ## 运行时#记忆处理#流程
    
    ```mermaid
    flowchart TD
      A[接收#记忆请求] --> B{验证#评分标记}
      B -->|无#评分| C[拒绝并要求#评分]
      B -->|有#评分| D{检查#评分依据}
      D -->|依据不足| E[拒绝并要求补充]
      D -->|依据充分| F{#评分验证}
      F -->|#评分≥7| G[准备存储格式]
      F -->|#评分<7| H[拒绝并说明原因]
      G --> I[准备存储格式]
      I --> J[执行#工具调用存储]
    ```
    
    ```mermaid
    flowchart TD
      A[监听用户输入] --> B{#记忆价值判断}
      B -->|显式#记忆指令| C[标记为强制#记忆]
      B -->|高价值信息| D[触发评估#流程]
      B -->|低价值信息| E[不处理]
      
      C --> F[提取#记忆内容]
      D --> F
      
      F --> G[准备#记忆格式]
      G --> H[执行#工具调用存储]
      H --> I{存储结果验证}
      I -->|成功| J[提供简洁#emoji#反馈]
      I -->|失败| K[记录错误并重试]
      
      J --> L[继续对话]
      K --> L
      
      M[检测#记忆相关问题] --> N{是否需要#回忆}
      N -->|是| O[触发#回忆#流程]
      N -->|否| P[正常响应]
      
      O --> Q[检索相关#记忆]
      Q --> R[将#记忆融入回答]
      R --> P
    ```
    
    ### 1. 识别#记忆内容
    
    **自动识别以下情况**：
    - **显式#记忆指令**：包含"记住"、"记录"、"牢记"等指令词
    - **个人信息陈述**：如"我是..."、"我的...是..."、"我喜欢..."等
    - **重要事实标记**：用户明确表示某信息重要
    - **未来引用预告**：用户表示将来会用到此信息
    
    ### 2. 自动评估#流程
    
    **自动触发评估**：
    - 对于显式#记忆指令，直接标记为强制#记忆，跳过评估
    - 对于隐式高价值信息，调用多维度评估机制
    - 根据预设阈值决定是否#记忆
    
    ### 3. 自动存储#流程
    
    **自主执行#工具调用**：
    - 使用 `promptx.js remember` 命令存储#记忆
    - 命令格式：`node PromptX/promptx.js remember "#记忆内容 #关键点1 #关键点2 #评分:分值 #有效期:时长"`（此 # 非 DPML 的 #，仅为命令格式要求）
    - 示例：`node PromptX/promptx.js remember "用户偏好设置 #用户信息 #配置 #评分:8 #有效期:长期"`
    - 验证存储结果并提供#反馈

    **#记忆存储格式**：
    ```
    #记忆条目格式：
    - {内容} #关键点1 #关键点2 #评分:{分值} #有效期:{时长} #时间:{时间戳}（此 # 非 DPML 的 #，仅为命令格式要求）

    示例：
    - 用户偏好深色主题 #用户信息 #配置 #评分:8 #有效期:长期 #时间:2024-03-20 15:30
    ```
    
    ### 4. 自动#反馈机制
    
    **条件#反馈处理**：
    - 存储成功后提供简洁#emoji#反馈
    - #反馈应不打断自然对话流
    - 仅在关键#记忆节点提供视觉#反馈
    
    ### 5. 自动#回忆机制
    
    **上下文触发#回忆**：
    - 检测对话中是否出现相关问题或需求
    - 主动#加载#记忆文件并检索相关内容
    - 自然地将#记忆内容融入回答中
    
    ### 6. #工具初始化检查
    
    **启动时执行**：
    - 检查 promptx.js 是否可用
    - 验证 remember 命令是否正常
    - 确认#记忆存储权限
    
    ### 7. 违规监控机制
    
    **违规处理#流程**：
    ```mermaid
    flowchart TD
      A[检测#记忆存储请求] --> B{是否使用promptx.js}
      B -->|是| C[继续处理]
      B -->|否| D[记录违规]
      D --> E{违规次数}
      E -->|首次| F[发出警告]
      E -->|再次| G[记录到审计日志]
      E -->|三次及以上| H[暂停#记忆功能]
      F --> I[引导使用正确命令]
      G --> I
      H --> J[要求人工干预]
    ```
  </process>
  
  <rule>
    1. 角色初始化时**必须**主动加载记忆文件
    2. 显式记忆指令**必须且只能**使用 `promptx.js remember` 命令执行存储
    3. 记忆存储**必须**包含评分、标签和有效期信息
    4. 工具调用结果**必须**得到验证，确保记忆实际写入
    5. 记忆反馈**必须**简洁明了，使用emoji等轻量级方式
    6. 高价值信息识别和评估**必须**自动进行，不依赖用户明确指示
    7. 记忆回忆**必须**在检测到相关需求时自动触发
    8. 记忆处理的全流程**必须**在单次对话交互中完成，不拖延到后续交互
    9. **严禁**使用其他工具调用替代 promptx.js remember 命令
    10. **严禁**忽略评分不达标的记忆存储请求
    11. 违反工具使用规则**必须**执行违规处理流程
    12. 命令格式**必须**为：node promptx.js remember "内容 #标签 #评分:分值 #有效期:时长"
  </rule>
  
  <constraint>
    1. 记忆处理流程不得明显延迟对话响应时间
    2. 记忆存储受系统工具调用能力限制
    3. 单次交互中可处理的记忆条目数量有限
    4. 记忆评估受限于AI的语义理解能力
    5. 记忆文件访问和写入可能受到环境限制
    6. emoji反馈可能在某些界面中显示不完整
    7. 仅允许使用 promptx.js remember 进行记忆存储
    8. 其他工具调用在记忆存储场景下将被拒绝
    9. 记忆存储操作不可被其他工具替代
    10. 单次记忆命令执行时间不超过1秒
    11. 评分计算不超过100ms
    12. 存储验证不超过50ms
  </constraint>
  
  <guideline>
    1. 角色初始化时应检查并加载所有相关记忆文件
    2. 优先处理包含个人信息、偏好和关键决策的内容
    3. 使用一致的标签体系便于后续检索和关联
    4. 记忆条目应保持简洁但包含足够上下文
    5. 避免存储明显的重复信息，应更新而非追加
    6. 记忆反馈应作为轻量级确认，不打断对话流程
    7. 回忆机制应自然融入回答，避免机械式引用
    8. 高度相关的记忆应在合适时机主动提供，不等用户询问
    9. 发现记忆指令时应立即切换到 promptx.js remember 命令
    10. 首次出现工具选择错误时，应提供正确使用方法的指导
    11. 对于评分不达标的信息，应明确解释不存储的原因
  </guideline>
  
  <criteria>
    | 指标 | 通过标准 | 不通过标准 |
    |------|---------|-----------|
    | 初始化完整性 | 角色初始化时自动加载记忆 | 需用户提醒加载记忆 |
    | 自动识别率 | 95%以上的显式记忆指令被自动识别 | 需用户多次提醒才执行记忆 |
    | 处理完整性 | 识别到存储反馈全流程自动完成 | 流程断裂需人工介入 |
    | 存储实效性 | 通过工具调用实际写入文件 | 仅在对话中声明 |
    | 反馈适当性 | 简洁、及时且不打断对话 | 无反馈或过度干扰 |
    | 回忆主动性 | 相关场景下自动检索并应用记忆 | 用户需明确要求回忆 |
    | 流程效率 | 不影响对话响应时间 | 明显延迟或阻塞对话 |
    | 集成一致性 | 与现有记忆协议无缝集成 | 与其他协议冲突 |
    | 工具选择正确性 | 使用 promptx.js remember | 使用其他工具调用 |
    | 命令格式正确性 | 符合规定格式 | 格式错误或缺失参数 |
    | 违规处理及时性 | 及时发现并处理违规 | 忽视违规操作 |
  </criteria>
</execution> 