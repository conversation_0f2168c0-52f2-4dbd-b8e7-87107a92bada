# ScoreManager类的单元测试
import unittest
import os
import json
import time
from score_manager import ScoreManager
from config import GAME_CONFIG

class TestScoreManager(unittest.TestCase):
    """ScoreManager类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        # 删除可能存在的高分文件
        if os.path.exists('high_score.json'):
            os.remove('high_score.json')
        
        self.score_manager = ScoreManager()
    
    def tearDown(self):
        """测试后的清理"""
        # 清理测试文件
        if os.path.exists('high_score.json'):
            os.remove('high_score.json')
    
    def test_initialization(self):
        """测试初始化"""
        sm = ScoreManager()
        
        # 检查初始值
        self.assertEqual(sm.get_current_score(), 0)
        self.assertEqual(sm.get_current_level(), 1)
        self.assertEqual(sm.get_lines_cleared(), 0)
        self.assertEqual(sm.get_total_pieces_placed(), 0)
        self.assertEqual(sm.get_high_score(), 0)
        
        # 检查配置
        self.assertIn(1, sm.base_scores)
        self.assertIn(4, sm.base_scores)
        self.assertGreater(sm.base_scores[4], sm.base_scores[1])  # Tetris分数更高
    
    def test_single_line_scoring(self):
        """测试单行消除分数"""
        earned = self.score_manager.add_line_score(1)
        
        # 单行在1级应该得到100分
        expected_score = 100 * 1  # base_score * level
        self.assertEqual(earned, expected_score)
        self.assertEqual(self.score_manager.get_current_score(), expected_score)
        self.assertEqual(self.score_manager.get_lines_cleared(), 1)
    
    def test_multiple_line_scoring(self):
        """测试多行消除分数"""
        # 测试双行消除
        earned_double = self.score_manager.add_line_score(2)
        expected_double = 300 * 1  # 双行基础分300，1级乘数
        self.assertEqual(earned_double, expected_double)
        
        # 测试四行消除（Tetris）
        earned_tetris = self.score_manager.add_line_score(4)
        expected_tetris = 800 * 1  # 四行基础分800，1级乘数
        self.assertEqual(earned_tetris, expected_tetris)
        
        # 总分数和行数检查
        total_expected = expected_double + expected_tetris
        self.assertEqual(self.score_manager.get_current_score(), total_expected)
        self.assertEqual(self.score_manager.get_lines_cleared(), 6)  # 2 + 4 行
    
    def test_level_progression(self):
        """测试等级提升"""
        lines_per_level = GAME_CONFIG['LINES_PER_LEVEL']
        
        # 消除足够的行来升级
        self.score_manager.add_line_score(lines_per_level)
        
        # 应该升到2级
        self.assertEqual(self.score_manager.get_current_level(), 2)
        
        # 检查升级历史记录
        self.assertEqual(len(self.score_manager.level_up_scores), 1)
        level_up_record = self.score_manager.level_up_scores[0]
        self.assertEqual(level_up_record['level'], 2)
        self.assertEqual(level_up_record['lines'], lines_per_level)
    
    def test_level_multiplier(self):
        """测试等级乘数效果"""
        lines_per_level = GAME_CONFIG['LINES_PER_LEVEL']
        
        # 先升到2级
        self.score_manager.add_line_score(lines_per_level)
        self.assertEqual(self.score_manager.get_current_level(), 2)
        
        # 在2级时消除单行
        earned = self.score_manager.add_line_score(1)
        expected = 100 * 2  # 基础分100 * 2级乘数
        self.assertEqual(earned, expected)
    
    def test_drop_scoring(self):
        """测试下落分数"""
        # 测试软下落
        soft_score = self.score_manager.add_drop_score(5, is_hard_drop=False)
        self.assertEqual(soft_score, 5 * 1)  # 5格 * 1分每格
        
        # 测试硬下落
        hard_score = self.score_manager.add_drop_score(3, is_hard_drop=True)
        self.assertEqual(hard_score, 3 * 2)  # 3格 * 2分每格
        
        # 总分数检查
        self.assertEqual(self.score_manager.get_current_score(), 5 + 6)
    
    def test_placement_bonus(self):
        """测试方块放置奖励"""
        initial_score = self.score_manager.get_current_score()
        initial_pieces = self.score_manager.get_total_pieces_placed()
        
        bonus = self.score_manager.add_placement_bonus()
        
        self.assertEqual(bonus, 10)  # 放置奖励10分
        self.assertEqual(self.score_manager.get_current_score(), initial_score + 10)
        self.assertEqual(self.score_manager.get_total_pieces_placed(), initial_pieces + 1)
    
    def test_fall_speed_calculation(self):
        """测试下落速度计算"""
        initial_speed = GAME_CONFIG['INITIAL_FALL_SPEED']
        rate = GAME_CONFIG['SPEED_INCREASE_RATE']
        
        # 1级的速度
        speed_level_1 = self.score_manager.get_fall_speed()
        self.assertEqual(speed_level_1, initial_speed)
        
        # 升到2级
        self.score_manager.add_line_score(GAME_CONFIG['LINES_PER_LEVEL'])
        speed_level_2 = self.score_manager.get_fall_speed()
        expected_speed_2 = int(initial_speed * rate)
        self.assertEqual(speed_level_2, expected_speed_2)
        
        # 确保有最小速度限制
        self.assertGreaterEqual(speed_level_2, 50)
    
    def test_lines_to_next_level(self):
        """测试距离下一级的行数计算"""
        lines_per_level = GAME_CONFIG['LINES_PER_LEVEL']
        
        # 初始状态
        lines_needed = self.score_manager.get_lines_to_next_level()
        self.assertEqual(lines_needed, lines_per_level)
        
        # 消除一些行
        self.score_manager.add_line_score(3)
        lines_needed = self.score_manager.get_lines_to_next_level()
        self.assertEqual(lines_needed, lines_per_level - 3)
        
        # 升级后
        self.score_manager.add_line_score(lines_per_level - 3)
        lines_needed = self.score_manager.get_lines_to_next_level()
        self.assertEqual(lines_needed, lines_per_level)  # 下一级需要的行数
    
    def test_performance_statistics(self):
        """测试性能统计"""
        # 添加一些数据
        self.score_manager.add_line_score(5)
        self.score_manager.add_placement_bonus()
        self.score_manager.add_placement_bonus()
        
        # 等待一小段时间以获得有意义的统计
        time.sleep(0.1)
        
        # 检查统计数据
        self.assertGreater(self.score_manager.get_game_time(), 0)
        self.assertGreaterEqual(self.score_manager.get_lines_per_minute(), 0)
        self.assertGreaterEqual(self.score_manager.get_pieces_per_minute(), 0)
    
    def test_score_breakdown(self):
        """测试分数详细信息"""
        self.score_manager.add_line_score(2)
        self.score_manager.add_placement_bonus()
        
        breakdown = self.score_manager.get_score_breakdown()
        
        # 检查包含的字段
        required_fields = [
            'total_score', 'level', 'lines_cleared', 'pieces_placed',
            'game_time', 'lines_per_minute', 'pieces_per_minute',
            'lines_to_next_level', 'level_up_history'
        ]
        
        for field in required_fields:
            self.assertIn(field, breakdown)
        
        # 检查数据一致性
        self.assertEqual(breakdown['total_score'], self.score_manager.get_current_score())
        self.assertEqual(breakdown['level'], self.score_manager.get_current_level())
        self.assertEqual(breakdown['lines_cleared'], self.score_manager.get_lines_cleared())
    
    def test_high_score_saving_and_loading(self):
        """测试高分保存和加载"""
        # 设置一个分数
        self.score_manager.add_line_score(10)
        initial_score = self.score_manager.get_current_score()
        
        # 保存高分
        saved = self.score_manager.save_high_score()
        self.assertTrue(saved)  # 应该保存成功，因为是新高分
        
        # 创建新的ScoreManager实例来测试加载
        new_sm = ScoreManager()
        self.assertEqual(new_sm.get_high_score(), initial_score)
        
        # 检查高分数据
        high_score_data = new_sm.get_high_score_data()
        self.assertEqual(high_score_data['score'], initial_score)
        self.assertIsNotNone(high_score_data['date'])
    
    def test_new_high_score_detection(self):
        """测试新高分检测"""
        # 初始状态不是新高分
        self.assertFalse(self.score_manager.is_new_high_score())
        
        # 添加分数后应该是新高分
        self.score_manager.add_line_score(1)
        self.assertTrue(self.score_manager.is_new_high_score())
    
    def test_grade_calculation(self):
        """测试等级评价计算"""
        # 初始等级应该是较低的
        initial_grade = self.score_manager.calculate_grade()
        self.assertIn(initial_grade, ['S', 'A', 'B', 'C', 'D'])
        
        # 添加大量分数和等级
        for _ in range(10):
            self.score_manager.add_line_score(4)  # Tetris
        
        # 等级应该提高
        improved_grade = self.score_manager.calculate_grade()
        self.assertIn(improved_grade, ['S', 'A', 'B', 'C', 'D'])
    
    def test_statistics_comprehensive(self):
        """测试综合统计信息"""
        # 添加各种数据
        self.score_manager.add_line_score(4)  # Tetris
        self.score_manager.add_drop_score(5, is_hard_drop=True)
        self.score_manager.add_placement_bonus()
        
        stats = self.score_manager.get_statistics()
        
        # 检查统计结构
        self.assertIn('current_session', stats)
        self.assertIn('high_score_record', stats)
        self.assertIn('performance_grade', stats)
        self.assertIn('is_new_record', stats)
        
        # 检查数据类型
        self.assertIsInstance(stats['current_session'], dict)
        self.assertIsInstance(stats['high_score_record'], dict)
        self.assertIsInstance(stats['performance_grade'], str)
        self.assertIsInstance(stats['is_new_record'], bool)
    
    def test_reset_functionality(self):
        """测试重置功能"""
        # 添加一些数据
        self.score_manager.add_line_score(5)
        self.score_manager.add_placement_bonus()
        original_high_score = self.score_manager.get_high_score()
        
        # 重置
        self.score_manager.reset()
        
        # 检查重置后的状态
        self.assertEqual(self.score_manager.get_current_score(), 0)
        self.assertEqual(self.score_manager.get_current_level(), 1)
        self.assertEqual(self.score_manager.get_lines_cleared(), 0)
        self.assertEqual(self.score_manager.get_total_pieces_placed(), 0)
        self.assertEqual(len(self.score_manager.level_up_scores), 0)
        
        # 高分应该保持不变
        self.assertEqual(self.score_manager.get_high_score(), original_high_score)
    
    def test_zero_lines_cleared(self):
        """测试消除0行的情况"""
        initial_score = self.score_manager.get_current_score()
        earned = self.score_manager.add_line_score(0)
        
        self.assertEqual(earned, 0)
        self.assertEqual(self.score_manager.get_current_score(), initial_score)
        self.assertEqual(self.score_manager.get_lines_cleared(), 0)
    
    def test_string_representations(self):
        """测试字符串表示"""
        self.score_manager.add_line_score(3)
        
        str_repr = str(self.score_manager)
        self.assertIn('Score:', str_repr)
        self.assertIn('Level:', str_repr)
        self.assertIn('Lines:', str_repr)
        
        repr_str = repr(self.score_manager)
        self.assertIn('ScoreManager', repr_str)
        self.assertIn('score=', repr_str)
        self.assertIn('level=', repr_str)

if __name__ == '__main__':
    unittest.main()