# MCP Calculator Service

## 项目简介

MCP Calculator Service 是一个基于 FastMCP 框架的计算器服务，提供加、减、乘、除四个基本数学功能。该服务支持 SSE 协议，允许客户端实时接收计算结果。

## 功能

- 加法
- 减法
- 乘法
- 除法

## 使用方法

1. 克隆项目：
   ```bash
   git clone <repository-url>
   cd mcp-calculator
   ```

2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

3. 配置环境变量：
   在 `.env` 文件中设置必要的环境变量。

4. 启动服务：
   ```bash
   python src/main.py
   ```

## 贡献

欢迎提交问题和请求功能！请查看 [贡献指南](CONTRIBUTING.md) 以获取更多信息。

## 许可证

该项目使用 MIT 许可证，详细信息请查看 [LICENSE](LICENSE) 文件。