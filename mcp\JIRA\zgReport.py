# -*- coding: utf-8 -*-
# @Time    : 2024年3月5日19:12:21
# <AUTHOR> xiayk10729
# @Email   : <EMAIL>
# @File    : zgReport
# @Software: PyCharm
#打包命令：  cd /D E:\python379\Scripts     pyinstaller.exe -F E:\workspace\py_project\pythonproj\coverage4c\server\zgReport.py -i exe.ico

import time
import requests
import json


#推送钉钉消息，保留，暂无使用
def ddmsg(text,productVersion):
    lesson = time.strftime("%Y%m%d", time.localtime())
    headers = {'Content-Type': 'application/json'}
    #api_url = 'https://oapi.dingtalk.com/robot/send?access_token=eec77aa0b07a22d97c637a4db95698e99c500ef36f4fa5469286592aeb6d88ab'
    api_url = 'xxxxxxx'
    if(len(text)>200):
        text = text[:200]
    body = {"msgtype": "text",
            "text": {
                "content": productVersion + "报错信息xxxx"
            }}

    r=requests.post(api_url,data=json.dumps(body),headers=headers)


# api = flask.Flask(__name__)
#
# #查询jira数据
# @api.route('/queryjira', methods = ['POST'])
# def queryjira():
#     pass



if __name__ == '__main__':
    #获取配置文件参数
    #print(os.path.dirname(sys.executable))
    # config = configParser.myConfigParser()
    # server_port = config.getConfigValue("common", "server_port")
    # exec_time = config.getConfigValue("common", "exec_time")
    #
    # write_log = myLogger.get_my_logger("server_log")
    #
    # db_oracle_ip = config.getConfigValue("DB", "db_oracle_ip")
    # db_oracle_port = config.getConfigValue("DB", "db_oracle_port")
    # db_oracle_SID = config.getConfigValue("DB", "db_oracle_SID")
    # db_oracle_user = config.getConfigValue("DB", "db_oracle_user")
    # db_oracle_passwd = config.getConfigValue("DB", "db_oracle_passwd")
    #
    # ora=OracleUtil(db_oracle_ip,db_oracle_SID,db_oracle_port,db_oracle_user,db_oracle_passwd)
    #
    #
    # write_log.info("定时任务启动，每天定时{}打包xml并调用覆盖率接口".format(exec_time))
    # schedule.every(1).days.at(exec_time).do(queryjira)
    #
    # api.run(port=server_port, debug=True, host='0.0.0.0')
    pass










