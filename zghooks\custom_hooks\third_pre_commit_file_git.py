# -*- coding: utf-8 -*-
# -------------------------------------------------------------------------------
import collections
import json
import os
import time
import subprocess
import datetime
import re
import configparser
import sys
import hashlib
import requests
import shutil
import smtplib
import logging
from email.header import Header
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText


# reload(sys)
# sys.setdefaultencoding('utf8')

requests.packages.urllib3.disable_warnings()

work_dir = os.path.abspath(os.path.dirname(__file__))
# Hooks 日志文件路径
log_dir = os.path.join(work_dir, 'temp_log')
log_file_commit = os.path.join(log_dir, 'hooks_log_' + datetime.datetime.now().strftime('%Y%m%d%H%M%S') + '.log')
list_log_files = [os.path.join(os.path.dirname(work_dir), 'gitlog.txt'), log_file_commit]
log_files = ','.join(list_log_files)
# 不进行转换的仓库名
global_original_project_name = ['FAIS2.0.RISK-FICE','FAIS2-0-RISK-RPAS']

Global_nigixUrl = 'http://*************:83'
Global_cmsUrl = 'http://zg-cms.hundsun.com'
# 效能平台
Global_devopsUrl_out = "https://dev.hundsun.com/openapi/invoke/defaultFormData"  # 云内
Global_devopsUrl_in = "http://cloudin.proxy.in.hundsun.com/openapi/invoke/defaultFormData"  # 云外
Global_devopsUrl = Global_devopsUrl_out
app_id = "xjzqhwcqbpqeayck"
app_key = "def3885060764d1ba2cc5c2c029eb27"
# TS接口中心
Global_ts_center = 'https://ic.hundsun.com'
Global_tsUser = 'nafgjfkgboldpmfh'
Global_tsPwd = '1uwn4wgr7h2h0cb5yu5sbxew4tdcaua8'
Global_Address = "<EMAIL>"
Global_CC = "<EMAIL>"


class Sendmail():
    def __init__(self):
        pass

    def sendmail(self, Contents, address, mailsubject, cc='noCC', sender='<EMAIL>', attach='noattach'):
        """
        功能说明：
            邮件发送
        参数说明：
            Contents--正文，文本形式
            address--收件人地址，逗号分隔字符串格式
            mailsubject--主题
            cc--抄送人，'noCC'表示默认没有抄送人
            sender--发件人，默认o3cm
            attach--附件路径，'noattach'表示默认不带附件
        """

        # 第三方 SMTP 服务
        # mail_host="smtp.hundsun.cn"  #外网
        # mail_user="<EMAIL>"
        # mail_pass="trade32@wu"
        mail_host = "intramail.hundsun.com"  # 设置服务器，公司内网的服务器，****************无需认证
        mail_user = "<EMAIL>"  # 用户名
        mail_pass = "trade32@wu"  # 口令

        """
        if isFile:
            sReeult,iResutl = self.readFile(TxtOrFile)
            if not sReeult:
                logging.error(u'[邮件发送]读取邮件正文时报错！')
                return False
        else:
           sReeult,iResutl = self.readFile(TxtOrFile, isFile = False)
           if not sReeult:
                logging.error(u'[邮件发送]读取邮件正文时报错！')
                return False
        """
        message = MIMEMultipart()
        message['From'] = sender
        message['To'] = "%s" % address
        list_address = address.split(',')  # 转换成列表，接收邮件
        if cc != 'noCC':
            list_cc = cc.split(',')
            message['CC'] = cc
        else:
            list_cc = []
        receivers = list_address + list_cc
        if mailsubject == '':
            logging.error(u'邮件主题：%s为空，邮件发送失败。' % mailsubject)
            return False
        subject = mailsubject
        message['Subject'] = Header(subject, 'utf-8')

        # 邮件正文内容
        message.attach(MIMEText(Contents, 'html', 'utf-8'))  # 文本“plain”

        # 判断附件参数是否执行
        if attach not in ['', 'noattach']:
            for attach_path in attach.split(','):
                if not os.path.exists(attach_path):
                    logging.error(u'邮件附件：%s不存在，邮件发送失败。' % attach_path)
                    return False
                att1 = MIMEText(open(attach_path, 'rb').read(), 'base64', 'utf-8')
                att1["Content-Type"] = 'application/octet-stream'
                # 这里的filename可以任意写，写什么名字，邮件中显示什么名字
                filename = "%s" % os.path.basename(attach_path)
                att1.add_header('Content-Disposition', 'attachment', filename=Header(filename, 'utf-8').encode())
                message.attach(att1)

        try:
            smtpObj = smtplib.SMTP()
            smtpObj.connect(mail_host, 25)  # 25 为 SMTP 端口号
            smtpObj.set_debuglevel(0)
            # smtpObj.login(mail_user,mail_pass)  #内网邮箱时，无需认证
            # 修改了smtplib.py，加入“stderr = open(os.devnull, 'w')”，屏蔽邮件print的错误日志，避免垃圾信息
            smtpObj.sendmail(sender, receivers, message.as_string())
            smtpObj.close()
            return True
        except smtplib.SMTPException as e:
            logging.error(u'邮件发送失败，报错：%s' % str(e))
            return False


def write_log(comment):
    comment = comment
    if not os.path.exists(log_dir):
        os.mkdir(log_dir)
    with open(log_file_commit, 'a') as f2:
        f2.write(comment)


class MyConfigParser(configparser.ConfigParser):
    """
    功能说明：
        获取ini配置内容时，保持键值大小写不变。重写ConfigParser的optionxform方法，直接返回选项名，保持返回键值的大小写不变
    """

    def __init__(self, defaults=None):
        configparser.ConfigParser.__init__(self, defaults=defaults)

    # 这里重写了optionxform方法，直接返回选项名
    def optionxform(self, optionstr):
        return optionstr


def checkValue(key, value, node=''):
    """
    功能说明：
        如果是字典/列表形式，则实例化，如果报错，检查配置项值问题所在
    参数说明：
        key--配置项
        value--配置项的值
        node--节点名
    """
    if re.search('^\{|^\[|\}$|\]$|^False$|^True$', value):
        try:
            value = value.replace('null', '""')
            value = eval(value)  # 将字符串转为列表或字典
            return True, value
        except Exception as e:
            # 粗略判断错误原因，后续配置上CMS，避免此类问题
            if node != '':
                compileError = u'配置项转换为对象时报错，配置节点（%s）配置项：\n%s=%s\n报错如下：\n' % (node, key, value)
            else:
                compileError = u'参数转换为对象时报错，配置项：\n%s=%s\n报错如下：\n' % (key, value)
            if "[" in value or "]" in value:
                Lb = value.count("[")
                Rb = value.count("]")
                if Lb > Rb:
                    compileError = compileError + u'多了%s个左中括号“[”，或者少了%s个右中括号“]”\n' % (str(Lb - Rb), str(Lb - Rb))
                elif Rb > Lb:
                    compileError = compileError + u'多了%s个右中括号“]”，或者少了%s个左中括号“[”\n' % (str(Rb - Lb), str(Rb - Lb))
            if "{" in value or "}" in value:
                Lb = value.count("{")
                Rb = value.count("}")
                if Lb > Rb:
                    compileError = compileError + u'多了%s个左花括号“{”，或者少了%s个右花括号“}”\n' % (str(Lb - Rb), str(Lb - Rb))
                elif Rb > Lb:
                    compileError = compileError + u'多了%s个右花括号“}”，或者少了%s个左花括号“{”\n' % (str(Rb - Lb), str(Rb - Lb))
            if "'" in value or '"' in value:
                quo = value.count("'")
                dquo = value.count('"')
                if quo % 2 != 0: compileError = compileError + u"少了1个单引号“'”\n"
                if dquo % 2 != 0: compileError = compileError + u'少了1个双引号“"”\n'

            if re.search(u'报错如下：\n$', compileError):
                try:
                    compileError = compileError + e.msg
                except:
                    try:
                        compileError = compileError + e.message
                    except:
                        compileError = compileError + u'语法错误'
            try:  # 解决jenkins界面显示乱码的情况
                error = compileError.encode('UTF-8')
            except:
                error = compileError.encode('GBK')  # Jenkins job报错
            return False, error
    else:
        if str(value) == 'null':
            value = ''
        return True, value


def synchronization_ini(configPath):
    """
    读取ini文件，返回ini文件内容字典
    """
    if not os.path.exists(configPath):
        error = u'(%s)配置文件不存在，请检查！' % configPath
        try:  # 解决jenkins界面显示乱码的情况
            error = error.encode('GBK')
        except:
            error = error.encode('UTF-8')
        return False, error
    # 读取配置文件.ini，获取所有变量信息，存放字典中返回出去，外层调用字典，get对应的key值获取ini文件中的值
    #  实例化configParser对象
    config = MyConfigParser()
    try:
        config.read(configPath, 'UTF-8')
    except Exception as e:
        return False, str(e)
    dict_tmp = collections.OrderedDict()
    for node in config.sections():
        for j in config.items(node, raw=True):
            key = u'%s' % j[0]
            value = u'%s' % j[1]
            sResult, iResult = checkValue(key, value, node=node)  # 检查对象合规
            if not sResult:
                result = u'(%s)配置文件规范不合规：%s' % (configPath, iResult)
                write_log(result)
                return False, result
            dict_tmp[key] = iResult
    return True, {'dict_config': dict_tmp}


def checkBuilding(versionNo, modifynos, isCloud):
    """
    功能说明：
        检查当前修改单是否正在集成，如在集成则报错
    """
    header = {'content-Type': 'application/json', 'Charset': 'UTF-8'}
    data = {
        "type": "modifyNos",
        "VersionTS": versionNo,
        "modifyNos": modifynos,
        "operateType": "query"
    }
    if isCloud == 0 or isCloud == '0':
        RedisURL = '%s/api/operate_api' % Global_nigixUrl
    else:
        RedisURL = '%s/api/operate_api' % Global_cmsUrl
    try:
        message_json = json.dumps(data)
        write_log('获取正在集成修改单:%s\n' % message_json)
        res = requests.post(url=RedisURL, auth=('trade', 'trade32wu'), data=message_json, headers=header, verify=False)
        result = res.json()
        result_no = result.get('result_no')
        result_info = result.get('result_info')
        if result_no == 0:
            result = u"[Error]修改/任务单[%s]正在集成，不允许递交。" % modifynos
            return False, result
        else:
            write_log(u'--->%s未被集成锁定，允许递交\n' % modifynos)
            return True, result_info
    except Exception as e:
        result = u"[Error]Git钩子校验，获取正在集成修改单异常，报错：%s。请联系CMS配置管理员。" % str(e)
        write_log('%s\n' % result)
        Sendmail().sendmail(Contents=str(result), address='<EMAIL>', mailsubject="Git钩子校验异常", cc=Global_CC, sender="<EMAIL>", attach=log_files)
        return True, result


def ts_login(username=Global_tsUser, password=Global_tsPwd):
    """
    功能说明：
        TS平台登录
    """
    timestamp = long(round(time.time() * 1000))
    data = {
        "username": username,
        "timestamp": timestamp
    }
    all_data = "%s&%s&%s&%s" % (username, timestamp, password, username)
    new_md5 = hashlib.md5()
    new_md5.update(all_data.encode("utf-8"))  # python3没有long类型
    sign = new_md5.hexdigest().upper()
    data["signature"] = sign.lower()  # 必须转小写，不是小写会报：用户验签不正确
    try:
        url = '%s/auth/loginservice' % Global_ts_center
        r1 = requests.post(url=url, data=data)
        result = r1.json()
        message = result.get('errmsg')
        errcode = result.get('errcode')
        if errcode == '0':
            access_token = result.get('data')['access_token']
            return True, access_token
        result = u'TS平台登录失败，报错：%s' % message
        return False, result
    except Exception as e:
        result = u'TS平台登录异常：%s' % str(e)
        return False, result


def ts_query_modify(modifynos=None, productId=None, modifyStatus=None, versionNo=None, promiseDateBegin=None,
                    promiseDateEnd=None, integationDateBegin=None, integationDateEnd=None, extTxt7=None, patchNum=None,
                    exactPatchNum=None, pageNum=None, testPackageName=None, moduleName=None, check_task_status='1'):
    """
    功能说明：
        TS修改单查询接口
    参数说明：
        modifynos--修改单编号
        productId--产品ID
        modifyStatus--修改单状态
        versionNo--修改版本
        check_task_status--是否校验TS修改单任务完成状态
    """
    try:
        sResult, iResult = ts_login()  # 登录TS，获取access_token
        if sResult:
            access_token = iResult
            headers = {
                'Authorization': access_token
            }
            if not productId:
                message = u'产品编号(%s)错误，请确认！' % productId
                return False, message
            data = {
                "productId": productId,
                "modifyNum": modifynos,
                "modifyStatus": modifyStatus,
                "versionNo": versionNo,
                "promiseDateBegin": promiseDateBegin,
                "promiseDateEnd": promiseDateEnd,
                "integationDateBegin": integationDateBegin,
                "integationDateEnd": integationDateEnd,
                "extTxt7": extTxt7,
                "patchNum": patchNum,
                "exactPatchNum": exactPatchNum,
                "pageNum": pageNum,
                "testPackageName": testPackageName,
                "moduleName": moduleName
            }
            url = '%s/tsinterface/pageQuery/modifyList' % Global_ts_center
            r1 = requests.post(url, headers=headers, data=data, verify=False)
            result = r1.json()
            message = result.get('errmsg')
            errcode = result.get('errcode')
            if errcode != '0':
                return False, message
            req_data = result.get('data')
            modify_data = req_data['datas']
            sResult, iResult = checkModifyStatus(modifynos=modifynos, modifyData=modify_data, versionNo=versionNo,
                                                 modifyStatus=modifyStatus, check_task_status=check_task_status,
                                                 ts_platform='0')

            if not sResult:
                return False, u'[Error]修改单状态校验报错：%s' % iResult
            return True, iResult
        else:
            return False, iResult
    except requests.RequestException as e:
        result = u'[Error]修改单接口查询报错：%s' % str(e)
        return False, result


def checkModifyStatus(modifynos, modifyData, versionNo, modifyStatus, check_task_status='1', ts_platform='0'):
    """
    功能说明：
        TS修改单或任务单状态校验
    参数说明：
        modifynos--需要校验的修改单编号
        modifyData--TS修改单或任务单查询数据
        versionNo--修改版本
        modifyStatus--修改单状态
        check_task_status--是否校验TS修改单任务完成状态
        ts_platform--0:ts/1:dev
    """

    if modifyData == []:  # 无返回值或者传入修改单总数和返回条数不相等，表示某条修改单不存在
        result = u'效能任务单编号：%s, 修改版本：%s，任务单状态：%s不匹配！' % (modifynos, versionNo, modifyStatus)
        return False, result
    if ts_platform == '0':
        write_log(u'需要校验的TS修改单编号=%s\n' % modifynos)
    else:
        write_log(u'需要校验的效能任务单编号=%s\n' % modifynos)
    compileError = ''
    list_noStatus = []  # 状态不对
    list_noVersionTS = []  # 版本号不对
    list_noCompleted = []  # 任务完成状态
    # Modify_Lists = []  # 待集成
    for i in modifyData:
        status = str(i['modifyStatus'])
        verts = str(i['versionNO'])
        if ts_platform == '0':
            modifyno = str(i[u'modifyNum'])
            comstatus = str(i.get(u'completedStatusName', ''))
            if check_task_status == "0":
                if comstatus == '未完成':
                    list_noCompleted.append(modifyno)
            if not re.search(status, modifyStatus):
                list_noStatus.append(modifyno)
            if verts != versionNo:
                list_noVersionTS.append(modifyno)
            write_log('单号=%s，状态=%s，版本号=%s，任务完成状态=%s' % (modifyno, status, verts, comstatus))
        else:
            modifyno = str(i[u'taskNums'])
            if not re.search(status, modifyStatus):
                list_noStatus.append(modifyno)
            if verts != versionNo:
                list_noVersionTS.append(modifyno)
            write_log(u'单号=%s，状态=%s，版本号=%s' % (modifyno, status, verts))

    if list_noStatus:
        compileError = u'不符合集成状态的任务单有：%s\n' % ','.join(list_noStatus)
    if list_noVersionTS:
        compileError = compileError + u'分支版本：%s，修改版本不正确的有：%s\n' % (versionNo, ','.join(list_noVersionTS))
    if list_noCompleted:
        compileError = compileError + u'任务未完成的有：%s\n' % ','.join(list_noCompleted)
    write_log('[Error]%s\n' % compileError)
    if compileError != '':
        return False, compileError

    result = u'修改版本及修改/任务单状态校验成功'
    return True, result


def dataProcess(data):
    """
    数据处理
    data:输入值，排序、拼接，MD5加密
    sign:MD5加密，参数根据字段名排序（如app_id=1&app_key=2&biz_content=1&method=1）
    :return:all_data
    """
    all_data = ""
    for key in sorted(data.keys()):
        all_data = all_data + key + "=" + str(data[key]) + "&"

    all_data = all_data[0:-1]
    new_md5 = hashlib.md5()
    new_md5.update(all_data.encode("utf-8"))  # 需要编码转换
    sign = new_md5.hexdigest().upper()
    data["sign"] = sign
    return data


def devopsUrl(isCloud='1'):
    global Global_devopsUrl
    if isCloud not in [False, '1']:
        Global_devopsUrl = Global_devopsUrl_in

    return Global_devopsUrl


def updateTestRecoder(app_id=app_id, app_key=app_key, taskNums=None, testModifyType=None, is_cloud='1'):
    """
    任务前后台字段修改
    :return:
    """
    Global_devopsUrl = devopsUrl(is_cloud)
    timestamp = int(round(time.time() * 1000))
    method = "ci/updateTaskRecord"
    data = {
        "app_id": app_id,
        "app_key": app_key,
        "method": method,
        "timestamp": timestamp,
        "taskNums": taskNums,
        "testModifyType": testModifyType
    }
    data = dataProcess(data)
    try:
        r = requests.post(url=Global_devopsUrl, data=data)
        result = r.json()
        message = result.get('message', '')
        if result.get('code') == '200':
            return True, message
        return False, message
    except Exception as e:
        result = u"[Error]Git钩子校验，调用效能接口任务前后台字段修改更新异常，报错：%s。请联系配置管理员。" % str(e)
        write_log('%s\n' % result)
        Sendmail().sendmail(Contents=str(result), address='<EMAIL>', mailsubject="Git钩子校验异常",
                            cc=Global_CC, sender="<EMAIL>", attach=log_files)
        return False, result


def separateTsTask(tproductId=None, versionNo=None, taskNums=None, is_cloud='1'):
    """
    区分出是TS单号还是效能单号
    参数说明：
        tproductId：产品编号
        versionNo： 修改版本
        taskNums：任务单编号
    返回结果：
        tsModifyNums--ts单号
        taskNums--效能单号
    """
    Global_devopsUrl = devopsUrl(is_cloud)
    timestamp = int(round(time.time() * 1000))
    method = "ci/separateTsTask"
    data = {
        "app_id": app_id,
        "app_key": app_key,
        "method": method,
        "timestamp": timestamp,
        "taskNums": taskNums
    }
    if tproductId:  # 非必填
        data["tproductId"] = tproductId
    if versionNo:  # 非必填
        data["versionNo"] = versionNo
    data = dataProcess(data)
    write_log(u'分离TS单号/效能单号(taskNums：%s，is_cloud：%s)\n' % (taskNums, is_cloud))
    try:
        r = requests.post(url=Global_devopsUrl, data=data, timeout=20)
        result = r.json()
        write_log(u'接口返回：%s\n' % str(result))
        if result.get('message') != 'success':
            return False, u'分离TS修改单和效能任务单失败'
        data = result.get('data')
        dict_modifyNos = {}
        dict_modifyNos['tsModifyNums'] = data.get('tsModifyNums')
        dict_modifyNos['taskNums'] = data.get('taskNums')
        return True, dict_modifyNos
    except Exception as e:
        result = u"[Error]Git钩子校验，调用效能接口分离TS修改单和效能任务单异常，报错：%s。请联系配置管理员。" % str(e)
        write_log('%s\n' % result)
        Sendmail().sendmail(Contents=str(result), address='<EMAIL>', mailsubject="Git钩子校验异常",
                            cc=Global_CC, sender="<EMAIL>", attach=log_files)
        return False, result


# 效能任务单
def queryDevTask(tproductId=None, modifyNum=None, reqNum=None, modifyStatus=None, integationDateBegin=None,
                 integationDateEnd=None, versionNo=None, testPackageName=None, patchNum=None, exactPatchNum=None,
                 createTimeStart=None, createTimeEnd=None, pageNum=-1, is_cloud='1'):
    """
    功能说明：
        查询效能平台任务单查询
    参数说明：
        tproductId：产品编号
        patchNum： 补丁单号
        versionNo： 修改版本
        modifyNum：修改单编号
        testPackageName：测试包版本号
        modifyStatus：修改单状态
        exactPatchNum：补丁单编号精确查询
        integationDateBegin：集成开始日期， 格式yyyy-MM-dd HH:mm:ss
        integationDateEnd：集成结束日期， 格式yyyy-MM-dd HH:mm:ss
        pageNum： 页码
    返回结果：
        包含以下信息：modifyStatus,patchNum,modifyNum,versionNO,testerName,testResultName,testContent,modifyId,integationMan,auditor
    """
    Global_devopsUrl = devopsUrl(is_cloud)
    timestamp = int(round(time.time() * 1000))
    method = "ci/queryDevTask"
    data = {
        "app_id": app_id,
        "app_key": app_key,
        "method": method,
        "timestamp": timestamp
    }
    if tproductId:
        data["tproductId"] = tproductId
    #if versionNo:
        #data["versionNo"] = versionNo
    if modifyNum:
        data["taskNums"] = modifyNum
    if reqNum:
        data["reqNum"] = reqNum
    #if modifyStatus:
        #data["modifyStatus"] = modifyStatus
    if testPackageName:
        data["testPackageName"] = testPackageName
    if patchNum:
        data["patchNum"] = patchNum
    if exactPatchNum:
        data["exactPatchNum"] = exactPatchNum
    if integationDateBegin:
        integationDateBegin = integationDateBegin + " 00:00:00"  # 传入的开始时间格式仅为日期，需从当前0点开始计算
        data["integationDateBegin"] = integationDateBegin
    if integationDateEnd:
        integationDateEnd = integationDateEnd + " 23:59:59"  # 结束时间，同理
        data["integationDateEnd"] = integationDateEnd

    data = dataProcess(data)
    try:
        r1 = requests.post(url=Global_devopsUrl, data=data, timeout=20)
        result = r1.json()
        write_log(u'本次递交任务单状态和版本校验结果：%s\n' % str(result))
        message = result.get('message')
        if message == 'success':
            modifyData = result.get('data').get('items')
        else:
            return False, message
        write_log('---' + modifyNum + '---' + versionNo + '---%s' % modifyStatus + '---\n')
        sResult, iResult = checkModifyStatus(modifynos=modifyNum, modifyData=modifyData, versionNo=versionNo,
                                             modifyStatus=modifyStatus, ts_platform='1')
        if not sResult:
            return False, u'[Error]任务单状态校验报错：%s' % iResult
        return True, iResult
    except requests.RequestException as e:
        result = u"[Error]Git钩子校验，调用效能接口任务单接口查询异常，报错：%s。请联系配置管理员。" % str(e)
        write_log('%s\n' % result)
        Sendmail().sendmail(Contents=str(result), address='<EMAIL>', mailsubject="Git钩子校验异常",
                            cc=Global_CC, sender="<EMAIL>", attach=log_files)
        return False, result


def readConfig():
    """
    功能说明：
        读取hookConfig.ini配置文件
    """
    configPath = os.path.join(work_dir, u"hookConfig.ini")
    # 读取ini文件，返回ini文件内容字典
    sResult, iResult = synchronization_ini(configPath)
    if not sResult:
        result = '读hookConfig.ini文件异常:%s\n' % iResult
        write_log(result)
        return False, result
    config_info = iResult
    commit_file_dict = collections.OrderedDict()
    commit_file_dict = config_info['dict_config']
    return True, commit_file_dict


def get_stdout(cmd):
    stdout_str = subprocess.Popen(cmd, shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE,
                                  stderr=subprocess.STDOUT)
    lines = stdout_str.stdout.read()
    try:
        lines = lines.decode('utf-8', 'strict')
    except Exception as err:
        try:
            lines = lines.decode('gbk')
        except Exception as e:
            lines = lines.decode('iso-8859-1')  # 获取文件修改行数时，忽略乱码
    return lines


class ZGHooks:
    def __init__(self, branch, commit_info, full_path):
        self.branch = branch.strip()
        self.commit_info = commit_info
        self.list_commit_info = self.parse_commit_info(self.commit_info)
        self.full_path = full_path
        self.global_versionts = collections.OrderedDict()
        self.analyse_versionts = collections.OrderedDict()
        self.modifyNo_template = '' # 修改单编号模板
        self.pattern_modifyNo = '' # 修改单编号模板匹配规则
        self.product_id = ''
        self.tproductId = ''
        self.platform = '1'  # 技术支持平台，0:TS, 1:效能
        self.ts_state = '3'  # 修改单状态
        self.dev_state = '8,20'  # 任务单状态 8：待集成  20：集成中
        # self.modify_status = ''
        self.check_task_status = '1'  # 仅TS有效，校验TS任务单状态
        self.check_taskscript_status = '1'  # 已弃用
        self.is_cloud = '1'  # 是否云内, 默认云外
        self.scm_list = ''  # 超级管理员列表字符串
        self.no_commit = {}  # 限制递交目录及人员白名单
        self.except_dir = {}  # 排除目录
        self.control_dir = {}  # 受控目录
        self.control_branch = {}  # 受控目录
        self.auto_check_modify = {}  # 自动校验单号目录
        self.check_file_content = {}  # 内容校验
        self.forword_backword = {}  # 前后台配置
        self.allow_modify_svnIgnore = '0'  # 是否允许非超级管理员递交svn:ignore属性，0：允许，1：不允许，默认允许

    def run(self):
        sResult, iResult = readConfig()
        if not sResult:
            write_log(iResult)
            return False
        commit_file_dict = collections.OrderedDict()
        commit_file_dict = iResult

        # 将配置文件获取到的内容，赋值给下面变量
        self.global_versionts = commit_file_dict.get('global_versionts', {})
        self.analyse_versionts = commit_file_dict.get('analyse_versionts', {})

        # 使用TS接口工具需要用到的一个产品版本号
        self.productId = commit_file_dict.get('product_id', commit_file_dict.get('ProductID', ''))
        self.tproductId = commit_file_dict.get('tproductId', commit_file_dict.get('tProductID', ''))
        self.platform = commit_file_dict.get('platform', '1')
        # 修改单状态
        self.ts_state = commit_file_dict.get('ts_state', '3')  # 修改单状态
        self.dev_state = commit_file_dict.get('dev_state', '8')  # 修改单状态

        self.modifyNo_template = commit_file_dict.get('modifyNo_template',
                                                      ["修改单编号", "修改单号", "\[修改单编号\]", "\[TS修改单编号/效能任务编号\]", "【修改单号】"])
        self.pattern_modifyNo = '|'.join(list(set(self.modifyNo_template)))  # 转义
        self.pattern_modifyNo = re.compile(self.pattern_modifyNo)

        # 产品名称
        self.product_name = commit_file_dict.get('product_name', '')

        # 云内云外0/1
        self.is_cloud = commit_file_dict.get('is_cloud', '1')
        # mr控制0/1
        self.mr_control = commit_file_dict.get('mr_control', '0')

        # 超级用户人员列表，可不进行钩子判断
        self.scm_list = commit_file_dict.get('scm_list', '')

        # 提交限制目录
        self.no_commit = commit_file_dict.get('no_commit', {})

        # 排除钩子控制的修改单号
        self.except_modify = commit_file_dict.get('except_modify', '')

        # 排除路径，以下目录直接放行
        self.except_dir = commit_file_dict.get('except_dir', {})

        # 钩子控制目录，以下目录进行控制
        self.control_dir = commit_file_dict.get('control_dir', {})

        # 钩子控制目录，以下目录进行控制
        self.control_dir = commit_file_dict.get('control_dir', {})

        # 钩子控制分支，以下分支进行控制
        self.control_branch = commit_file_dict.get('branch_control_info', {})

        # 校验任务完成状态
        self.check_task_status = commit_file_dict.get('check_task_status', '1')
        self.auto_check_modify = commit_file_dict.get('auto_check_modify', {})

        # 是否限制非超级管理员不允许修改svn:ignore属性, 1:限制非超级管理员修改svn:ignore属性 0：允许非超级管理员修改svn:ignore属性
        self.allow_modify_svnIgnore = commit_file_dict.get('allow_modify_svnIgnore', '0')

        # 校验任务脚本完成状态
        self.check_taskscript_status = commit_file_dict.get('check_taskscript_status', '1')  # 已废弃

        # 校验文件内容
        self.check_file_content = commit_file_dict.get('check_file_content', {})

        # 限制提交文件类型
        self.no_commit_filetypes = commit_file_dict.get('no_commit_filetypes', {})

        # 前后台配置
        self.forword_backword = commit_file_dict.get('forword_backword', {})

        self.getCmEmail(product_name=self.product_name, isCloud=self.is_cloud)
        write_log('-------------------------Git钩子配置-------------------------\n')
        write_log("product_name=%s\n" % str(self.product_name))
        write_log("productId=%s\n" % str(self.productId))
        write_log("tproductId=%s\n" % str(self.tproductId))
        write_log("platform=%s\n" % str(self.platform))
        write_log("ts_state=%s\n" % str(self.ts_state))
        write_log("dev_state=%s\n" % str(self.dev_state))
        write_log("is_cloud=%s\n" % str(self.is_cloud))
        write_log("scm_list=%s\n" % str(self.scm_list))
        write_log("no_commit=%s\n" % str(self.no_commit))
        write_log("except_modify=%s\n" % str(self.except_modify))
        write_log("except_dir=%s\n" % str(self.except_dir))
        write_log("control_dir=%s\n" % str(self.control_dir))
        write_log("check_task_status=%s\n" % str(self.check_task_status))
        write_log("auto_check_modify=%s\n" % str(self.auto_check_modify))
        write_log("allow_modify_svnIgnore=%s\n" % str(self.allow_modify_svnIgnore))
        write_log("check_taskscript_status=%s\n" % str(self.check_taskscript_status))
        write_log("check_file_content=%s\n" % str(self.check_file_content))
        write_log("forword_backword=%s\n" % str(self.forword_backword))
        write_log('-----------------------------------------------------------\n')
        return True

    def str2list(self, strinfo, delimiter=','):
        """
        分割字符串，并返回列表
        """
        return_list = []
        return_list = [i for i in strinfo.strip(delimiter).split(delimiter) if i.strip() != '']
        return return_list

    def repository2fullpath(self, repository):
        """
        通过接口创建出来的仓库规则为：
            前台显示仓库名为:FAIS2.0.CP-INFODATA
            后台存储fullpath为fais2-0-cp-infodata
        通过界面创建出来的仓库保持原状
        """
        if repository == '':
            return ''

        if repository in global_original_project_name:
            return repository

        #*前面的点不进行替换
        if ".*" in repository:
            repository = re.sub(".*", "splitflagds", repository)
        #云外fullpath需要做特殊处理
        if self.is_cloud == '1':
            #将大写的都改成小写
            repository = repository.lower()
            #将.都替换成-
            repository = repository.replace(".", "-")
        if "splitflagds" in repository:
            repository = re.sub("splitflagds", ".*", repository)
        return repository

    # 是否是超级用户
    def committer_check(self):
        list_scm_list = self.str2list(self.scm_list)
        list_scm_list.append('zgcm-ci')
        write_log("超级用户=%s\n" % list_scm_list)
        list_committer = []
        list_committer_email = []
        for i in self.list_commit_info:
            if i.get('modifyuser') not in list_scm_list:
                list_committer.extend(i.get('modifyuser'))
        list_committer = list(set(list_committer))
        for i in list_committer:
            list_committer_email.append("%<EMAIL>" %i)
        write_log(u"提交人：%s\n" % (','.join(list_committer)))
        global Global_Address
        Global_Address = ','.join(list_committer_email)
        # 仅允许单人提交并且提交人为超级用户
        if len(list_committer) == 1 and list_committer[0] in list_scm_list:
            return True
        return False

    # 是否排除目录
    def folder_check(self):
        list_exclude_dir = []
        list_modify_files = []
        list_info = self.except_dir.get('list', [])
        for info in list_info:
            repository_info = info.get('exclude_repository', '')
            if repository_info == 'ALL':
                list_exclude_dir.extend(self.str2list(info.get('list_except_dir', '')))
                continue
            list_repository = self.str2list(repository_info)
            for repository_name in list_repository:
                repository_name = repository_name.strip()
                repository_name = self.repository2fullpath(repository_name)
                if re.search(repository_name, self.full_path):
                    list_exclude_dir.extend(self.str2list(info.get('list_except_dir', '')))
                else:
                    continue
        list_exclude_dir = list(set(list_exclude_dir))
        write_log("排除目录=%s\n" % str(list_exclude_dir))
        for i in self.list_commit_info:
            modifyuser = i.get('modifyuser', [])
            modifyfile = i.get('modifyfile', [])
            modifyno = i.get('modifyno', [])
            for j in modifyfile:
                file_exclude = False
                for k in list_exclude_dir:
                    k = k.replace('*', '.*')
                    if re.search(k, j):
                        file_exclude = True
                #只要有一个文件未排除，则需要继续检查
                if not file_exclude:
                    return False
        return True

    def branch_check(self):
        list_control_branch = []
        list_exclude_branch = []
        for info in self.control_branch.get('list', []):
            list_repository = self.str2list(info.get('branch_repository', ''))
            # 如果配置库为空则代表需要校验
            if list_repository == []:
                list_control_branch.extend(self.str2list(info.get('branch_control', '')))
                list_exclude_branch.extend(self.str2list(info.get('exclude_branch', '')))
            for repository in list_repository:
                repository = self.repository2fullpath(repository)
                if repository == '':
                    list_control_branch.extend(self.str2list(info.get('branch_control', '')))
                    list_exclude_branch.extend(self.str2list(info.get('exclude_branch', '')))
                elif re.search(repository, self.full_path):
                    list_control_branch.extend(self.str2list(info.get('branch_control', '')))
                    list_exclude_branch.extend(self.str2list(info.get('exclude_branch', '')))
        write_log(u'受控分支列表：%s\n' % str(list_control_branch))
        write_log(u'排除分支列表：%s\n' % str(list_exclude_branch))
        write_log(u'本次修改分支：%s\n' % self.branch)
        if self.branch in list_exclude_branch:
            write_log(u'本次修改分支(%s)为排除分支\n' % self.branch)
            return False
        # 排除目录支持正则
        for i in list_exclude_branch:
            i = i.replace('*', '.*')
            if re.search(i, self.branch):
                write_log(u'本次修改分支(%s)匹配排除分支规则(%s)\n' % (self.branch, i))
                return False

        for i in list_control_branch:
            i = i.replace('*', '.*')
            if re.search(i, self.branch):
                write_log(u'本次修改分支(%s)匹配受控分支规则(%s)\n' % (self.branch, i))
                return True
        return False

    def mr_check(self):
        if 'Merge branch' in self.commit_info:
            if self.mr_control == '1':
                write_log(u'本次递交是Merge Requests，MR受控未开启！\n')
                return False
            else:
                write_log(u'本次递交是Merge Requests，MR受控已开启！\n')
        else:
            write_log(u'本次递交非Merge Requests！\n')
        return True

    def remove_exclude_modifynum(self, modifynum):
        list_modify = self.str2list(modifynum)
        return_list_modify = []
        list_except_modify = self.except_modify.split(',') if self.except_modify else []
        for i in list_modify:
            if i not in list_except_modify:
                return_list_modify.append(i)
        return_list_modify = list(set(return_list_modify))
        return_modifynum = ','.join(return_list_modify)
        write_log(u'排除修改单列表：%s\n' % str(list_except_modify))
        write_log(u'本次递交修改单列表：%s\n' % str(list_modify))
        write_log(u'本次递交非排除修改单：%s\n' % str(return_modifynum))
        return True, return_modifynum

    def updateTestRecoder(self, modifyNos):
        if modifyNos == '' or self.forword_backword == {} or self.forword_backword.get('list') == []:
            return True, ''
        list_fb = self.forword_backword.get('list')

        testModifyType = '2'
        for i in list_fb:
            list_repository = self.str2list(i.get('fb_repository', ''))
            for repository in list_repository:
                repository = self.repository2fullpath(repository)
                if re.search(repository, self.full_path):
                    testModifyType = i.get('fb_type', '2')
                    break

        sResult, iResult = updateTestRecoder(taskNums=modifyNos, testModifyType=testModifyType, is_cloud=self.is_cloud)
        if not sResult:
            return False, iResult
        write_log(u'任务前后台字段修改完成\n')

        return True, ''

    # 文件内容校验，目前*pom*.xml和*build*.gradle进行校验
    def filecontent_check(self, files_content):
        write_log(u"文件内容校验=%s\n" % str(self.check_file_content))
        list_modify_contents = [{"file_name": i.split(':')[0], "content": ":".join(i.split(':')[2:])} for i in files_content.split('\n')]
        if self.check_file_content == {} or self.check_file_content.get('list') == []:
            return True, ''
        else:
            list_check_file_content = self.check_file_content.get('list', [])
            for dict_file_check in list_check_file_content:
                match_type = str(dict_file_check.get('match_type', '0'))  # 0:匹配到报错,1:匹配不到报错
                check_file_name = dict_file_check.get('file_name', '').replace('*', '.*')  # 文件名，支持模糊匹配
                check_file_path = dict_file_check.get('file_path', '').replace('*', '.*')  # 匹配路径，支持模糊匹配
                check_file_content = dict_file_check.get('file_content', '').replace('*', '.*')  # 文件内容，不支持多个
                list_check_file_name = check_file_name.split(',') if check_file_name else []
                list_check_file_path = check_file_path.split(',') if check_file_path else []
                if list_check_file_path:  # 先对文件路径做校验，再对文件名和文件内容做校验
                    for check_path in list_check_file_path:
                        for modify_info in list_modify_contents:
                            modify_file = modify_info['file_name']
                            modify_content = modify_info['content']
                            if not re.search(check_path, modify_file):
                                continue
                            for check_file in list_check_file_name:
                                if re.search(check_file, modify_file):
                                    if match_type == '0' and re.search(r'^\+', modify_content) and re.search(check_file_content, modify_content):
                                        compileError = u'文件内容校验失败，%s不允许存在%s关键词\n' % (modify_file, check_file_content)
                                        return False, compileError
                                    elif match_type == '1' and not re.search(check_file_content, modify_content):
                                        compileError = u'文件内容校验失败，%s不存在%s关键词\n' % (modify_file, check_file_content)
                                        return False, compileError
                else:  # 仅对文件名和文件内容做校验
                    for modify_info in list_modify_contents:
                        modify_file = modify_info['file_name']
                        modify_content = modify_info['content']
                        for check_file in list_check_file_name:
                            if re.search(check_file, modify_file):
                                if match_type == '0' and re.search(r'^\+', modify_content) and re.search(check_file_content, modify_content):  # 匹配到报错
                                    compileError = u'文件内容校验失败，%s不允许存在%s关键词\n' % (modify_file, check_file_content)
                                    return False, compileError
                                elif match_type == '1' and not re.search(check_file_content, modify_content):  # 匹配不到报错
                                    compileError = u'文件内容校验失败，%s不存在%s关键词\n' % (modify_file, check_file_content)
                                    return False, compileError
        return True, ''

    # 提交限制递交文件类型
    def filetypes_check(self):
        #限制提交文件类型
        sub_no_commit_filetypes = self.no_commit_filetypes.get('sub_no_commit_filetypes', '')
        sub_except_users = self.no_commit_filetypes.get('sub_except_filetypes', '')
        list_no_commit_filetypes = self.str2list(sub_no_commit_filetypes)
        list_except_users = self.str2list(sub_except_users)
        list_no_commit_filetypes = list(set(list_no_commit_filetypes))
        list_except_users = list(set(list_except_users))
        write_log(u"限制递交文件类型=%s\n" % str(sub_no_commit_filetypes))
        write_log(u"限制递交文件白名单=%s\n" % str(sub_except_users))
        write_log(u"修改文件列表=%s\n" % str(self.list_commit_info))
        for i in self.list_commit_info:
            modifyuser = i.get('modifyuser', [])
            modifyfile = i.get('modifyfile_nodelete', [])
            modifyno = i.get('modifyno', [])
            for change_file in modifyfile:
                change_file = change_file.strip()
                if change_file == "":
                    continue
                write_log("change_file=%s\n" % change_file)
                sShortName, suffix = os.path.splitext(change_file)
                write_log("suffix=%s\n" % str(suffix))
                if suffix == '':
                    continue
                # 兼容限制提交文件类型前面带.的场景
                for no_commit_type in list_no_commit_filetypes:
                    no_commit_type = '.' + str(no_commit_type).strip('.')
                    write_log("no_commit_type=%s\n" % str(no_commit_type))
                    if str(no_commit_type) == str(suffix):
                        if len(modifyuser) == 1 and modifyuser[0] in list_except_users:  # 限制递交文件路径白名单不为空
                            continue
                        else:
                            compileError = u'提交失败!二进制文件：%s限制递交，如二方、三方编译依赖请走流程管理审批申请开通白名单。\n' % no_commit_type
                            return False, compileError
        return True, ''

    # 获取日志是否包含任务单编号
    def is_check_modifyno(self):
        list_modify_num = []
        write_log(u'修改内容：%s\n' % str(self.list_commit_info))
        for i in self.list_commit_info:
            modifyno = i.get('modifyno', [])
            list_modify_num.extend(modifyno)
        list_modify_num = list(set(list_modify_num))
        modifynum = ','.join(list_modify_num)
        write_log(u'[TS修改单编号/效能任务编号]：%s\n' % modifynum)
        if modifynum == '':
            return False, ""
        return True, modifynum

    # 是否排除修改单号
    def modify_check(self, modify_no):
        list_exceptnos = self.except_modify.split(',')
        list_modifynos = modify_no.split(',')
        for no in list_modifynos:
            if no in list_exceptnos:
                return True
        return False

    # 获取log内填写的修改单编号,并保存到文件
    def get_modify_id(self, modify_num):
        sResult, iResult = separateTsTask(taskNums=modify_num, is_cloud=self.is_cloud)
        if not sResult:
            return False, iResult, ''
        list_modify_no_ts = iResult['tsModifyNums']  # ts修改单
        list_modify_no_dev = iResult['taskNums']  # 效能任务单
        modifyNos_ts = ','.join(list_modify_no_ts)
        modifyNos_dev = ','.join(list_modify_no_dev)
        if self.platform == '1' and modifyNos_ts != '':
            return False, u'效能单号：%s不存在' %modifyNos_ts, ''

        return True, modifyNos_ts, modifyNos_dev

    def queryVersionConfig(self, data):
        """
        功能说明：
            1、一体化调用cms接口，查询信息，已发布，返回false，未发布，返回true
        参数说明：
                Version--版本号
        """

        Header = {
            'Content-Type': 'application/json',
        }
        isCloud = data.get('isCloud')

        if isCloud == '0':  # 云内
            cmsURL = '%s/api/operate_api' % Global_nigixUrl
        else:  # 云外
            cmsURL = '%s/api/operate_api' % Global_cmsUrl
        # 查询
        Version = data.get('Version')
        data = {"type": "versionConfigInfo", "Version": Version, "VersionTS": None, "ConfigLogo": None,
                "operateType": "query", "VersionType": None, "ReleaseTime": None}
        data = json.dumps(data)
        write_log('查询版本配置信息：%s\n' % data)
        try:
            r = requests.get(url=cmsURL, auth=('trade', 'trade32wu'), data=data, headers=Header)
            result = r.json()
            sResult = result.get('result_no')
            if sResult != 0:  # CMS上未维护该产品信息时不检查发布状态
                compileError = result.get('result_info')
                write_log(compileError)
                return True
            else:
                iResult = result.get('result_info')
                write_log('版本配置信息：%s\n' % str(iResult))
        except Exception as e:  # CMS调用异常时不检查发布状态
            result = u"[Error]Git钩子校验，调用CMS版本配置信息查询接口异常，报错：%s。请联系配置管理员。" % str(e)
            write_log('%s\n' % result)
            Sendmail().sendmail(Contents=str(result), address='<EMAIL>', mailsubject="Git钩子校验异常", cc=Global_CC, sender="<EMAIL>", attach=log_files)
            return True
        for key, value in iResult.items():
            if value.get("ReleaseType") == u"已发布":
                write_log(u'按照version(%s)调用cms接口查询版本信息，状态为已发布，请确认！' % Version)
                return False
        else:
            return True

    def getCmEmail(self, product_name, isCloud):
        """
        功能说明：
            获取产品对应的配管邮箱
        参数说明：
            product_name--产品名
        """
        Header = {
            'Content-Type': 'application/json',
        }
        if isCloud == '0':  # 云内
            cmsURL = '%s/api/operate_api' % Global_nigixUrl
        else:  # 云外
            cmsURL = '%s/api/operate_api' % Global_cmsUrl
        # 查询
        data = {"type": "query_product_info_by_id", "product_id": str(product_name)}
        data = json.dumps(data)
        try:
            r = requests.get(url=cmsURL, auth=('trade', 'trade32wu'), data=data, headers=Header)
            result = r.json()
            sResult = result.get('result_no')
            if sResult != 0:  # CMS上未维护该产品信息时不检查发布状态
                compileError = result.get('result_info')
                write_log(compileError)
                return False
            else:
                iResult = result.get('result_info', {})
                cm_name = iResult.get('cm_name', '')
                if cm_name != '':
                    cm_email = cm_name + '@hundsun.com'  # 获取配管邮箱
                    global Global_CC
                    if cm_email not in Global_CC:
                        Global_CC += ',' + cm_email
        except Exception as e:  # CMS调用异常时不检查发布状态
            write_log('查询产品配管邮箱：%s\n' % data)
            result = u"[Error]Git钩子校验，调用CMS查询产品配管邮箱异常，报错：%s。请联系配置管理员。" % str(e)
            write_log('%s\n' % result)
            Sendmail().sendmail(Contents=str(result), address='<EMAIL>', mailsubject="Git钩子校验异常",
                                cc=Global_CC, sender="<EMAIL>", attach=log_files)
        return True

    def getVersionTS(self, commit_file):
        # VersionTS关键字获取：
        # 1、具体版本号，可通过%版本标识1%指定，     dir_match,separate_match,str_replace,str_connect
        # 2、sep_num：/作为分隔字符，取目标路径的第几段sep
        # 3、文件内容字符串替换：读出来trunk.sh文件内容再进行字符串替换，用%VERSIONTS%表示读取出来的内容
        # 4、字符串拼接：读出来trunk.sh文件内容再进行字符串拼接，用%VERSIONTS%表示读取出来的内容
        # 定义工作目录，存放TS接口工具和非临时补丁版本的TS版本号脚本
        VersionTS_value = ""
        sign = False  # 是否匹配到VersionTS标志，匹配目录获取对应的具体版本versionTS，如果匹配到了，直接做后面切割等操作，如果都没匹配到，报错
        VersionTS_value = ''
        write_log(u'获取提交的分支analyse_versionts版本号中……\n')
        for dir, match_rules in self.analyse_versionts.items():  # {"dir":{"trunk":%版本标识1%,"separate_match":"3","str_replace":"V20,-clientV20","str_connect":'model%VERSIONTS%.15210'}}
            key = dir.replace('*', '.*?')  # 支持模糊匹配
            write_log(u'commit_file:%s\n' % commit_file)
            if re.search(key, commit_file):
                sign = True
                n = 0
                # analyse_versionts是个匹配版本规则，字典格式，key为路径dir，传入的参数commit_file可以与dir目录值相匹配时，其中对应的value就是版本号的修改规则：查找，切割，拼接，最终得到真正的VersionTS
                for type, type_value in match_rules.items():  # match_rules:{"1":%版本标识1%,}

                    if type in ["1", "2", "3", "4", "5", "6"]:
                        n = n + 1
                    if type == "1":  # {"1":%版本标识1%,}
                        write_log(u'VersionTS_value:%s\n' % VersionTS_value)
                        if "%VERSIONTS%" in type_value:
                            if VersionTS_value == "":
                                return False, ""
                            type_value = type_value.replace("VERSIONTS", VersionTS_value)
                        VersionTS_value = type_value
                        write_log(u'VersionTS_value:%s\n' % VersionTS_value)
                        key = '%.*?%'
                        list_value = re.findall(key, type_value)
                        for tt in list_value:
                            t_value = tt.strip("%")
                            list_global_versionts = self.global_versionts.get('list', [])
                            for dict_global_versionts in list_global_versionts:
                                version_sign = dict_global_versionts.get('versiont_key', '')
                                version_value = dict_global_versionts.get('versiont_value', '')
                                if t_value == version_sign:  # 通过global_versionts找到对应的版本号
                                    VersionTS_value = VersionTS_value.replace("%" + t_value + "%", version_value)

                    # 对获取到的versionTS接着按照规则处理
                    elif type == "2":  # 规则2：以"/"做切割,取第几位
                        if int(type_value) > 0:
                            type_value = int(type_value) - 1
                        VersionTS_value = commit_file.split("/")[int(type_value)]
                    elif type == "3":  # 规则3：字符串替换
                        replace_src = type_value.split(",")[0]
                        replace_target = type_value.split(",")[1]
                        VersionTS_value = VersionTS_value.replace(replace_src, replace_target)
                    elif type == "4":  # 规则4：字符串拼接
                        if "%VERSIONTS%" not in type_value:
                            VersionTS_value = VersionTS_value + type_value
                        else:
                            VersionTS_value = type_value.replace("%VERSIONTS%", VersionTS_value)
                            key = '%.*?%'
                            list_value = re.findall(key, VersionTS_value)
                            for i in list_value:
                                list_global_versionts = self.global_versionts.get('list', [])
                                for dict_global_versionts in list_global_versionts:
                                    version_sign = dict_global_versionts.get('versiont_key', '')
                                    version_value = dict_global_versionts.get('versiont_value', '')
                                    if i == "%" + version_sign + "%":  # 通过global_versionts找到对应的版本号
                                        VersionTS_value = VersionTS_value.replace(i, version_value)
                    elif type == "5":
                        dir = commit_file  # 递交目录
                        write_log(u"递交目录：" + commit_file)
                        filePath = type_value.get("filePath")
                        splitParagraph = type_value.get("splitParagraph")
                        # 读取文件获取英文模块名
                        with open(filePath, "r")as file:
                            moduleInfo_list = file.readlines()
                            for moduleInfo in moduleInfo_list:
                                module = moduleInfo.strip('\n').strip('').split("=")[0]
                                moduleName = moduleInfo.strip('\n').strip('').split("=")[1]
                                if commit_file.find(module) != -1:
                                    moduleNameNew = moduleName  # 有：FA6.0-模块英文名.Vtrunk替换；没有：截取第五段：FA6.0-模块英文名.V第五段
                                    write_log(u"读取英文模块名moduleNameNew为：" + moduleNameNew)
                        if commit_file.find("Trunk") != -1:
                            list_global_versionts = self.global_versionts.get('list', [])
                            for dict_global_versionts in list_global_versionts:
                                version_sign = dict_global_versionts.get('versiont_key', '')
                                version_value = dict_global_versionts.get('versiont_value', '')
                                if version_sign == "Trunk":
                                    trunk_info = version_value
                                    write_log(u"获取的trunk_info为：" + trunk_info)
                                    if trunk_info == None:  # trunk未定义，直接报错退出
                                        return False, ""
                                    VersionTS_value = "FA6.0-%s.V%s" % (moduleNameNew, trunk_info)
                        else:
                            splitParagraph = int(splitParagraph) - 1
                            info = commit_file.split("/")[splitParagraph]
                            VersionTS_value = "FA6.0-%s.V%s" % (moduleNameNew, info)
                    elif type == "6":  # 根据日志获取出对应的修改版本
                        log_arr = self.log_msg.split('\n')
                        for line in log_arr:
                            if line.find(type_value) != -1:
                                modify_version = line.split(type_value)[1].strip().strip(':').strip('：').strip('\n')
                                if modify_version == '':
                                    write_log(u"递交日志上未找到修改版本，请确认")
                                    return False, ""
                                VersionTS_value = modify_version
                    write_log(u"钩子脚本分析获取的VersionTS为：%s\n" % str(VersionTS_value))
                if n != 0:  # 匹配到就退出
                    write_log(u"获取的VersionTS修改版本为：%s\n" % VersionTS_value)
                    break
        if not sign:
            return False, ""
        return True, VersionTS_value.strip()

    # 获取提交的分支版本号
    def get_branch_versionts(self):
        write_log(u'获取提交的分支版本号中……\n')
        versionts = ''
        list_rules = []
        match_info = {}
        analyse_versionts_info = self.analyse_versionts.get('list', [])
        for i in analyse_versionts_info:
            match_repository_flag = False
            repository_info = i.get('version_repository', '')
            if repository_info.strip() == '':
                match_repository_flag = True
            list_repository = self.str2list(repository_info)
            for repository in list_repository:
                repository = repository.strip()
                if repository == "":
                    continue
                repository = self.repository2fullpath(repository)
                if re.search(repository, self.full_path):
                    match_repository_flag = True
                    break
            if match_repository_flag:
                get_version_type = i.get('type_git', '')
                if get_version_type == '':
                    continue
                if get_version_type == '0':
                    write_log(u'分支(%s)对应具体版本号\n' % self.branch)
                    rules = i.get('rules', '')
                    list_rules = self.str2list(rules)
                    write_log(u'版本规则列表：%s\n' % str(list_rules))
                    for j in list_rules:
                        if len(j.split(':')) == 2 and j.split(':')[0] == self.branch:
                            write_log(u'分支匹配的规则是：%s\n' % str(j))
                            versionts = j.split(':')[1]
                            break
                    if versionts != '':
                        if i["sub_git_dev_status"] not in ["", []]:
                            self.dev_state = ','.join(i['sub_git_dev_status'])
                            write_log(u'任务单状态列表：%s\n' % str(self.dev_state))
                        break
                elif get_version_type == '1':
                    write_log(u'分支(%s)字符串替换生成版本号\n' % self.branch)
                    rules = i.get('rules', '').strip()
                    if rules == '':
                        continue
                    if self.branch.find(rules) != -1 and len(rules.split(':')) == 1:
                        versionts = self.branch.replace(rules, '')
                        write_log(u'分支匹配的版本号：%s\n' % versionts)
                        if i["sub_git_dev_status"] not in ["", []]:
                            self.dev_state = ','.join(i['sub_git_dev_status'])
                            write_log(u'任务单状态列表：%s\n' % str(self.dev_state))
                        break
                    elif self.branch.find(rules) != -1 and len(rules.split(':')) == 2:
                        versionts = self.branch.replace(rules.split(':')[0], rules.split(':')[1])
                        write_log(u'分支匹配的版本号：%s\n' % versionts)
                        if i["sub_git_dev_status"] not in ["", []]:
                            self.dev_state = ','.join(i['sub_git_dev_status'])
                            write_log(u'任务单状态列表：%s\n' % str(self.dev_state))
                        break
                    else:
                        continue
                elif get_version_type == '2':
                    write_log(u'分支(%s)切割生成版本号\n' % self.branch)
                    rules = i.get('rules', '').strip()
                    write_log(u'分支分割规则：%s\n' % rules)
                    if rules == '':
                        continue
                    if len(rules.split(',')) == 1:
                        list_branch = self.branch.split('-')
                        if rules == '0' and len(list_branch) >= 2:
                            versionts = '-'.join(list_branch[1:])
                            write_log(u'分支匹配的版本号：%s\n' % versionts)
                            if i["sub_git_dev_status"] not in ["", []]:
                                self.dev_state = ','.join(i['sub_git_dev_status'])
                                write_log(u'任务单状态列表：%s\n' % str(self.dev_state))
                            break
                        elif rules == '1' and len(list_branch) >= 2:
                            versionts = '-'.join(list_branch[:-1])
                            write_log(u'分支匹配的版本号：%s\n' % versionts)
                            if i["sub_git_dev_status"] not in ["", []]:
                                self.dev_state = ','.join(i['sub_git_dev_status'])
                                write_log(u'任务单状态列表：%s\n' % str(self.dev_state))
                            break
                        else:
                            continue
                    elif len(rules.split(',')) == 2:
                        list_branch = self.branch.split(rules.split(',')[0])
                        if rules.split(',')[1] == '0' and len(list_branch) >= 2:
                            versionts = (rules.split(',')[0]).join(list_branch[1:])
                            write_log(u'分支匹配的版本号：%s\n' % versionts)
                            if i["sub_git_dev_status"] not in ["",[]]:
                                self.dev_state = ','.join(i['sub_git_dev_status'])
                                write_log(u'任务单状态列表：%s\n' % str(self.dev_state))
                            break
                        elif rules.split(',')[1] == '1' and len(list_branch) >= 2:
                            versionts = (rules.split(',')[0]).join(list_branch[:-1])
                            write_log(u'分支匹配的版本号：%s\n' % versionts)
                            if i["sub_git_dev_status"] not in ["",[]]:
                                self.dev_state = ','.join(i['sub_git_dev_status'])
                                write_log(u'任务单状态列表：%s\n' % str(self.dev_state))
                            break
                        else:
                            continue
        if versionts == '':
            return False, u'无法获取到分支对应的版本号，请确认'

        # 部分版本号需要加LS
        is_add_ls = match_info.get('is_add_ls', '')
        if is_add_ls == "0":
            versionts = "%s.LS" % versionts
        write_log(u'%s分支对应的版本号:%s\n' % (self.branch, versionts))
        data = {"Version": versionts, "isCloud": str(self.is_cloud)}
        if '.LS' not in versionts:
            if not self.queryVersionConfig(data):
                return False, u'递交目录版本已发布，请确认'
        return True, versionts

    # 限制目录组判断
    def no_commit_check(self):
        write_log(u'限制目录组：%s\n' % str(self.no_commit))
        if self.no_commit == {} or self.no_commit.get('list') == []:
            return True, ""
        list_no_commit_dirs = []
        list_percommit_user = []
        dict_no_allow_user_modifyfile = {}
        list_no_commit_info = self.no_commit.get('list')
        for nocommitinfo in list_no_commit_info:
            list_repository = self.str2list(nocommitinfo.get('no_commit_repository', ''))
            if list_repository == []:
                list_no_commit_dirs.extend(self.str2list(nocommitinfo.get('list_no_commit_path', '')))  # 限定提交的目录
                list_percommit_user.extend(self.str2list(nocommitinfo.get('list_no_commit_user', '')))  # 限定提交目录允许提交人员
            for repository in list_repository:
                repository = self.repository2fullpath(repository)
                if repository == '':
                    list_no_commit_dirs.extend(self.str2list(nocommitinfo.get('list_no_commit_path', '')))  # 限定提交的目录
                    list_percommit_user.extend(self.str2list(nocommitinfo.get('list_no_commit_user', ''))) # 限定提交目录允许提交人员
                elif re.search(repository, self.full_path):
                    list_no_commit_dirs.extend(self.str2list(nocommitinfo.get('list_no_commit_path', '')))  # 限定提交的目录
                    list_percommit_user.extend(self.str2list(nocommitinfo.get('list_no_commit_user', '')))  # 限定提交目录允许提交人员
        for i in self.list_commit_info:
            modifyuser = i.get('modifyuser', [])
            list_modifyfile = i.get('modifyfile', [])
            modifyno = i.get('modifyno', [])
            for modifyfile in list_modifyfile:
                for no_commit_dir in list_no_commit_dirs:
                    if not len(modifyuser) == 1:
                        return False, u"获取用户信息失败：%s" % str(modifyuser)
                    if re.search(no_commit_dir, modifyfile) and  modifyuser[0] not in list_percommit_user:
                        if dict_no_allow_user_modifyfile.has_key(modifyuser[0]):
                            dict_no_allow_user_modifyfile[modifyuser[0]].append(modifyfile)
                        else:
                            dict_no_allow_user_modifyfile[modifyuser[0]] = [modifyfile]
        if dict_no_allow_user_modifyfile != {}:
            error_message = ''
            for key, value in dict_no_allow_user_modifyfile.items():
                error_message += "提交失败！用户(%s)无权限修改文件(%s)\n" % (key, ','.join(value))
            return False, error_message
        else:
            return True, ""

    # 校验（版本及修改单）
    def version_modify_check(self, version, modify_no, ts_platform='0'):
        try:
            sResult, iResult = checkBuilding(versionNo=version, modifynos=modify_no, isCloud=self.is_cloud)
            if not sResult:
                return False, iResult

            if ts_platform == '0':  # 校验TS修改单状态
                if self.check_taskscript_status == '0':  # 校验任务脚本提交状态
                    log_ts_dir = log_dir + os.sep + 'ts_log' + os.sep + datetime.datetime.now().strftime('%Y%m%d.%H%M%S')
                    if not os.path.exists(log_ts_dir):
                        os.mkdir(log_ts_dir)
                    log_file = log_ts_dir + os.sep + 'modify_no.txt'
                    with open(log_file, 'a') as w:
                        w.write(modify_no)
                    write_log("\nmodify_no=%s\n" % modify_no)
                    cmd_str = 'cd %s/TS_SVN/TStool;/opt/jdk1.7.0_79/bin/java -jar ' % work_dir + 'tsSWC.jar -p %s -v %s -c "123456" -m %s -o %s -t "1"' % (
                        self.productId, version, log_file, log_ts_dir)
                    write_log("cmd_str=" + cmd_str + '\n')
                    result = get_stdout(cmd_str)
                    write_log("result=" + result + "333\n")
                    try:
                        if result.find('[Error]:该修改单对应的任务脚本没有合并') != -1:
                            return False, result.split('[Error]:')[1]
                        elif result.find('[Error]') != -1:
                            return False, result.split('[Error]')[1]
                        else:
                            return True, ''
                    except Exception as err:
                        return False, str(err)
                else:
                    sResult, iResult = ts_query_modify(modifynos=modify_no, productId=self.productId,
                                                       modifyStatus=self.ts_state, versionNo=version,
                                                       check_task_status=self.check_task_status)
                    if not sResult:
                        iResult = u"TS修改单查询异常：%s\n" % iResult
                        write_log(iResult)
                        return False, iResult
            else:  # 校验效能任务单状态
                if self.tproductId == '':
                    iResult = u'校验效能任务单状态，tProductID协同技术编号不能为空\n'
                    return False, iResult
                write_log(u'校验效能任务单状态(tproductId：%s, modifyNum：%s, modifyStatus：%s, versionNo：%s, is_cloud：%s)\n' % (self.tproductId, modify_no, self.dev_state, version, self.is_cloud))
                sResult, iResult = queryDevTask(tproductId=self.tproductId, modifyNum=modify_no,
                                                modifyStatus=self.dev_state, versionNo=version, is_cloud=self.is_cloud)
                if not sResult:
                    return False, iResult
            return True, u"修改单状态校验成功"
        except Exception as err:
            return False, str(err)

    def getModifyNos(self, modifyNos):
        """
        修改单 T或M开头的12位数据 - 数据
        """
        if 'M20' not in modifyNos and 'T' not in modifyNos:
            return ''
        list_modify_no = []

        modifyNos=modifyNos.replace(':',',').replace('：',',').replace(' ',',').replace('.',',').replace('；',',').replace(';',',').replace('，',',').replace('。',',').replace('；',',').replace('、',',').replace(',,',',').strip()
        list_modify_no_tmp = modifyNos.split(',')
        for i in list_modify_no_tmp:
            i = i.strip()
            if re.match('[M,T].*[0-9]{12}-*[0-9]*', i): # 效能12位数字编号
                list_modify_no.append(i)

        modifyNos = ','.join(list(set(list_modify_no)))

        return modifyNos
        
    def parse_commit_info(self, commit_info):
        gitlog = commit_info.split('------------------------------------------------------------------------\n')
        pattern_gitlogo = re.compile(r'A\t|D\t|M\t|R\t|C\t|T\t')
        pattern_gitlogo_del = re.compile(r'A\t|M\t|R\t|C\t|T\t')
        pattern_modifyNo = re.compile('\[TS修改单编号/效能任务编号\]|修改单编号')
        list_gitlog = []
        for logs in gitlog:
            if logs.strip() != '':
                modify_no = ''
                modifyuser = ''
                dict_gitlog = {'modifyuser': [], 'modifyfile': [], 'modifyno': [], 'modifyfile_nodelete': []}
                list_logs = logs.split('\n')
                frist_line = list_logs[0].split(' | ')  # 获取变更段中的首行，即是包含修改人、版本号的这行
                modifyuser = frist_line[1].split('@')[0]  # 去掉邮箱后缀“hundsun.com”，保持和git获取的统一
                dict_gitlog['modifyuser'].append(modifyuser)
                n = 0
                for log in list_logs[1:]:
                    if re.match(pattern_gitlogo, log):
                        modify_file = log[2:].strip()  # 从第3位开始取，第1位为状态编码，第2位为制表符\t
                        dict_gitlog['modifyfile'].append(modify_file)
                        if re.match(pattern_gitlogo_del, log):
                            dict_gitlog['modifyfile_nodelete'].append(modify_file)
                    # 支持研发中心研发的IDEA插件git日志模板：
                    # [提交类型]:需求/缺陷/代码整理/代码重构/单元测试用例补充
                    # [修改版本]:
                    # [需求单号]:
                    # [缺陷编号]:236395
                    # [TS修改单编号/效能任务编号]:A12345,A12346
                    # [修改内容说明]:取委托sql市场关联
                    elif re.search(pattern_modifyNo, log):
                        # 从[TS修改单编号/效能任务编号]　[修改内容说明]之前的文本内容中获取修改单编号
                        modify_no_tmp = re.sub(pattern_modifyNo, 'STARTSPLITFLAG', log).strip().split('STARTSPLITFLAG')[
                            1]
                        # 如果有[，取[前的内容代表修改单，没有则都取
                        if '[' in modify_no_tmp:
                            modify_no_tmp = \
                                re.sub(u'\[', 'ENDSPLITFLAG', modify_no_tmp, 1).strip().split('ENDSPLITFLAG')[0]
                        modify_no = self.getModifyNos(modify_no_tmp)
                        dict_gitlog['modifyno'].append(modify_no)
                list_gitlog.append(dict_gitlog)
        return list_gitlog


def get_full_path():
    full_path = ''
    config_file = os.path.join(os.path.dirname(os.path.realpath(__file__)), "..", "config")
    with open(config_file, 'r') as fr:
         list_config = fr.readlines()
    for i in list_config:
        i = i.strip()
        if i.startswith("fullpath"):
            full_path = i.split('=')[1].strip()
    return full_path


def zg_check(**kwargs):
    branch = kwargs.get('branch', '')

    commit_file = os.path.join(os.path.dirname(os.path.realpath(__file__)), "../gitlog.txt")  # 递交日志
    with open(commit_file, 'r') as fr:
        commit_info = fr.read()

    diff_file = os.path.join(os.path.dirname(os.path.realpath(__file__)), "../gitdiff.txt")  # 修改内容
    with open(diff_file, 'r', encoding='utf-8', errors='ignore') as fr:
        diff_info = fr.read()

    full_path = get_full_path()

    if branch == '':
        return 1113, u'无法获取到branch，请联系配管'
    if full_path == '':
        return 1113, u'无法获取到full_path，请联系配管'

    now_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')
    # 日志数据处理讨论先入库 后期根据用户查询进行展示
    write_log(u'*************************************************************************************************\n')
    write_log(u'自定义钩子开始校验……\n')
    write_log(u'提交时间：%s\n' % now_time)
    # write_log(u'提交用户：%s\n' % user_name)
    # write_log(u'提交文件：%s\n' % change_files)
    write_log(u'提交日志：\n')
    write_log(u'%s\n' % commit_info)
    write_log(u'------------------------------------------------------------------------\n')

    h = ZGHooks(branch=branch, commit_info=commit_info, full_path=full_path)
    # 获取钩子数据需CMS提供接口
    if not h.run():
        result = u'钩子异常，请联系配管\n'
        write_log(result)
        return 1110, result
    write_log(u'--->钩子正常启动\n')

    # 如果是超级用户操作，就做如下判断
    if h.committer_check():
        write_log(u'超级用户，无需验证\n')
        return 0, u'超级用户，无需验证'
    write_log(u'--->非超级用户 验证继续\n')

    # 限制递交文件类型校验
    sResult, iResult = h.filetypes_check()
    if not sResult:
        write_log(iResult)
        return 1113, iResult
    write_log(u'--->非限制递交文件类型 验证继续\n')

    # 如果是排除的目录，则无需验证
    if h.folder_check():
        write_log(u'排除目录，无需验证\n')
        return 0, u'排除目录，无需验证'
    write_log(u'--->排除目录 验证结束\n')

    # 非允许人员不准提交限制目录
    sResult, iResult = h.no_commit_check()
    if not sResult:
        write_log(iResult)
        return 1111, iResult
    write_log(u'--->非允许人员不准提交限制目录 验证结束\n')

    # 分支是否需要验证
    if not h.branch_check():
        write_log(u'非受控分支，无需验证\n')
        return 0, u'非受控分支，无需验证'
    write_log(u'--->受控分支 验证结束\n')

    # MR场景是否需要校验
    if not h.mr_check():
        write_log(u'配置MR场景，无需验证\n')
        return 0, u'配置MR场景，无需验证'
    write_log(u'--->MR场景受控 验证结束\n')

    # git钩子文件内容校验需要调研下，先不提供功能
    sResult, iResult = h.filecontent_check(diff_info)
    if not sResult:
        write_log(iResult)
        return 1113, iResult
    write_log(u'--->文件内容校验 验证结束\n')

    # 是否需要校验修改单状态，默认需要校验
    # 如果日志里没有修改单编号： 字样报错退出
    sResult, iResult = h.is_check_modifyno()
    if not sResult:
        write_log(u'日志里未找到修改单编号(请确保commit信息中包含[TS修改单编号/效能任务编号]字段)\n')
        return 1112, u"日志里未找到修改单编号(请确保commit信息中包含[TS修改单编号/效能任务编号]字段)"
    write_log(u'--->是否有修改单编号 验证结束\n')
    modify_no = iResult

    # 将不需要检查的修改单剔除
    sResult, iResult = h.remove_exclude_modifynum(modify_no)
    if iResult == '':
        write_log(u'全部是排除修改单，无需验证\n')
        return 0, u'全部是排除修改单，无需验证'
    write_log(u'--->存在非排除修改单 验证结束\n')
    modify_no = iResult

    # 获取提交的TS单号
    status, modify_no_ts, modify_no_dev = h.get_modify_id(modify_no)
    if not status:
        write_log(u'修改单查询异常，报错：%s\n' % modify_no_ts)
        return 1115, u'修改单查询异常，报错原因为%s' % modify_no_ts
    if modify_no_ts != '' and modify_no_dev != '':
        write_log(u'不允许同时提交TS及效能平台单号\n')
        return 1115, u'不允许同时提交TS及效能平台单号'

    # 获取提交的版本
    sResult, iResult = h.get_branch_versionts()
    if not sResult:
        write_log(iResult)
        return 1114, iResult
    write_log(u'--->获取提交版本VersionTS,验证结束\n')
    version = iResult

    write_log(u'modify_no_ts=%s\nmodify_no_dev=%s\n' % (modify_no_ts, modify_no_dev))
    if modify_no_ts != '':
        write_log(u'修改单编号:%s;修改版本:%s\n' % (modify_no_ts, version))
        result, msg = h.version_modify_check(version, modify_no_ts, ts_platform='0')
        if not result:
            write_log(str(msg) + '\n')
            return 1116, str(msg)
    if modify_no_dev != '':
        write_log(u'任务单编号:%s;修改版本:%s\n' % (modify_no_dev, version))
        result, msg = h.version_modify_check(version, modify_no, ts_platform='1')
        if not result:
            write_log(str(msg) + '\n')
            return 1116, str(msg)
    write_log(u'--->修改/任务单校验 验证结束\n')

    # 任务单任务前后台字段修改
    iResult, sResult = h.updateTestRecoder(modify_no_dev)
    if not iResult:
        write_log(str(sResult) + '\n')
        return 1118, str(sResult)

    write_log(u'--->全部规则验证成功，程序结束\n')
    return 0, u'success'


if __name__ == '__main__':
    # sys.argv = ['', 'master']
    status, output = zg_check(branch=sys.argv[1])
    if status != 0:
        branch = sys.argv[1]
        full_path = get_full_path()
        contents = "</br><span style=font-weight:bold><font color=black size='3'>git仓库:%s.git, git分支:%s钩子校验报错：</font></span><br></br><font color=red> %s。</font></br></br>详情见附件！" % (full_path, branch, str(output))
        try:
            sResult = Sendmail().sendmail(Contents=str(contents), address=Global_Address, mailsubject="Git钩子校验失败", cc=Global_CC, sender="<EMAIL>", attach=log_files)
        except:
            pass
    print (status, output)
    sys.exit(status)


