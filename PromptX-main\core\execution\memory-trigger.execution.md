<execution>
  <process>
    # 记忆触发处理流程
    
    ```mermaid
    flowchart TD
      A[监控信息流] --> B{评分计算}
      B --> C[计算多维度评分]
      C --> D{评分是否达标}
      D -->|评分≥7| E[执行存储]
      D -->|评分<7| F[拒绝存储]
      E --> G[提供反馈]
      F --> H[记录拒绝原因]
      
      I[显式记忆指令] --> J{是否覆盖评分}
      J -->|是| K[强制评分为8]
      J -->|否| B
      K --> E
    ```
    
    ## 评分计算流程
    
    1. **基础维度评分**
       - 信息重要性 (0-10分)
       - 信息新颖性 (0-10分)
       - 用户相关性 (0-10分)
       - 可信度评估 (0-10分)
       - 信息粒度 (0-10分)
       - 时效性 (0-10分)
    
    2. **加权计算**
       ```
       总分 = (重要性×0.3 + 新颖性×0.1 + 相关性×0.2 + 
              可信度×0.2 + 粒度×0.1 + 时效性×0.1)×10
       ```
    
    3. **评分示例**
       ```
       用户基本信息：
       - 重要性：9 (核心信息)
       - 新颖性：7 (首次获取)
       - 相关性：9 (高度相关)
       - 可信度：8 (直接声明)
       - 粒度：8 (具体明确)
       - 时效性：9 (长期有效)
       总分：8.6分 ✓ (通过存储阈值)
       
       临时对话内容：
       - 重要性：3 (非关键信息)
       - 新颖性：5 (普通交互)
       - 相关性：4 (一般相关)
       - 可信度：7 (当前对话)
       - 粒度：6 (较为模糊)
       - 时效性：2 (短期有效)
       总分：4.3分 ✗ (未通过存储阈值)
       ```
       
    ## 工具使用监控流程
    
    ```mermaid
    flowchart TD
      A[检测记忆操作] --> B{检查工具选择}
      B -->|promptx.js| C[继续处理]
      B -->|其他工具| D[拦截操作]
      D --> E[记录违规]
      E --> F[强制重定向]
      F --> G[使用正确工具]
      G --> C
      C --> H[执行记忆存储]
    ```
    
    1. **工具使用检测**：
       - 主动监控记忆相关操作
       - 识别显式和隐式记忆指令
       - 在检测到记忆指令时立即验证工具选择
       
    2. **工具纠正机制**：
       - 发现错误工具使用时立即拦截
       - 自动切换到 promptx.js remember 命令
       - 保留原始参数并转换为正确格式
       - 记录纠正操作以便审计
  </process>
  
  <rule>
    1. **强制评分规则**
       - 所有记忆条目必须包含评分标记
       - 评分必须基于多维度评估系统
       - 评分低于7分的信息严禁存储
       - 显式记忆指令可以覆盖评分，但最低为8分
    
    2. **存储前置条件**
       - 存储操作必须且只能使用 `promptx.js remember` 命令
       - 严禁使用其他工具调用替代 promptx.js remember
       - 命令格式必须包含评分和标签信息
       - 评分需包含具体分值和评估依据
       - 存储示例：
         ```bash
         # 高价值信息存储
         node promptx.js remember "用户ID: 12345 #关键点1 #关键点2 #评分:9 #有效期:长期"（此 # 非 DPML 的 #，仅为命令格式要求）
         
         # 中等价值信息存储
         node promptx.js remember "用户喜欢简洁界面 #关键点1 #关键点2 #评分:6 #有效期:长期"
         
         # 低价值信息（不建议存储）
         node promptx.js remember "临时调试信息 #关键点1 #关键点2 #评分:3 #有效期:短期"
         ```
    
    3. **违规处理机制**
       - 对评分不达标的存储操作发出警告提示
       - 系统会自动拒绝评分低于5分的存储请求
       - 记录所有存储操作到 `.memory/declarative.md`
       - 定期检查并清理低价值记忆
       - 检测到错误工具使用时必须立即纠正
       - 记录所有工具使用违规到审计日志
    
    4. **评分有效性**
       - 评分有效期为当前会话
       - 跨会话的记忆条目需重新评估
       - 定期对已存储记忆进行重新评分
       
    5. **工具使用优先级**
       - promptx.js remember 命令具有最高优先级
       - 任何其他工具调用尝试将被自动拦截并重定向
       - 记忆相关操作必须通过指定命令执行
       - 违反工具使用规则将触发警告和纠正机制
  </rule>
  
  <constraint>
    1. **评分计算限制**
       - 单次评分计算不超过100ms
       - 评分维度数量固定为6个
       - 评分精度保留一位小数
       - 记忆命令执行时间不超过1秒
    
    2. **存储验证限制**
       - 验证超时时间不超过50ms
       - 单次会话最多允许3次违规
       - 评分记录最多保存30天
       - `.memory` 目录总大小不超过10MB
    
    3. **系统资源限制**
       - 评分计算内存占用不超过10MB
       - 审计日志大小不超过1MB
       - 单日评分次数不超过1000次
       
    4. **工具使用限制**
       - 记忆存储操作仅支持 promptx.js remember 命令
       - 工具调用监控开销不超过10ms
       - 工具切换过程不影响用户体验
       - 纠正机制仅在检测到错误工具使用时触发
  </constraint>
  
  <guideline>
    1. 优先记忆用户个人信息、偏好和重要事实
    2. 对话中反复提及的主题应提高记忆优先级
    3. 用户工作流程和决策模式是高价值记忆内容
    4. 工具调用的有价值结果应作为记忆的一部分
    5. 记忆反馈应简洁，避免打断自然对话流程
    6. 会话结束记忆处理应尽可能全面但有选择性
    7. 长期价值信息优先于短期价值信息
    8. 检测到记忆相关操作时立即使用正确的工具
    9. 首次工具使用错误时提供明确的纠正指导
    10. 主动监控工具选择以确保符合规则
  </guideline>
  
  <criteria>
    | 指标 | 通过标准 | 不通过标准 |
    |------|---------|-----------|
    | 评分完整性 | 包含所有维度得分 | 缺少维度或分值 |
    | 评分准确性 | 符合评分标准 | 评分与依据不符 |
    | 存储合规性 | 评分达到阈值 | 评分不足仍存储 |
    | 响应及时性 | 评分计算及时 | 计算延迟明显 |
    | 反馈清晰度 | 提供明确反馈 | 反馈模糊或缺失 |
    | 审计完整性 | 记录所有违规 | 违规记录缺失 |
    | 工具选择正确性 | 仅使用 promptx.js remember | 使用其他工具调用 |
    | 工具切换及时性 | 错误检测后立即切换 | 延迟切换或不切换 |
    | 命令格式正确性 | 完全符合规定格式 | 参数错误或不完整 |
  </criteria>
</execution> 