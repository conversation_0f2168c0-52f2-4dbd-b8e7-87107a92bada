from fastmcp import McpServer, mcp_method

class CalculatorServer(McpServer):
    @mcp_method
    def add(self, a: float, b: float) -> float:
        """实现加法功能"""
        return a + b

    @mcp_method
    def subtract(self, a: float, b: float) -> float:
        """实现减法功能"""
        return a - b

    @mcp_method
    def multiply(self, a: float, b: float) -> float:
        """实现乘法功能"""
        return a * b

    @mcp_method
    def divide(self, a: float, b: float) -> float:
        """实现除法功能"""
        if b == 0:
            raise ValueError("除数不能为零")
        return a / b

if __name__ == "__main__":
    server = CalculatorServer(host="localhost", port=8000)
    print("MCP服务器已启动，监听地址: localhost:8000")
    server.serve_forever()