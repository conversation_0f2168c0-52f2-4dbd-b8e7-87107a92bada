<div class="sideMenu">
 <div class="toc--part" id="quarkdown-stdlib-nav-submenu" pageid="quarkdown-stdlib::////PointingToDeclaration//742850071" data-nesting-level="0">
  <div class="toc--row">
   <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/index.html" class="toc--link"><span><span>quarkdown-stdlib</span></span></a>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-0&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Bibliography/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-bibliography.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Bibliography</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib/BibliographyStyle///PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-0-1&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/index.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon enum-class-kt"></span><span><span>Bibliography</span><wbr><span><span>Style</span></span></span></span></a>
    </div>
    <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-1-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib/BibliographyStyle.PLAIN///PointingToDeclaration/{&quot;org.jetbrains.dokka.links.EnumEntryDRIExtra&quot;:{&quot;key&quot;:&quot;org.jetbrains.dokka.links.EnumEntryDRIExtra&quot;}}/742850071" data-nesting-level="3">
     <div class="toc--row">
      <a href="quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/-p-l-a-i-n/index.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon enum-class-kt"></span><span><span><span>PLAIN</span></span></span></span></a>
     </div>
    </div>
    <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-1-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib/BibliographyStyle.IEEETR///PointingToDeclaration/{&quot;org.jetbrains.dokka.links.EnumEntryDRIExtra&quot;:{&quot;key&quot;:&quot;org.jetbrains.dokka.links.EnumEntryDRIExtra&quot;}}/742850071" data-nesting-level="3">
     <div class="toc--row">
      <a href="quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/-i-e-e-e-t-r/index.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon enum-class-kt"></span><span><span><span>IEEETR</span></span></span></span></a>
     </div>
    </div>
    <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-1-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib/BibliographyStyle.ACM///PointingToDeclaration/{&quot;org.jetbrains.dokka.links.EnumEntryDRIExtra&quot;:{&quot;key&quot;:&quot;org.jetbrains.dokka.links.EnumEntryDRIExtra&quot;}}/742850071" data-nesting-level="3">
     <div class="toc--row">
      <a href="quarkdown-stdlib/com.quarkdown.stdlib/-bibliography-style/-a-c-m/index.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon enum-class-kt"></span><span><span><span>ACM</span></span></span></span></a>
     </div>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Collection/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-collection.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Collection</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Data/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-data.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Data</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-4" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Dictionary/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-dictionary.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Dictionary</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-5" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Document/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-document.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Document</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-6" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Ecosystem/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-ecosystem.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Ecosystem</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-7" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Flow/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-flow.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Flow</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-8" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Injection/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-injection.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Injection</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-9" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Layout/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-layout.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Layout</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-10" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Library/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-library.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Library</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-11" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Localization/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-localization.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Localization</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-12" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Logger/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-logger.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Logger</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-13" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Logical/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-logical.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Logical</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-14" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Math/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-math.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Math</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-15" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Mermaid/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-mermaid.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Mermaid</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-16" pageid="quarkdown-stdlib::com.quarkdown.stdlib//NOT_FOUND/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-n-o-t_-f-o-u-n-d.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span>NOT_</span><wbr><span>FOUND</span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-17" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Optionality/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-optionality.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Optionality</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-18" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Slides/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-slides.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Slides</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-19" pageid="quarkdown-stdlib::com.quarkdown.stdlib/Stdlib///PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-stdlib/index.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon object"></span><span><span><span>Stdlib</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-20" pageid="quarkdown-stdlib::com.quarkdown.stdlib//String/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-string.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>String</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-21" pageid="quarkdown-stdlib::com.quarkdown.stdlib//TableComputation/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-table-computation.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span>Table</span><wbr><span><span>Computation</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-22" pageid="quarkdown-stdlib::com.quarkdown.stdlib/TableSortOrder///PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-0-22&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib/-table-sort-order/index.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon enum-class-kt"></span><span><span>Table</span><wbr><span>Sort</span><wbr><span><span>Order</span></span></span></span></a>
    </div>
    <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-22-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib/TableSortOrder.ASCENDING///PointingToDeclaration/{&quot;org.jetbrains.dokka.links.EnumEntryDRIExtra&quot;:{&quot;key&quot;:&quot;org.jetbrains.dokka.links.EnumEntryDRIExtra&quot;}}/742850071" data-nesting-level="3">
     <div class="toc--row">
      <a href="quarkdown-stdlib/com.quarkdown.stdlib/-table-sort-order/-a-s-c-e-n-d-i-n-g/index.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon enum-class-kt"></span><span><span><span>ASCENDING</span></span></span></span></a>
     </div>
    </div>
    <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-22-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib/TableSortOrder.DESCENDING///PointingToDeclaration/{&quot;org.jetbrains.dokka.links.EnumEntryDRIExtra&quot;:{&quot;key&quot;:&quot;org.jetbrains.dokka.links.EnumEntryDRIExtra&quot;}}/742850071" data-nesting-level="3">
     <div class="toc--row">
      <a href="quarkdown-stdlib/com.quarkdown.stdlib/-table-sort-order/-d-e-s-c-e-n-d-i-n-g/index.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon enum-class-kt"></span><span><span><span>DESCENDING</span></span></span></span></a>
     </div>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-0-23" pageid="quarkdown-stdlib::com.quarkdown.stdlib//Text/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib/-text.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon val"></span><span><span><span>Text</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib.external////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-1&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.external/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>external</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-1-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib.external/QdLibraryExporter///PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.external/-qd-library-exporter/index.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon class-kt"></span><span><span>Qd</span><wbr><span>Library</span><wbr><span><span>Exporter</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Bibliography////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-2&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Bibliography/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span><span>Bibliography</span></span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-2-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//bibliography/#com.quarkdown.core.context.MutableContext#kotlin.String#com.quarkdown.stdlib.BibliographyStyle#com.quarkdown.core.ast.InlineMarkdownContent?#kotlin.Boolean/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Bibliography/bibliography.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>bibliography()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-2-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//cite/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Bibliography/cite.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>cite()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Collection////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-3&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Collection</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionAverage/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/average.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>average()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionDistinct/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/distinct.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>distinct()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionFirst/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/first.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>first()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionGet/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]#kotlin.Int#com.quarkdown.core.function.value.DynamicValue/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/getat.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>getat()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-4" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionGroup/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/groupvalues.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>groupvalues()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-5" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionLast/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/last.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>last()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-6" pageid="quarkdown-stdlib::com.quarkdown.stdlib//pair/#com.quarkdown.core.function.value.DynamicValue#com.quarkdown.core.function.value.DynamicValue/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/pair.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>pair()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-7" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionReverse/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/reversed.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>reversed()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-8" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionSecond/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/second.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>second()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-9" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionSize/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/size.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>size()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-10" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionSorted/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]#com.quarkdown.core.function.value.data.Lambda?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/sorted.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>sorted()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-11" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionSumAll/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/sumall.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>sumall()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-3-12" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collectionThird/#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Collection/third.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>third()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-4" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Data////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-4&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Data/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Data</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-4-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//csv/#com.quarkdown.core.context.Context#kotlin.String#kotlin.String?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Data/csv.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>csv()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-4-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//read/#com.quarkdown.core.context.Context#kotlin.String#com.quarkdown.core.function.value.data.Range/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Data/read.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>read()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-5" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Dictionary////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-5&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Dictionary/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Dictionary</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-5-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//dictionary/#kotlin.collections.Map[kotlin.String,com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Dictionary/dictionary.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>dictionary()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-5-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//dictionaryGet/#kotlin.String#kotlin.collections.Map[kotlin.String,com.quarkdown.core.function.value.OutputValue[*]]#com.quarkdown.core.function.value.DynamicValue/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Dictionary/get.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>get()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Document////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-6&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Document</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//autoPageBreak/#com.quarkdown.core.context.MutableContext#kotlin.Int/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/autopagebreak.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>autopagebreak()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//captionPosition/#com.quarkdown.core.context.Context#com.quarkdown.core.document.layout.caption.CaptionPosition?#com.quarkdown.core.document.layout.caption.CaptionPosition?#com.quarkdown.core.document.layout.caption.CaptionPosition?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/captionposition.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>captionposition()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//currentPage/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/currentpage.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>currentpage()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//docAuthor/#com.quarkdown.core.context.Context#kotlin.String?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/docauthor.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>docauthor()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-4" pageid="quarkdown-stdlib::com.quarkdown.stdlib//docAuthors/#com.quarkdown.core.context.Context#kotlin.collections.Map[kotlin.String,com.quarkdown.core.function.value.DictionaryValue[com.quarkdown.core.function.value.OutputValue[kotlin.String]]]?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/docauthors.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>docauthors()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-5" pageid="quarkdown-stdlib::com.quarkdown.stdlib//docLanguage/#com.quarkdown.core.context.Context#kotlin.String?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/doclang.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>doclang()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-6" pageid="quarkdown-stdlib::com.quarkdown.stdlib//docName/#com.quarkdown.core.context.Context#kotlin.String?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/docname.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>docname()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-7" pageid="quarkdown-stdlib::com.quarkdown.stdlib//docType/#com.quarkdown.core.context.Context#com.quarkdown.core.document.DocumentType?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/doctype.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>doctype()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-8" pageid="quarkdown-stdlib::com.quarkdown.stdlib//footer/#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/footer.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>footer()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-9" pageid="quarkdown-stdlib::com.quarkdown.stdlib//marker/#com.quarkdown.core.ast.InlineMarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/marker.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>marker()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-10" pageid="quarkdown-stdlib::com.quarkdown.stdlib//disableAutoPageBreak/#com.quarkdown.core.context.MutableContext/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/noautopagebreak.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>noautopagebreak()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-11" pageid="quarkdown-stdlib::com.quarkdown.stdlib//disableNumbering/#com.quarkdown.core.context.Context/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/nonumbering.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>nonumbering()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-12" pageid="quarkdown-stdlib::com.quarkdown.stdlib//numbering/#com.quarkdown.core.context.Context#kotlin.collections.Map[kotlin.String,com.quarkdown.core.function.value.Value[kotlin.String]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/numbering.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>numbering()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-13" pageid="quarkdown-stdlib::com.quarkdown.stdlib//pageFormat/#com.quarkdown.core.context.Context#com.quarkdown.core.document.layout.page.PageSizeFormat?#com.quarkdown.core.document.layout.page.PageOrientation#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Sizes?#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?#com.quarkdown.core.misc.color.Color?#kotlin.Int?#com.quarkdown.core.ast.quarkdown.block.Container.TextAlignment?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/pageformat.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>pageformat()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-14" pageid="quarkdown-stdlib::com.quarkdown.stdlib//pageMarginContent/#com.quarkdown.core.document.layout.page.PageMarginPosition#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/pagemargin.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>pagemargin()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-15" pageid="quarkdown-stdlib::com.quarkdown.stdlib//paragraphStyle/#com.quarkdown.core.context.Context#kotlin.Number?#kotlin.Number?#kotlin.Number?#kotlin.Number?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/paragraphstyle.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>paragraphstyle()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-16" pageid="quarkdown-stdlib::com.quarkdown.stdlib//tableOfContents/#com.quarkdown.core.ast.InlineMarkdownContent?#kotlin.Int#com.quarkdown.core.ast.InlineMarkdownContent?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/tableofcontents.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>tableofcontents()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-17" pageid="quarkdown-stdlib::com.quarkdown.stdlib//texMacro/#com.quarkdown.core.context.Context#kotlin.String#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/texmacro.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>texmacro()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-18" pageid="quarkdown-stdlib::com.quarkdown.stdlib//theme/#com.quarkdown.core.context.Context#kotlin.String?#kotlin.String?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/theme.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>theme()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-6-19" pageid="quarkdown-stdlib::com.quarkdown.stdlib//totalPages/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Document/totalpages.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>totalpages()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-7" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Ecosystem////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-7&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Ecosystem/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Ecosystem</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-7-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//include/#com.quarkdown.core.context.MutableContext#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Ecosystem/include.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>include()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-7-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//includeAll/#com.quarkdown.core.context.MutableContext#kotlin.collections.Iterable[com.quarkdown.core.function.value.Value[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Ecosystem/includeall.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>includeall()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-8" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Flow////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-8&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Flow</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-8-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//forEach/#kotlin.collections.Iterable[com.quarkdown.core.function.value.Value[*]]#com.quarkdown.core.function.value.data.Lambda/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/foreach.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>foreach()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-8-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//function/#com.quarkdown.core.context.MutableContext#kotlin.String#com.quarkdown.core.function.value.data.Lambda/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/function.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>function()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-8-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//if/#kotlin.Boolean#com.quarkdown.core.function.value.data.Lambda/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/if.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>if()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-8-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//ifNot/#kotlin.Boolean#com.quarkdown.core.function.value.data.Lambda/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/ifnot.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>ifnot()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-8-4" pageid="quarkdown-stdlib::com.quarkdown.stdlib//let/#com.quarkdown.core.function.value.DynamicValue#com.quarkdown.core.function.value.data.Lambda/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/let.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>let()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-8-5" pageid="quarkdown-stdlib::com.quarkdown.stdlib//node/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/node.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>node()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-8-6" pageid="quarkdown-stdlib::com.quarkdown.stdlib//repeat/#kotlin.Int#com.quarkdown.core.function.value.data.Lambda/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/repeat.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>repeat()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-8-7" pageid="quarkdown-stdlib::com.quarkdown.stdlib//variable/#com.quarkdown.core.context.MutableContext#kotlin.String#com.quarkdown.core.function.value.DynamicValue/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Flow/var.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>var()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-9" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Injection////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-9&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Injection/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Injection</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-9-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//html/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Injection/html.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>html()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Layout////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-10&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Layout</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//align/#com.quarkdown.core.ast.quarkdown.block.Container.Alignment#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/align.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>align()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//box/#com.quarkdown.core.context.Context#com.quarkdown.core.ast.InlineMarkdownContent?#com.quarkdown.core.ast.quarkdown.block.Box.Type#com.quarkdown.core.document.size.Size?#com.quarkdown.core.misc.color.Color?#com.quarkdown.core.misc.color.Color?#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/box.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>box()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//center/#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/center.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>center()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//clip/#com.quarkdown.core.ast.quarkdown.block.Clipped.Clip#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/clip.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>clip()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-4" pageid="quarkdown-stdlib::com.quarkdown.stdlib//collapse/#com.quarkdown.core.ast.InlineMarkdownContent#kotlin.Boolean#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/collapse.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>collapse()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-5" pageid="quarkdown-stdlib::com.quarkdown.stdlib//column/#com.quarkdown.core.ast.quarkdown.block.Stacked.MainAxisAlignment#com.quarkdown.core.ast.quarkdown.block.Stacked.CrossAxisAlignment#com.quarkdown.core.document.size.Size?#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/column.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>column()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-6" pageid="quarkdown-stdlib::com.quarkdown.stdlib//container/#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?#kotlin.Boolean#com.quarkdown.core.misc.color.Color?#com.quarkdown.core.misc.color.Color?#com.quarkdown.core.misc.color.Color?#com.quarkdown.core.document.size.Sizes?#com.quarkdown.core.ast.quarkdown.block.Container.BorderStyle?#com.quarkdown.core.document.size.Sizes?#com.quarkdown.core.document.size.Sizes?#com.quarkdown.core.document.size.Sizes?#com.quarkdown.core.ast.quarkdown.block.Container.Alignment?#com.quarkdown.core.ast.quarkdown.block.Container.TextAlignment?#com.quarkdown.core.ast.quarkdown.block.Container.FloatAlignment?#com.quarkdown.core.ast.MarkdownContent?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/container.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>container()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-7" pageid="quarkdown-stdlib::com.quarkdown.stdlib//figure/#kotlin.String?#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/figure.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>figure()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-8" pageid="quarkdown-stdlib::com.quarkdown.stdlib//float/#com.quarkdown.core.ast.quarkdown.block.Container.FloatAlignment#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/float.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>float()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-9" pageid="quarkdown-stdlib::com.quarkdown.stdlib//fullColumnSpan/#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/fullspan.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>fullspan()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-10" pageid="quarkdown-stdlib::com.quarkdown.stdlib//grid/#kotlin.Int#com.quarkdown.core.ast.quarkdown.block.Stacked.MainAxisAlignment#com.quarkdown.core.ast.quarkdown.block.Stacked.CrossAxisAlignment#com.quarkdown.core.document.size.Size?#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/grid.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>grid()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-11" pageid="quarkdown-stdlib::com.quarkdown.stdlib//numbered/#kotlin.String#com.quarkdown.core.function.value.data.Lambda/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/numbered.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>numbered()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-12" pageid="quarkdown-stdlib::com.quarkdown.stdlib//row/#com.quarkdown.core.ast.quarkdown.block.Stacked.MainAxisAlignment#com.quarkdown.core.ast.quarkdown.block.Stacked.CrossAxisAlignment#com.quarkdown.core.document.size.Size?#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/row.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>row()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-13" pageid="quarkdown-stdlib::com.quarkdown.stdlib//table/#com.quarkdown.core.context.Context#kotlin.collections.Iterable[com.quarkdown.core.function.value.Value[kotlin.String]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/table.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>table()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-14" pageid="quarkdown-stdlib::com.quarkdown.stdlib//inlineCollapse/#com.quarkdown.core.ast.InlineMarkdownContent#com.quarkdown.core.ast.InlineMarkdownContent#kotlin.Boolean/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/textcollapse.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>textcollapse()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-15" pageid="quarkdown-stdlib::com.quarkdown.stdlib//toDo/#com.quarkdown.core.context.Context#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/todo.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>todo()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-10-16" pageid="quarkdown-stdlib::com.quarkdown.stdlib//whitespace/#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Layout/whitespace.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>whitespace()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-11" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Library////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-11&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Library/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Library</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-11-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//functionExists/#com.quarkdown.core.context.Context#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Library/functionexists.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>functionexists()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-11-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//libraryExists/#com.quarkdown.core.context.Context#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Library/libexists.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>libexists()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-11-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//libraryFunctions/#com.quarkdown.core.context.Context#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Library/libfunctions.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>libfunctions()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-11-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//libraries/#com.quarkdown.core.context.Context/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Library/libraries.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>libraries()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-12" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Localization////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-12&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Localization/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span><span>Localization</span></span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-12-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//localization/#com.quarkdown.core.context.MutableContext#kotlin.String#kotlin.Boolean#kotlin.collections.Map[kotlin.String,com.quarkdown.core.function.value.DictionaryValue[com.quarkdown.core.function.value.OutputValue[kotlin.String]]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Localization/localization.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>localization()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-12-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//localize/#com.quarkdown.core.context.Context#kotlin.String#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Localization/localize.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>localize()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-13" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Logger////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-13&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Logger/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Logger</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-13-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//debug/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Logger/debug.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>debug()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-13-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//error/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Logger/error.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>error()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-13-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//log/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Logger/log.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>log()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-14" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Logical////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-14&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Logical/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Logical</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-14-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//equals/#com.quarkdown.core.function.value.DynamicValue#com.quarkdown.core.function.value.DynamicValue/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Logical/equals.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>equals()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-14-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//isGreater/#kotlin.Number#kotlin.Number#kotlin.Boolean/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Logical/isgreater.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>isgreater()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-14-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//isLower/#kotlin.Number#kotlin.Number#kotlin.Boolean/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Logical/islower.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>islower()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-14-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//not/#kotlin.Boolean/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Logical/not.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>not()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Math////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-15&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Math</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//abs/#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/abs.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>abs()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//cos/#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/cos.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>cos()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//divide/#kotlin.Number#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/divide.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>divide()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//isEven/#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/iseven.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>iseven()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-4" pageid="quarkdown-stdlib::com.quarkdown.stdlib//logn/#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/logn.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>logn()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-5" pageid="quarkdown-stdlib::com.quarkdown.stdlib//multiply/#kotlin.Number#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/multiply.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>multiply()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-6" pageid="quarkdown-stdlib::com.quarkdown.stdlib//negate/#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/negate.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>negate()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-7" pageid="quarkdown-stdlib::com.quarkdown.stdlib//pi/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/pi.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>pi()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-8" pageid="quarkdown-stdlib::com.quarkdown.stdlib//pow/#kotlin.Number#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/pow.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>pow()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-9" pageid="quarkdown-stdlib::com.quarkdown.stdlib//range/#kotlin.Number?#kotlin.Number?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/range.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>range()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-10" pageid="quarkdown-stdlib::com.quarkdown.stdlib//rem/#kotlin.Number#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/rem.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>rem()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-11" pageid="quarkdown-stdlib::com.quarkdown.stdlib//round/#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/round.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>round()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-12" pageid="quarkdown-stdlib::com.quarkdown.stdlib//sin/#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/sin.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>sin()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-13" pageid="quarkdown-stdlib::com.quarkdown.stdlib//sqrt/#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/sqrt.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>sqrt()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-14" pageid="quarkdown-stdlib::com.quarkdown.stdlib//subtract/#kotlin.Number#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/subtract.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>subtract()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-15" pageid="quarkdown-stdlib::com.quarkdown.stdlib//sum/#kotlin.Number#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/sum.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>sum()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-16" pageid="quarkdown-stdlib::com.quarkdown.stdlib//tan/#kotlin.Number/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/tan.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>tan()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-15-17" pageid="quarkdown-stdlib::com.quarkdown.stdlib//truncate/#kotlin.Number#kotlin.Int/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Math/truncate.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>truncate()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-16" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Mermaid////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-16&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Mermaid/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Mermaid</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-16-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//mermaid/#kotlin.String?#com.quarkdown.core.function.value.data.EvaluableString/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Mermaid/mermaid.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>mermaid()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-16-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//xyChart/#kotlin.Boolean#kotlin.Boolean#kotlin.String?#com.quarkdown.core.function.value.data.Range?#kotlin.collections.Iterable[com.quarkdown.core.function.value.Value[*]]?#kotlin.String?#com.quarkdown.core.function.value.data.Range?#kotlin.String?#kotlin.collections.Iterable[com.quarkdown.core.function.value.OutputValue[*]]/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Mermaid/xychart.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>xychart()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-17" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Optionality////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-17&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span><span>Optionality</span></span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-17-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//ifPresent/#com.quarkdown.core.function.value.DynamicValue#com.quarkdown.core.function.value.data.Lambda/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/ifpresent.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>ifpresent()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-17-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//isNone/#com.quarkdown.core.function.value.DynamicValue/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/isnone.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>isnone()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-17-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//none/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/none.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>none()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-17-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//otherwise/#com.quarkdown.core.function.value.DynamicValue#com.quarkdown.core.function.value.DynamicValue/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/otherwise.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>otherwise()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-17-4" pageid="quarkdown-stdlib::com.quarkdown.stdlib//takeIf/#com.quarkdown.core.function.value.DynamicValue#com.quarkdown.core.function.value.data.Lambda/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Optionality/takeif.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>takeif()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-18" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Slides////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-18&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Slides/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Slides</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-18-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//fragment/#com.quarkdown.core.ast.quarkdown.block.SlidesFragment.Behavior#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Slides/fragment.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>fragment()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-18-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//setSlidesConfiguration/#kotlin.Boolean?#kotlin.Boolean?#com.quarkdown.core.document.slides.Transition.Style?#com.quarkdown.core.document.slides.Transition.Speed/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Slides/slides.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>slides()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-19" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.String////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-19&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.String/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>String</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-19-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//capitalize/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.String/capitalize.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>capitalize()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-19-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//concatenate/#kotlin.String#kotlin.String#kotlin.Boolean/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.String/concatenate.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>concatenate()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-19-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//isEmpty/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.String/isempty.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>isempty()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-19-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//isNotEmpty/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.String/isnotempty.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>isnotempty()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-19-4" pageid="quarkdown-stdlib::com.quarkdown.stdlib//lowercase/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.String/lowercase.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>lowercase()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-19-5" pageid="quarkdown-stdlib::com.quarkdown.stdlib//string/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.String/string.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>string()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-19-6" pageid="quarkdown-stdlib::com.quarkdown.stdlib//uppercase/#kotlin.String/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.String/uppercase.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>uppercase()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-20" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.TableComputation////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-20&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Table</span><wbr><span><span>Computation</span></span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-20-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//tableColumn/#kotlin.Int#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/tablecolumn.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>tablecolumn()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-20-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//tableColumns/#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/tablecolumns.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>tablecolumns()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-20-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//tableCompute/#kotlin.Int#com.quarkdown.core.function.value.data.Lambda#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/tablecompute.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>tablecompute()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-20-3" pageid="quarkdown-stdlib::com.quarkdown.stdlib//tableFilter/#kotlin.Int#com.quarkdown.core.function.value.data.Lambda#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/tablefilter.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>tablefilter()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-20-4" pageid="quarkdown-stdlib::com.quarkdown.stdlib//tableSort/#kotlin.Int#com.quarkdown.stdlib.TableSortOrder#com.quarkdown.core.ast.MarkdownContent/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.TableComputation/tablesort.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>tablesort()</span></span></span></span></a>
    </div>
   </div>
  </div>
  <div class="toc--part" id="quarkdown-stdlib-nav-submenu-21" pageid="quarkdown-stdlib::com.quarkdown.stdlib.module.Text////PointingToDeclaration//742850071" data-nesting-level="1">
   <div class="toc--row">
    <button class="toc--button" onclick="document.getElementById(&quot;quarkdown-stdlib-nav-submenu-21&quot;).classList.toggle(&quot;toc--part_hidden&quot;);"></button><a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Text/index.html" class="toc--link"><span>com.</span><wbr><span>quarkdown.</span><wbr><span>stdlib.</span><wbr><span>module.</span><wbr><span>Text</span></a>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-21-0" pageid="quarkdown-stdlib::com.quarkdown.stdlib//code/#kotlin.String?#kotlin.Boolean#com.quarkdown.core.function.value.data.Range?#com.quarkdown.core.function.value.data.EvaluableString/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Text/code.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>code()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-21-1" pageid="quarkdown-stdlib::com.quarkdown.stdlib//loremIpsum/#/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Text/loremipsum.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>loremipsum()</span></span></span></span></a>
    </div>
   </div>
   <div class="toc--part" id="quarkdown-stdlib-nav-submenu-21-2" pageid="quarkdown-stdlib::com.quarkdown.stdlib//text/#com.quarkdown.core.ast.InlineMarkdownContent#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Size?#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Weight?#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Style?#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Decoration?#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Case?#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Variant?#com.quarkdown.core.misc.color.Color?#kotlin.String?/PointingToDeclaration//742850071" data-nesting-level="2">
    <div class="toc--row">
     <a href="quarkdown-stdlib/com.quarkdown.stdlib.module.Text/text.html" class="toc--link"><span class="toc--link-grid"><span class="toc--icon function"></span><span><span><span>text()</span></span></span></span></a>
    </div>
   </div>
  </div>
 </div>
</div>
