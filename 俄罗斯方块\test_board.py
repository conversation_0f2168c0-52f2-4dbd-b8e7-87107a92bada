# Board类的单元测试
import unittest
from board import Board
from tetromino import Tetromino
from config import GAME_CONFIG

class TestBoard(unittest.TestCase):
    """Board类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.board = Board()
        self.tetromino_i = Tetromino('I')
        self.tetromino_o = Tetromino('O')
        self.tetromino_t = Tetromino('T')
    
    def test_initialization(self):
        """测试初始化"""
        # 测试默认尺寸
        board = Board()
        self.assertEqual(board.width, GAME_CONFIG['BOARD_WIDTH'])
        self.assertEqual(board.height, GAME_CONFIG['BOARD_HEIGHT'])
        
        # 测试自定义尺寸
        custom_board = Board(15, 25)
        self.assertEqual(custom_board.width, 15)
        self.assertEqual(custom_board.height, 25)
        
        # 测试初始状态为空
        self.assertEqual(board.get_filled_cells_count(), 0)
        self.assertTrue(all(cell is None for row in board.grid for cell in row))
    
    def test_valid_position_checking(self):
        """测试位置有效性检查"""
        # 测试有效位置
        self.assertTrue(self.board.is_valid_position(self.tetromino_i, 5, 5))
        
        # 测试左边界 - I形状在(-3,5)时，方块会在(-1,6)，这会越界
        self.assertFalse(self.board.is_valid_position(self.tetromino_i, -3, 5))
        
        # 测试右边界 - I形状在(8,5)时，方块会在(10,6)，这会越界
        self.assertFalse(self.board.is_valid_position(self.tetromino_i, 8, 5))
        
        # 测试底部边界 - I形状在(5,17)时，方块会在(7,18)到(7,21)，最后一个会越界
        self.assertFalse(self.board.is_valid_position(self.tetromino_i, 5, 17))
        
        # 测试顶部上方（应该允许）
        self.assertTrue(self.board.is_valid_position(self.tetromino_i, 5, -2))
    
    def test_movement_checking(self):
        """测试移动检查功能"""
        x, y = 5, 10
        
        # 测试基本移动检查
        self.assertTrue(self.board.can_move_left(self.tetromino_i, x, y))
        self.assertTrue(self.board.can_move_right(self.tetromino_i, x, y))
        self.assertTrue(self.board.can_move_down(self.tetromino_i, x, y))
        
        # 测试边界限制 - 考虑I形状的实际坐标
        # I形状在(0,y)位置时，实际方块在(2,y+1)到(2,y+4)，所以向左移动到(-1,y)会导致方块在(1,y+1)，这是有效的
        # 我们需要找到真正会导致越界的位置
        self.assertFalse(self.board.can_move_left(self.tetromino_i, -2, y))  # 这会导致方块在(-2+2=0, y+1)，但向左移动会到(-1,y+1)，仍然有效
        
        # 对于右边界，I形状在位置(7,y)时，方块在(9,y+1)，向右移动会到(10,y+1)，这会越界
        self.assertFalse(self.board.can_move_right(self.tetromino_i, 7, y))
        
        # 对于底部边界，I形状在位置(x,16)时，方块在(x+2,17)到(x+2,20)，向下移动会越界
        self.assertFalse(self.board.can_move_down(self.tetromino_i, x, 16))
    
    def test_rotation_checking(self):
        """测试旋转检查功能"""
        # I形状可以旋转
        self.assertTrue(self.board.can_rotate(self.tetromino_i, 5, 5))
        
        # O形状不能旋转
        self.assertFalse(self.board.can_rotate(self.tetromino_o, 5, 5))
        
        # T形状可以旋转
        self.assertTrue(self.board.can_rotate(self.tetromino_t, 5, 5))
    
    def test_wall_kick(self):
        """测试踢墙算法"""
        # 在边界附近测试踢墙
        success, new_x, new_y = self.board.try_wall_kick(self.tetromino_i, 0, 5)
        
        # 应该能够通过踢墙完成旋转
        if self.tetromino_i.can_rotate():
            self.assertTrue(success or new_x != 0 or new_y != 5)
    
    def test_tetromino_placement(self):
        """测试方块放置"""
        # 测试成功放置
        result = self.board.place_tetromino(self.tetromino_i, 5, 5)
        self.assertTrue(result)
        self.assertGreater(self.board.get_filled_cells_count(), 0)
        
        # 测试在已占用位置放置
        result2 = self.board.place_tetromino(self.tetromino_i, 5, 5)
        self.assertFalse(result2)
    
    def test_drop_position(self):
        """测试下落位置计算"""
        # 在空板上测试
        drop_y = self.board.get_drop_position(self.tetromino_i, 5, 0)
        self.assertGreaterEqual(drop_y, 0)
        self.assertLess(drop_y, self.board.height)
        
        # 放置一个方块后再测试 - 使用更合适的位置
        self.board.place_tetromino(self.tetromino_o, 5, 16)  # 放在更高的位置
        new_drop_y = self.board.get_drop_position(self.tetromino_i, 5, 0)
        self.assertLessEqual(new_drop_y, drop_y)  # 应该小于等于原来的位置
    
    def test_line_detection(self):
        """测试行检测功能"""
        # 测试空行
        self.assertTrue(self.board.is_line_empty(0))
        self.assertFalse(self.board.is_line_full(0))
        
        # 填满一行
        for x in range(self.board.width):
            self.board.set_cell(x, 19, (255, 0, 0))
        
        self.assertTrue(self.board.is_line_full(19))
        self.assertFalse(self.board.is_line_empty(19))
    
    def test_line_clearing(self):
        """测试行消除功能"""
        # 填满底部两行
        for y in [18, 19]:
            for x in range(self.board.width):
                self.board.set_cell(x, y, (255, 0, 0))
        
        # 在中间放一些方块
        self.board.set_cell(5, 17, (0, 255, 0))
        
        initial_filled = self.board.get_filled_cells_count()
        lines_cleared, cleared_lines = self.board.clear_lines()
        
        # 应该清除了2行
        self.assertEqual(lines_cleared, 2)
        self.assertEqual(len(cleared_lines), 2)
        
        # 填充的格子数应该减少
        self.assertLess(self.board.get_filled_cells_count(), initial_filled)
        
        # 中间的方块应该下移
        self.assertIsNotNone(self.board.get_cell(5, 19))
    
    def test_height_profile(self):
        """测试高度轮廓计算"""
        # 空板的高度轮廓应该全为0
        profile = self.board.get_height_profile()
        self.assertEqual(len(profile), self.board.width)
        self.assertTrue(all(h == 0 for h in profile))
        
        # 放置一些方块
        self.board.set_cell(0, 19, (255, 0, 0))  # 第0列高度1
        self.board.set_cell(1, 18, (255, 0, 0))  # 第1列高度2
        self.board.set_cell(1, 19, (255, 0, 0))
        
        new_profile = self.board.get_height_profile()
        self.assertEqual(new_profile[0], 1)
        self.assertEqual(new_profile[1], 2)
        self.assertEqual(new_profile[2], 0)
    
    def test_game_over_detection(self):
        """测试游戏结束检测"""
        # 空板不应该游戏结束
        self.assertFalse(self.board.is_game_over())
        
        # 在顶部放置方块
        self.board.set_cell(5, 0, (255, 0, 0))
        self.assertTrue(self.board.is_game_over())
        
        # 测试方块无法放置的情况 - 在I形状会占用的位置放置方块
        self.board.clear_board()
        # I形状在(5,0)时会占用(7,1)，所以在那里放置方块
        self.board.set_cell(7, 1, (255, 0, 0))
        self.assertTrue(self.board.is_game_over(self.tetromino_i, 5, 0))
    
    def test_board_operations(self):
        """测试游戏板操作"""
        # 测试获取和设置格子
        self.assertIsNone(self.board.get_cell(5, 5))
        
        color = (255, 0, 0)
        self.board.set_cell(5, 5, color)
        self.assertEqual(self.board.get_cell(5, 5), color)
        
        # 测试边界外访问
        self.assertIsNone(self.board.get_cell(-1, 5))
        self.assertIsNone(self.board.get_cell(5, -1))
        self.assertIsNone(self.board.get_cell(self.board.width, 5))
        self.assertIsNone(self.board.get_cell(5, self.board.height))
    
    def test_board_copy(self):
        """测试游戏板复制"""
        # 放置一些方块
        self.board.set_cell(5, 5, (255, 0, 0))
        self.board.set_cell(6, 6, (0, 255, 0))
        
        # 复制游戏板
        board_copy = self.board.copy()
        
        # 应该有相同的内容
        self.assertEqual(board_copy.get_cell(5, 5), self.board.get_cell(5, 5))
        self.assertEqual(board_copy.get_cell(6, 6), self.board.get_cell(6, 6))
        self.assertEqual(board_copy.get_filled_cells_count(), self.board.get_filled_cells_count())
        
        # 但应该是不同的对象
        self.assertIsNot(board_copy, self.board)
        
        # 修改复制对象不应该影响原对象
        board_copy.set_cell(7, 7, (0, 0, 255))
        self.assertIsNone(self.board.get_cell(7, 7))
    
    def test_board_clearing(self):
        """测试游戏板清空"""
        # 放置一些方块
        self.board.set_cell(5, 5, (255, 0, 0))
        self.board.set_cell(6, 6, (0, 255, 0))
        
        self.assertGreater(self.board.get_filled_cells_count(), 0)
        
        # 清空游戏板
        self.board.clear_board()
        
        self.assertEqual(self.board.get_filled_cells_count(), 0)
        self.assertIsNone(self.board.get_cell(5, 5))
        self.assertIsNone(self.board.get_cell(6, 6))
    
    def test_string_representation(self):
        """测试字符串表示"""
        # 测试空板
        str_repr = str(self.board)
        self.assertIn("·", str_repr)  # 应该包含空格子符号
        
        # 放置方块后测试
        self.board.set_cell(0, 0, (255, 0, 0))
        str_repr = str(self.board)
        self.assertIn("█", str_repr)  # 应该包含填充符号
        
        # 测试repr
        repr_str = repr(self.board)
        self.assertIn("Board", repr_str)
        self.assertIn(str(self.board.width), repr_str)
        self.assertIn(str(self.board.height), repr_str)

if __name__ == '__main__':
    unittest.main()