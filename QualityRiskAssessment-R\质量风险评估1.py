import tkinter as tk
from tkinter import ttk, filedialog
import json
from tkinter import messagebox
import pandas as pd
from typing import Dict, List
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import os
from datetime import datetime
import warnings
warnings.filterwarnings("ignore", category=UserWarning)
import matplotlib
import traceback
os.environ['MPLBACKEND'] = 'Agg'  # 设置matplotlib后端
matplotlib.use('Agg')  # 在导入pyplot之前设置backend
matplotlib.rcParams['figure.dpi'] = 100  # 设置DPI
matplotlib.rcParams['savefig.dpi'] = 100  # 设置保存图片的DPI

# 禁用一些不必要的matplotlib功能
matplotlib.rcParams['figure.autolayout'] = False
matplotlib.rcParams['figure.figsize'] = [6.0, 4.0]
matplotlib.rcParams['figure.max_open_warning'] = 0

# 禁用ICC配置文件
matplotlib.rcParams['image.cmap'] = 'RdYlGn_r'  # 设置默认colormap
matplotlib.rcParams['figure.facecolor'] = 'white'
matplotlib.rcParams['savefig.facecolor'] = 'white'

class QualityRiskAssessment:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("软件质量风险评估系统")
        self.root.geometry("1200x800")
        
        # 初始化基本UI
        self.create_menu()
        self.create_notebook()
        
        # 延迟加载其他组件
        self.root.after(100, self.delayed_init)
    
    def delayed_init(self):
        """延迟初始化其他组件"""
        # 配置文件路径
        self.config_file = "risk_assessment_config.json"
        
        # 初始化数据
        self.load_config()
        
        # 创建界面组件
        self.create_risk_dashboard()
        self.create_config_panel()
        
        # 风险预警阈值
        self.risk_thresholds = {
            "low": 0.3,
            "medium": 0.6,
            "high": 0.8
        }

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.business_modules = data.get('business_modules', self.get_default_modules())
            else:
                self.business_modules = self.get_default_modules()
        except Exception as e:
            messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")
            self.business_modules = self.get_default_modules()

    def get_default_modules(self):
        """获取默认模块配置"""
        return {
            "M1": {"name": "指令系统", "weight": 0.12, "functions": {}},
            "M2": {"name": "委托管理", "weight": 0.15, "functions": {}},
            "M3": {"name": "交易通道", "weight": 0.13, "functions": {}},
            "M4": {"name": "风控系统", "weight": 0.12, "functions": {}},
            "M5": {"name": "清算系统", "weight": 0.12, "functions": {}},
            "M6": {"name": "账户管理", "weight": 0.12, "functions": {}},
            "M7": {"name": "报表系统", "weight": 0.10, "functions": {}},
            "M8": {"name": "监控预警", "weight": 0.08, "functions": {}},
            "M9": {"name": "行情资讯", "weight": 0.06, "functions": {}}
        }

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入配置", command=self.import_config)
        file_menu.add_command(label="导出配置", command=self.export_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="风险阈值设置", command=self.show_risk_settings)
        tools_menu.add_command(label="生成风险报告", command=self.generate_risk_report)
        
    def create_notebook(self):
        """创建选项卡界面"""
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both')
        
        # 创建风险看板和配置管理两个选项卡
        self.risk_frame = ttk.Frame(self.notebook)
        self.config_frame = ttk.Frame(self.notebook)
        
        self.notebook.add(self.risk_frame, text='风险可视化看板')
        self.notebook.add(self.config_frame, text='配置管理')

    def create_risk_dashboard(self):
        """创建风险可视化看板"""
        try:
            # 创建上下分布的主框架
            main_frame = ttk.Frame(self.risk_frame)
            main_frame.pack(fill='both', expand=True, padx=5, pady=5)
            
            # 创建上部热力图区域的框架
            heatmap_container = ttk.Frame(main_frame)
            heatmap_container.pack(fill='x', expand=False)
            
            # 创建左侧风险总结区域
            summary_frame = ttk.LabelFrame(heatmap_container, text="风险评估总结", width=200)
            summary_frame.pack(side='left', fill='y', padx=5, pady=5)
            summary_frame.pack_propagate(False)  # 防止子组件影响frame大小
            
            # 总体风险度显示
            risk_frame = ttk.Frame(summary_frame)
            risk_frame.pack(fill='x', padx=5, pady=5)
            ttk.Label(risk_frame, text="需求总体风险度：", font=('Arial', 10, 'bold')).pack()
            self.total_risk_label = ttk.Label(risk_frame, text="0%", font=('Arial', 12, 'bold'))
            self.total_risk_label.pack()
            
            # 风险总结信息
            self.risk_summary_text = tk.Text(summary_frame, wrap=tk.WORD, width=20, height=15)
            self.risk_summary_text.pack(fill='both', expand=True, padx=5, pady=5)
            
            # 创建刷新按钮容器，使用Frame包装以控制位置
            button_frame = ttk.Frame(summary_frame)
            button_frame.pack(fill='x', padx=5, pady=5, side='bottom')
            
            # 添加刷新按钮
            refresh_button = ttk.Button(button_frame, text="刷新风险度", 
                                      command=self.update_risk_dashboard)
            refresh_button.pack(fill='x')
            
            # 创建热力图区域，使用Frame包装以控制大小和位置
            heatmap_frame = ttk.Frame(heatmap_container)
            heatmap_frame.pack(side='right', fill='both', expand=True, padx=5)
            
            # 保存heatmap_frame的引用
            self.heatmap_frame = heatmap_frame
            
            # 创建热力图
            self.update_heatmap(heatmap_frame)
            
            # 创建下部风险详情列表
            self.create_risk_details(main_frame)
            
        except Exception as e:
            messagebox.showerror("错误", f"创建风险看板失败: {str(e)}")

    def update_heatmap(self, heatmap_frame):
        """更新热力图"""
        if hasattr(self, 'canvas'):
            self.canvas.get_tk_widget().destroy()
        
        try:
            # 重置matplotlib设置
            plt.rcParams.update(plt.rcParamsDefault)
            plt.rcParams['font.family'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
            plt.rcParams['axes.unicode_minus'] = False
            plt.style.use('seaborn')
            
            # 创建新的图形，增加顶部空间以容纳标题
            fig = plt.figure(figsize=(6, 6.5), dpi=100)  # 增加高度以适应标题
            ax = fig.add_subplot(111)
            
            # 获取风险数据和名称
            risks = self.calculate_all_risks()
            risk_matrix = np.zeros((3, 3))
            name_matrix = np.empty((3, 3), dtype=object)
            
            # 填充矩阵
            for i, (module_id, risk) in enumerate(risks.items()):
                row = i // 3
                col = i % 3
                risk_matrix[row, col] = risk
                name = self.business_modules[module_id]['name']
                name_matrix[row, col] = name
            
            # 创建热力图
            heatmap = ax.imshow(risk_matrix, 
                               cmap='RdYlGn_r',
                               vmin=0, vmax=1,
                               aspect='equal')
            
            # 添加模块名称和风险值
            for i in range(3):
                for j in range(3):
                    risk = risk_matrix[i, j]
                    module_name = name_matrix[i, j]
                    if module_name:
                        try:
                            color = 'white' if risk > 0.5 else 'black'
                            fontsize = min(10, 120 / max(len(module_name), 1))
                            
                            text = f"{module_name}\n{risk:.1%}"
                            bbox_props = dict(
                                boxstyle='round,pad=0.3',
                                fc='white' if risk > 0.5 else 'none',
                                ec='gray',
                                alpha=0.7
                            )
                            
                            ax.text(j, i, text,
                                   ha='center', va='center',
                                   color=color,
                                   fontsize=fontsize,
                                   fontweight='bold',
                                   bbox=bbox_props,
                                   family='Microsoft YaHei')
                        except Exception as e:
                            print(f"添加文本失败: {str(e)}")
            
            # 设置标题，调整位置和大小
            plt.title("模块风险热力图", 
                     fontsize=12, 
                     pad=15,  # 增加标题与图表的距离
                     fontweight='bold',
                     family='Microsoft YaHei',
                     y=1.02)  # 将标题向上移动
            
            # 设置颜色条
            cbar = plt.colorbar(heatmap)
            cbar.set_label('风险度', 
                          fontsize=10, 
                          fontweight='bold',
                          family='Microsoft YaHei')
            cbar.ax.tick_params(labelsize=8)
            
            # 移除坐标轴
            ax.set_xticks([])
            ax.set_yticks([])
            
            # 添加网格线
            for edge in range(4):
                ax.axhline(y=edge-0.5, color='white', linewidth=1)
                ax.axvline(x=edge-0.5, color='white', linewidth=1)
            
            # 调整布局，确保标题完全显示
            plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为顶部标题预留空间
            
            # 创建canvas并添加边框
            self.canvas = FigureCanvasTkAgg(fig, master=heatmap_frame)
            self.canvas.draw()
            canvas_widget = self.canvas.get_tk_widget()
            canvas_widget.pack(side=tk.TOP, fill=tk.BOTH, expand=1, padx=5, pady=5)
            
        except Exception as e:
            print(f"热力图错误详情: {str(e)}")
            traceback.print_exc()
            messagebox.showerror("错误", f"更新热力图失败: {str(e)}")

    def create_risk_details(self, main_frame):
        """创建风险详情列表"""
        details_frame = ttk.LabelFrame(main_frame, text="风险详情")
        details_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 创建详情表格
        columns = ("module", "weight", "risk", "status", "warning", "func_count")
        self.risk_tree = ttk.Treeview(details_frame, columns=columns, show='headings')
        
        # 设置列标题
        self.risk_tree.heading("module", text="模块")
        self.risk_tree.heading("weight", text="权重")
        self.risk_tree.heading("risk", text="风险度")
        self.risk_tree.heading("status", text="状态")
        self.risk_tree.heading("warning", text="预警信息")
        self.risk_tree.heading("func_count", text="功能点数")
        
        # 设置列宽
        self.risk_tree.column("module", width=150)
        self.risk_tree.column("weight", width=80)
        self.risk_tree.column("risk", width=80)
        self.risk_tree.column("status", width=80)
        self.risk_tree.column("warning", width=150)
        self.risk_tree.column("func_count", width=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(details_frame, orient="vertical", command=self.risk_tree.yview)
        self.risk_tree.configure(yscrollcommand=scrollbar.set)
        
        self.risk_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

    def create_config_panel(self):
        """创建配置管理界面"""
        # 创建水平分割窗口
        self.config_paned = ttk.PanedWindow(self.config_frame, orient=tk.HORIZONTAL)
        self.config_paned.pack(fill='both', expand=True)
        
        # 创建左侧树形结构面板
        tree_frame = ttk.Frame(self.config_paned)
        
        # 创建树形表格
        columns = (
            "name",          # 名称
            "weight",        # 权重
            "total_cases",   # 需求对应用例数
            "executed_cases", # 需求对应用例执行数
            "case_coverage", # 测试用例覆盖率
            "w1",           # 测试用例覆盖率权重
            "branch_total", # 需求对应代码分支数
            "branch_covered", # 需求测试覆盖代码分支数
            "branch_coverage", # 分支覆盖率
            "w2",           # 分支覆盖率权重
            "business_cases", # 业务模块对应用例数
            "business_covered", # 业务模块回归覆盖用例数
            "business_coverage", # 业务覆盖度
            "w3",           # 业务覆盖度权重
            "risk"          # 功能点风险度
        )
        
        # 列标题中文映射
        column_names = {
            "name": "名称",
            "weight": "权重",
            "total_cases": "需求用例数",
            "executed_cases": "已执行用例数",
            "case_coverage": "用例覆盖率",
            "w1": "用例权重",
            "branch_total": "分支总数",
            "branch_covered": "已覆盖分支",
            "branch_coverage": "分支覆盖率",
            "w2": "分支权重",
            "business_cases": "业务用例数",
            "business_covered": "回归覆盖数",
            "business_coverage": "业务覆盖度",
            "w3": "业务权重",
            "risk": "风险度"
        }
        
        self.tree = ttk.Treeview(tree_frame, columns=columns)
        
        # 设置列标题
        self.tree.heading("#0", text="ID")
        for col in columns:
            self.tree.heading(col, text=column_names[col])
        
        # 设置列宽
        self.tree.column("#0", width=80)
        for col in columns:
            self.tree.column(col, width=100)
        
        # 添加滚动条
        h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.tree.xview)
        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # 使用grid布局管理器
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # 配置grid权重
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # 创建右侧编辑面板，设置最小宽度
        self.edit_frame = ttk.LabelFrame(self.config_paned, text="编辑面板", width=300)
        
        # 将左右两个面板添加到PanedWindow，并设置权重和最小宽度
        self.config_paned.add(tree_frame, weight=1)
        self.config_paned.add(self.edit_frame, weight=0)  # 设置weight=0使其保持固定宽度
        
        # 设置编辑面板的最小宽度
        self.edit_frame.configure(width=300)
        self.edit_frame.pack_propagate(False)  # 防止子组件影响frame大小
        
        # 创建子模块和功能点编辑界面
        self.create_edit_interfaces()
        
        # 绑定选择事件
        self.tree.bind('<<TreeviewSelect>>', self.on_tree_select)
        
        # 加载初始数据
        self.load_tree_data()

    def create_edit_interfaces(self):
        """创建编辑界面"""
        # 创建子模块编辑界面
        self.submodule_frame = ttk.Frame(self.edit_frame)
        
        # 子模块编辑区域
        ttk.Label(self.submodule_frame, text="子模块配置", font=('Arial', 10, 'bold')).pack(pady=5)
        
        # 子模块输入区域
        input_frame = ttk.Frame(self.submodule_frame)
        input_frame.pack(fill='x', padx=5, pady=5)
        
        # 名称输入
        ttk.Label(input_frame, text="名称:").pack(anchor='w')
        self.submodule_name_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.submodule_name_var).pack(fill='x', pady=2)
        
        # 权重输入
        ttk.Label(input_frame, text="权重:").pack(anchor='w')
        self.submodule_weight_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.submodule_weight_var).pack(fill='x', pady=2)
        
        # 按钮区域
        button_frame = ttk.Frame(self.submodule_frame)
        button_frame.pack(fill='x', padx=5, pady=5)
        ttk.Button(button_frame, text="新增子模块", command=self.add_new_submodule).pack(side='left', padx=5)
        ttk.Button(button_frame, text="保存子模块", command=self.save_submodule).pack(side='left', padx=5)
        ttk.Button(button_frame, text="删除子模块", command=self.delete_submodule).pack(side='left', padx=5)
        
        # 创建功能点编辑界面
        self.function_frame = ttk.Frame(self.edit_frame)
        ttk.Label(self.function_frame, text="功能点配置", font=('Arial', 10, 'bold')).pack(pady=5)
        
        # 功能点编辑字段
        self.function_vars = {}
        fields = [
            ("名称:", "name"),
            ("权重:", "weight"),
            ("需求用例数:", "total_cases"),
            ("已执行用例数:", "executed_cases"),
            ("用例覆盖率权重(%):", "w1"),
            ("代码分支总数:", "branch_total"),
            ("已覆盖分支数:", "branch_covered"),
            ("分支覆盖率权重(%):", "w2"),
            ("业务用例总数:", "business_cases"),
            ("回归覆盖用例数:", "business_covered"),
            ("业务覆盖度权重(%):", "w3")
        ]
        
        # 创建可滚动的容器
        canvas = tk.Canvas(self.function_frame)
        scrollbar = ttk.Scrollbar(self.function_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        # 配置滚动区域
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 添加字段
        for label, key in fields:
            frame = ttk.Frame(scrollable_frame)
            frame.pack(fill='x', padx=5, pady=2)
            ttk.Label(frame, text=label).pack(side='left')
            var = tk.StringVar()
            self.function_vars[key] = var
            ttk.Entry(frame, textvariable=var).pack(side='right', expand=True, fill='x')
        
        # 按钮区域
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.pack(fill='x', padx=5, pady=5)
        ttk.Button(button_frame, text="保存功能点", command=self.save_function).pack(side='left', padx=5)
        ttk.Button(button_frame, text="删除功能点", command=self.delete_function).pack(side='left', padx=5)
        
        # 布局滚动区域
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 初始时隐藏所有面板
        self.submodule_frame.pack_forget()
        self.function_frame.pack_forget()

    def on_tree_select(self, event):
        """处理树形结构选择事件"""
        selected = self.tree.selection()
        if not selected:
            return
        
        item = selected[0]
        values = self.tree.item(item)['values']
        
        # 隐藏所有面板
        self.submodule_frame.pack_forget()
        self.function_frame.pack_forget()
        
        if '_func_' in item:  # 功能点
            # 显示功能点编辑界面
            self.function_frame.pack(fill='both', expand=True)
            # 加载功能点数据
            self.load_function_data(item, values)
        else:  # 子模块
            # 显示子模块编辑界面
            self.submodule_frame.pack(fill='both', expand=True)
            # 加载子模块数据
            self.submodule_name_var.set(values[0])
            self.submodule_weight_var.set(values[1])

    def load_submodules(self, module_id):
        """加载子模块数据"""
        # 清空子模块表格
        for item in self.submodule_tree.get_children():
            self.submodule_tree.delete(item)
        
        # 加载子模块数据
        if 'functions' in self.business_modules[module_id]:
            for func_id, func_info in self.business_modules[module_id]['functions'].items():
                if isinstance(func_info, dict):
                    self.submodule_tree.insert("", "end", func_id, 
                                             values=(func_info.get('name', ''), 
                                                    func_info.get('weight', 10)))

    def load_function_data(self, function_id, values):
        """加载功能点数据"""
        if not values:
            return
        
        # 从配置数据中获取完整的功能点数据
        module_id = function_id.split('_')[0]  # 获取父模块ID
        func_data = self.business_modules[module_id]['functions'].get(function_id, {})
        
        # 设置功能点数据，确保数值格式正确
        self.function_vars['name'].set(func_data.get('name', ''))
        self.function_vars['weight'].set(f"{func_data.get('weight', 0):.1f}")
        self.function_vars['total_cases'].set(str(func_data.get('total_cases', 0)))
        self.function_vars['executed_cases'].set(str(func_data.get('executed_cases', 0)))
        self.function_vars['w1'].set(str(func_data.get('w1', 33)))
        self.function_vars['branch_total'].set(str(func_data.get('branch_total', 0)))
        self.function_vars['branch_covered'].set(str(func_data.get('branch_covered', 0)))
        self.function_vars['w2'].set(str(func_data.get('w2', 33)))
        self.function_vars['business_cases'].set(str(func_data.get('business_cases', 0)))
        self.function_vars['business_covered'].set(str(func_data.get('business_covered', 0)))
        self.function_vars['w3'].set(str(func_data.get('w3', 34)))

    def add_new_submodule(self):
        """新增子模块"""
        new_id = f"M{len(self.business_modules) + 1}"  # 生成新的子模块ID
        self.submodule_name_var.set("新子模块")
        self.submodule_weight_var.set("10")  # 默认权重为10
        
        # 更新树形结构
        self.tree.insert("", "end", new_id, text=new_id, values=("新子模块", "10.0", "", "", "", "", "", "", "", "", "", "", "", "", ""))
        
        # 更新配置数据
        self.business_modules[new_id] = {
            "name": "新子模块",
            "weight": 0.1,  # 默认权重为0.1
            "functions": {}
        }

    def delete_submodule(self):
        """删除子模块"""
        selected = self.tree.selection()
        if not selected or '_' in selected[0]:  # 确保选中的是子模块
            messagebox.showwarning("警告", "请先选择一个子模块")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的子模块吗？"):
            for item in selected:
                # 从树形结构中删除
                self.tree.delete(item)
                
                # 从配置数据中删除
                if item in self.business_modules:
                    del self.business_modules[item]
            
            # 保存配置
            self.save_config()
            
            # 清空编辑面板
            self.submodule_name_var.set("")
            self.submodule_weight_var.set("")
            
            messagebox.showinfo("成功", "子模块已删除")

    def save_submodule(self):
        """保存子模块配置"""
        selected = self.tree.selection()
        if not selected or '_' in selected[0]:  # 确保选中的是子模块
            messagebox.showwarning("警告", "请先选择一个子模块")
            return
        
        try:
            module_id = selected[0]
            name = self.submodule_name_var.get().strip()
            if not name:
                raise ValueError("名称不能为空")
            
            weight = float(self.submodule_weight_var.get())
            if not (0 <= weight <= 100):
                raise ValueError("权重必须在0-100之间")
            
            # 更新树形结构
            values = [
                name,
                f"{weight:.1f}",  # 保持一位小数
                "", "", "", "", "", "", "", "", "", "", "", "", ""  # 其他字段为空
            ]
            self.tree.item(module_id, values=values)
            
            # 更新配置数据
            self.business_modules[module_id].update({
                'name': name,
                'weight': weight/100  # 转换为小数存储
            })
            
            # 保存配置
            self.save_config()
            
            # 更新风险看板
            self.update_risk_dashboard()
            
            messagebox.showinfo("成功", "子模块配置已保存")
            
        except ValueError as e:
            messagebox.showerror("错误", str(e))

    def save_function(self):
        """保存功能点配置"""
        selected = self.tree.selection()
        if not selected or '_func_' not in selected[0]:
            messagebox.showwarning("警告", "请先选择一个功能点")
            return
        
        try:
            function_id = selected[0]
            module_id = function_id.split('_')[0]
            
            # 获取并验证输入值
            name = self.function_vars['name'].get().strip()
            if not name:
                raise ValueError("名称不能为空")
            
            # 验证所有数值输入
            weight = self.validate_numeric_input(self.function_vars['weight'].get(), 
                                              "权重", 0, 100)
            total_cases = int(self.validate_numeric_input(self.function_vars['total_cases'].get(), 
                                                        "需求用例数", 0))
            executed_cases = int(self.validate_numeric_input(self.function_vars['executed_cases'].get(), 
                                                           "已执行用例数", 0))
            w1 = self.validate_numeric_input(self.function_vars['w1'].get(), 
                                           "用例覆盖率权重", 0, 100)
            branch_total = int(self.validate_numeric_input(self.function_vars['branch_total'].get(), 
                                                         "分支总数", 0))
            branch_covered = int(self.validate_numeric_input(self.function_vars['branch_covered'].get(), 
                                                           "已覆盖分支数", 0))
            w2 = self.validate_numeric_input(self.function_vars['w2'].get(), 
                                           "分支覆盖率权重", 0, 100)
            business_cases = int(self.validate_numeric_input(self.function_vars['business_cases'].get(), 
                                                           "业务用例数", 0))
            business_covered = int(self.validate_numeric_input(self.function_vars['business_covered'].get(), 
                                                             "回归覆盖用例数", 0))
            w3 = self.validate_numeric_input(self.function_vars['w3'].get(), 
                                           "业务覆盖度权重", 0, 100)
            
            # 验证数据关系
            if executed_cases > total_cases:
                raise ValueError("已执行用例数不能大于总用例数")
            if branch_covered > branch_total:
                raise ValueError("已覆盖分支数不能大于分支总数")
            if business_covered > business_cases:
                raise ValueError("回归覆盖用例数不能大于业务用例总数")
            if not abs(w1 + w2 + w3 - 100) < 0.01:
                raise ValueError("三个权重之和必须等于100%")
            
            # 计算覆盖率和风险度
            case_coverage = (executed_cases / total_cases * 100) if total_cases > 0 else 0
            branch_coverage = (branch_covered / branch_total * 100) if branch_total > 0 else 0
            business_coverage = (business_covered / business_cases * 100) if business_cases > 0 else 0
            
            risk = self.calculate_risk(
                executed_cases/total_cases if total_cases > 0 else 0,
                branch_covered/branch_total if branch_total > 0 else 0,
                business_covered/business_cases if business_cases > 0 else 0,
                w1/100, w2/100, w3/100
            )
            
            # 更新功能点数据
            func_info = {
                'name': name,
                'weight': weight,
                'total_cases': total_cases,
                'executed_cases': executed_cases,
                'w1': w1,
                'branch_total': branch_total,
                'branch_covered': branch_covered,
                'w2': w2,
                'business_cases': business_cases,
                'business_covered': business_covered,
                'w3': w3,
                'case_coverage': case_coverage,
                'branch_coverage': branch_coverage,
                'business_coverage': business_coverage,
                'risk': risk
            }
            
            self.business_modules[module_id]['functions'][function_id] = func_info
            
            # 保存并更新显示
            self.save_config()
            self.load_tree_data()
            self.update_risk_dashboard()
            
            messagebox.showinfo("成功", "功能点配置已保存")
            
        except ValueError as e:
            messagebox.showerror("错误", str(e))

    def delete_function(self):
        """删除功能点"""
        selected = self.tree.selection()
        if not selected or '_func_' not in selected[0]:
            messagebox.showwarning("警告", "请先选择一个功能点")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的功能点吗？"):
            for item in selected:
                # 从树形结构中删除
                self.tree.delete(item)
                
                # 从配置数据中删除
                module_id = item.split('_')[0]
                if 'functions' in self.business_modules[module_id]:
                    if item in self.business_modules[module_id]['functions']:
                        del self.business_modules[module_id]['functions'][item]
            
            # 保存配置
            self.save_config()
            
            # 清空编辑面板
            for var in self.function_vars.values():
                var.set("")
            
            messagebox.showinfo("成功", "功能点已删除")

    def load_tree_data(self):
        """加载树形结构数据"""
        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 加载子模块数据（一级节点）
            for module_id, module_info in self.business_modules.items():
                # 子模块值
                sub_values = [
                    module_info["name"],
                    f"{module_info['weight'] * 100:.1f}",  # 转换为百分比数值
                    "", "", "", "", "", "", "", "", "", "", "", "", ""  # 子模块其他字段为空
                ]
                
                # 插入子模块
                self.tree.insert("", "end", module_id, text=module_id, values=sub_values)
                
                # 加载功能点数据（二级节点）
                if "functions" in module_info:
                    for func_id, func_info in module_info["functions"].items():
                        if isinstance(func_info, dict):
                            # 计算覆盖率
                            total_cases = func_info.get("total_cases", 0)
                            executed_cases = func_info.get("executed_cases", 0)
                            branch_total = func_info.get("branch_total", 0)
                            branch_covered = func_info.get("branch_covered", 0)
                            business_cases = func_info.get("business_cases", 0)
                            business_covered = func_info.get("business_covered", 0)
                            
                            case_coverage = (executed_cases / total_cases * 100) if total_cases > 0 else 0
                            branch_coverage = (branch_covered / branch_total * 100) if branch_total > 0 else 0
                            business_coverage = (business_covered / business_cases * 100) if business_cases > 0 else 0
                            
                            # 计算风险度
                            w1 = func_info.get("w1", 33)
                            w2 = func_info.get("w2", 33)
                            w3 = func_info.get("w3", 34)
                            
                            risk = self.calculate_risk(
                                executed_cases/total_cases if total_cases > 0 else 0,
                                branch_covered/branch_total if branch_total > 0 else 0,
                                business_covered/business_cases if business_cases > 0 else 0,
                                w1/100, w2/100, w3/100
                            )
                            
                            func_values = [
                                func_info.get("name", ""),
                                f"{func_info.get('weight', 0):.1f}",  # 权重
                                str(total_cases),
                                str(executed_cases),
                                f"{case_coverage:.1f}",
                                str(w1),
                                str(branch_total),
                                str(branch_covered),
                                f"{branch_coverage:.1f}",
                                str(w2),
                                str(business_cases),
                                str(business_covered),
                                f"{business_coverage:.1f}",
                                str(w3),
                                f"{risk*100:.1f}"
                            ]
                            
                            if not self.tree.exists(func_id):
                                self.tree.insert(module_id, "end", func_id, text=func_id, values=func_values)
                            
        except Exception as e:
            messagebox.showerror("错误", f"加载树形结构数据失败: {str(e)}")

    def save_config(self):
        """保存配置到文件"""
        try:
            # 将当前配置保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'business_modules': self.business_modules
                }, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def import_config(self):
        """导入配置"""
        file_path = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.business_modules = json.load(f)
                self.load_tree_data()
                self.update_risk_dashboard()
                messagebox.showinfo("成功", "配置导入成功")
            except Exception as e:
                messagebox.showerror("错误", f"导入失败: {str(e)}")

    def export_config(self):
        """导出配置"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.business_modules, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", "配置导出成功")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")

    def generate_risk_report(self):
        """生成风险报告"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".html",
            filetypes=[("HTML files", "*.html"), ("All files", "*.*")]
        )
        if not file_path:
            return

        try:
            risks = self.calculate_all_risks()
            total_risk = sum(risk * self.business_modules[module_id]['weight'] 
                            for module_id, risk in risks.items())
            
            # 生成HTML报告头部
            html_parts = []
            html_parts.append("""
                <html>
                <head>
                    <title>质量风险评估报告</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                        .high-risk { background-color: #ffcccc; }
                        .medium-risk { background-color: #ffffcc; }
                        .low-risk { background-color: #ccffcc; }
                        .summary { margin: 20px 0; }
                        .module-details { margin: 20px 0; }
                    </style>
                </head>
                <body>
            """)

            # 添加报告标题和总体信息
            html_parts.append(f"""
                <h1>质量风险评估报告</h1>
                <p>生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                
                <div class="summary">
                    <h2>总体风险评估</h2>
                    <p>总体风险度：<strong>{total_risk:.1%}</strong></p>
                    <p>风险状态：<strong>{self.get_risk_status(total_risk)}</strong></p>
                    <p>预警信息：<strong>{self.get_risk_warning(total_risk)}</strong></p>
                </div>
            """)

            # 添加模块风险详情表格
            html_parts.append("""
                <div class="module-details">
                    <h2>模块风险详情</h2>
                    <table>
                        <tr>
                            <th>模块</th>
                            <th>权重</th>
                            <th>风险度</th>
                            <th>状态</th>
                            <th>预警信息</th>
                            <th>功能点数量</th>
                        </tr>
            """)

            # 添加模块数据
            for module_id, risk in risks.items():
                module_info = self.business_modules[module_id]
                func_count = len(module_info.get('functions', {}))
                risk_class = 'high-risk' if risk > self.risk_thresholds['medium'] else \
                            'medium-risk' if risk > self.risk_thresholds['low'] else 'low-risk'
                
                html_parts.append(f"""
                    <tr class="{risk_class}">
                        <td>{module_info['name']}</td>
                        <td>{module_info['weight']:.1%}</td>
                        <td>{risk:.1%}</td>
                        <td>{self.get_risk_status(risk)}</td>
                        <td>{self.get_risk_warning(risk)}</td>
                        <td>{func_count}</td>
                    </tr>
                """)

            # 添加功能点详情表格头部
            html_parts.append("""
                    </table>
                    
                    <h2>功能点风险详情</h2>
                    <table>
                        <tr>
                            <th>模块</th>
                            <th>功能点</th>
                            <th>权重</th>
                            <th>用例覆盖率</th>
                            <th>分支覆盖率</th>
                            <th>业务覆盖度</th>
                            <th>风险度</th>
                        </tr>
            """)

            # 添加功能点数据
            for module_id, module_info in self.business_modules.items():
                if 'functions' in module_info:
                    for func_id, func_info in module_info['functions'].items():
                        risk = func_info.get('risk', 0)
                        risk_class = 'high-risk' if risk > self.risk_thresholds['medium'] else \
                                   'medium-risk' if risk > self.risk_thresholds['low'] else 'low-risk'
                        
                        html_parts.append(f"""
                            <tr class="{risk_class}">
                                <td>{module_info['name']}</td>
                                <td>{func_info['name']}</td>
                                <td>{func_info['weight']:.1%}</td>
                                <td>{func_info.get('case_coverage', 0):.1f}%</td>
                                <td>{func_info.get('branch_coverage', 0):.1f}%</td>
                                <td>{func_info.get('business_coverage', 0):.1f}%</td>
                                <td>{risk*100:.1f}%</td>
                            </tr>
                        """)

            # 添加HTML尾部
            html_parts.append("""
                    </table>
                </div>
            </body>
            </html>
            """)

            # 合并所有HTML部分并写入文件
            html_content = "".join(html_parts)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            messagebox.showinfo("成功", "风险报告生成成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {str(e)}")

    def show_risk_settings(self):
        """显示风险阈值设置对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("风险阈值设置")
        dialog.geometry("300x200")
        
        # 创建输入框
        frames = {}
        vars = {}
        for level in ['low', 'medium', 'high']:
            frames[level] = ttk.Frame(dialog)
            frames[level].pack(fill='x', padx=20, pady=5)
            
            ttk.Label(frames[level], text=f"{level.title()}风险阈值:").pack(side='left')
            vars[level] = tk.StringVar(value=f"{self.risk_thresholds[level]:.1%}")
            ttk.Entry(frames[level], textvariable=vars[level]).pack(side='right', expand=True)
        
        def save_settings():
            try:
                for level in ['low', 'medium', 'high']:
                    value = float(vars[level].get().strip('%')) / 100
                    if not (0 <= value <= 1):
                        raise ValueError(f"{level.title()}风险阈值必须在0-100%之间")
                    self.risk_thresholds[level] = value
                    
                if not (self.risk_thresholds['low'] < self.risk_thresholds['medium'] < self.risk_thresholds['high']):
                    raise ValueError("阈值必须满足: 低风险 < 中风险 < 高风险")
                    
                self.update_risk_dashboard()
                dialog.destroy()
                messagebox.showinfo("成功", "设置已保存")
            except ValueError as e:
                messagebox.showerror("错误", str(e))
        
        ttk.Button(dialog, text="保存", command=save_settings).pack(pady=20)

    def calculate_risk(self, execution_rate: float, coverage_rate: float, 
                      business_coverage: float, w1: float = 0.4, 
                      w2: float = 0.35, w3: float = 0.25) -> float:
        """计算风险度"""
        try:
            # 确保输入值在有效范围内
            execution_rate = max(0.0, min(1.0, float(execution_rate)))
            coverage_rate = max(0.0, min(1.0, float(coverage_rate)))
            business_coverage = max(0.0, min(1.0, float(business_coverage)))
            
            # 确保权重之和为1，使用更精确的计算
            total_weight = w1 + w2 + w3
            if abs(total_weight - 1.0) > 0.0001:
                w1, w2, w3 = w1/total_weight, w2/total_weight, w3/total_weight
            
            # 使用更精确的风险计算公式
            risk = (
                (1 - execution_rate) * w1 +
                (1 - coverage_rate) * w2 +
                (1 - business_coverage) * w3
            )
            
            # 使用round确保精度
            return round(max(0.0, min(1.0, float(risk))), 4)
        
        except Exception as e:
            print(f"风险计算错误: {str(e)}")
            return 1.0  # 出错时返回最高风险

    def calculate_all_risks(self) -> Dict[str, float]:
        """计算所有模块的风险度"""
        risks = {}
        for module_id, module_info in self.business_modules.items():
            if not module_info.get('functions'):
                risks[module_id] = 0
                continue
                
            functions = module_info['functions']
            if not functions:
                risks[module_id] = 0
                continue
            
            weights = [f.get('weight', 0) for f in functions.values()]
            risk_values = [f.get('risk', 0) for f in functions.values()]
            
            total_weight = sum(weights)
            if total_weight <= 0:
                risks[module_id] = 0
                continue
                
            module_risk = sum(w * r for w, r in zip(weights, risk_values)) / total_weight
            risks[module_id] = module_risk
        
        return risks

    def update_risk_dashboard(self):
        """更新风险看板"""
        try:
            # 使用保存的heatmap_frame引用更新热力图
            if hasattr(self, 'heatmap_frame'):
                self.update_heatmap(self.heatmap_frame)
            
            # 更新风险详情
            self.update_risk_details()
            
            # 计算并更新总体风险
            risks = self.calculate_all_risks()
            total_risk = sum(risk * self.business_modules[module_id]['weight'] 
                            for module_id, risk in risks.items())
            self.total_risk_label.config(text=f"{total_risk:.1%}")
            
            # 生成并更新风险总结信息
            summary = self.generate_risk_summary(risks)
            self.risk_summary_text.delete('1.0', tk.END)
            self.risk_summary_text.insert('1.0', summary)
            
        except Exception as e:
            messagebox.showerror("错误", f"更新风险看板失败: {str(e)}")

    def update_risk_details(self):
        """更新风险详情列表"""
        try:
            # 清空现有数据
            for item in self.risk_tree.get_children():
                self.risk_tree.delete(item)
            
            # 添加新数据
            risks = self.calculate_all_risks()
            for module_id, risk in risks.items():
                module_info = self.business_modules[module_id]
                func_count = len(module_info.get('functions', {}))
                status = self.get_risk_status(risk)
                warning = self.get_risk_warning(risk)
                
                # 设置风险等级样式
                tags = ('high_risk',) if risk > self.risk_thresholds['medium'] else \
                       ('medium_risk',) if risk > self.risk_thresholds['low'] else \
                       ('low_risk',)
                
                self.risk_tree.insert("", "end", values=(
                    module_info['name'],
                    f"{module_info['weight']*100:.1f}%",
                    f"{risk:.1%}",
                    status,
                    warning,
                    func_count
                ), tags=tags)
            
            # 设置不同风险等级的颜色
            self.risk_tree.tag_configure('high_risk', background='#ffcccc')
            self.risk_tree.tag_configure('medium_risk', background='#ffffcc')
            self.risk_tree.tag_configure('low_risk', background='#ccffcc')
            
        except Exception as e:
            messagebox.showerror("错误", f"更新风险详情失败: {str(e)}")

    def get_risk_status(self, risk: float) -> str:
        """获取风险状态"""
        if risk <= self.risk_thresholds['low']:
            return "正常"
        elif risk <= self.risk_thresholds['medium']:
            return "警告"
        else:
            return "危险"

    def get_risk_warning(self, risk: float) -> str:
        """获取风险预警信息"""
        if risk <= self.risk_thresholds['low']:
            return "风险可控"
        elif risk <= self.risk_thresholds['medium']:
            return "需要关注"
        elif risk <= self.risk_thresholds['high']:
            return "需要立即处理"
        else:
            return "严重风险！"

    def validate_numeric_input(self, value: str, field_name: str, 
                             min_val: float = None, max_val: float = None) -> float:
        """验证数值输入
        Args:
            value: 输入值
            field_name: 字段名称
            min_val: 最小值
            max_val: 最大值
        Returns:
            float: 验证后的数值
        """
        try:
            num = float(value.strip().rstrip('%'))
            if min_val is not None and num < min_val:
                raise ValueError(f"{field_name}不能小于{min_val}")
            if max_val is not None and num > max_val:
                raise ValueError(f"{field_name}不能大于{max_val}")
            return num
        except ValueError:
            raise ValueError(f"请输入有效的{field_name}")

    def generate_risk_summary(self, risks: Dict[str, float]) -> str:
        """生成风险总结信息"""
        try:
            summary_parts = []
            
            # 统计各风险等级的模块数量
            high_risk = []
            medium_risk = []
            low_risk = []
            
            for module_id, risk in risks.items():
                module_name = self.business_modules[module_id]['name']
                if risk > self.risk_thresholds['medium']:
                    high_risk.append(module_name)
                elif risk > self.risk_thresholds['low']:
                    medium_risk.append(module_name)
                else:
                    low_risk.append(module_name)
            
            # 生成总结信息
            if high_risk:
                summary_parts.append("高风险模块：")
                summary_parts.append(f"共{len(high_risk)}个")
                summary_parts.append("分别是：" + "、".join(high_risk))
                summary_parts.append("\n建议：需要立即处理\n")
            
            if medium_risk:
                summary_parts.append("\n中风险模块：")
                summary_parts.append(f"共{len(medium_risk)}个")
                summary_parts.append("分别是：" + "、".join(medium_risk))
                summary_parts.append("\n建议：需要持续监控\n")
            
            if low_risk:
                summary_parts.append("\n低风险模块：")
                summary_parts.append(f"共{len(low_risk)}个")
                summary_parts.append("分别是：" + "、".join(low_risk))
                summary_parts.append("\n状态：风险可控")
            
            return "".join(summary_parts)
            
        except Exception as e:
            print(f"生成风险总结失败: {str(e)}")
            return "风险总结生成失败"

    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = QualityRiskAssessment()
    app.run()
