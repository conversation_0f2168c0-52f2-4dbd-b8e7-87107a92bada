# Tetromino类的单元测试
import unittest
from tetromino import Tetromino
from config import TETROMINO_SHAPES, CUTE_COLORS

class TestTetromino(unittest.TestCase):
    """Tetromino类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.tetromino_i = Tetromino('I')
        self.tetromino_o = Tetromino('O')
        self.tetromino_t = Tetromino('T')
    
    def test_initialization(self):
        """测试初始化"""
        # 测试指定形状的初始化
        tetromino = Tetromino('I')
        self.assertEqual(tetromino.shape_type, 'I')
        self.assertEqual(tetromino.rotation, 0)
        self.assertEqual(tetromino.color, CUTE_COLORS['I'])
        self.assertEqual(tetromino.x, 0)
        self.assertEqual(tetromino.y, 0)
        
        # 测试随机初始化
        random_tetromino = Tetromino()
        self.assertIn(random_tetromino.shape_type, TETROMINO_SHAPES.keys())
    
    def test_rotation(self):
        """测试旋转功能"""
        # 测试I形状旋转
        initial_rotation = self.tetromino_i.rotation
        result = self.tetromino_i.rotate()
        self.assertTrue(result)
        self.assertEqual(self.tetromino_i.rotation, (initial_rotation + 1) % len(self.tetromino_i.shapes))
        
        # 测试O形状不旋转
        initial_rotation = self.tetromino_o.rotation
        result = self.tetromino_o.rotate()
        self.assertFalse(result)
        self.assertEqual(self.tetromino_o.rotation, initial_rotation)
        
        # 测试T形状完整旋转循环
        t_tetromino = Tetromino('T')
        rotations = []
        for _ in range(4):
            rotations.append(t_tetromino.rotation)
            t_tetromino.rotate()
        
        # 应该回到初始状态
        self.assertEqual(t_tetromino.rotation, 0)
        self.assertEqual(len(set(rotations)), 4)  # 4个不同的旋转状态
    
    def test_get_rotated_shape(self):
        """测试获取旋转后形状（不改变状态）"""
        initial_rotation = self.tetromino_t.rotation
        rotated_shape = self.tetromino_t.get_rotated_shape()
        
        # 状态不应该改变
        self.assertEqual(self.tetromino_t.rotation, initial_rotation)
        
        # 但返回的应该是下一个旋转状态的形状
        expected_shape = self.tetromino_t.shapes[(initial_rotation + 1) % len(self.tetromino_t.shapes)]
        self.assertEqual(rotated_shape, expected_shape)
    
    def test_get_shape_blocks(self):
        """测试获取方块坐标"""
        blocks = self.tetromino_i.get_shape_blocks()
        
        # I形状在初始状态应该有4个方块
        self.assertEqual(len(blocks), 4)
        
        # 所有坐标应该是元组
        for block in blocks:
            self.assertIsInstance(block, tuple)
            self.assertEqual(len(block), 2)
    
    def test_get_absolute_blocks(self):
        """测试获取绝对坐标"""
        self.tetromino_i.set_position(5, 3)
        absolute_blocks = self.tetromino_i.get_absolute_blocks()
        relative_blocks = self.tetromino_i.get_shape_blocks()
        
        # 绝对坐标应该是相对坐标加上位置偏移
        for abs_block, rel_block in zip(absolute_blocks, relative_blocks):
            self.assertEqual(abs_block[0], rel_block[0] + 5)
            self.assertEqual(abs_block[1], rel_block[1] + 3)
    
    def test_movement(self):
        """测试移动功能"""
        initial_x, initial_y = self.tetromino_i.get_position()
        
        # 测试相对移动
        self.tetromino_i.move(2, 3)
        new_x, new_y = self.tetromino_i.get_position()
        self.assertEqual(new_x, initial_x + 2)
        self.assertEqual(new_y, initial_y + 3)
        
        # 测试绝对位置设置
        self.tetromino_i.set_position(10, 15)
        x, y = self.tetromino_i.get_position()
        self.assertEqual(x, 10)
        self.assertEqual(y, 15)
    
    def test_copy(self):
        """测试复制功能"""
        self.tetromino_t.set_position(5, 7)
        self.tetromino_t.rotate()
        
        copy_tetromino = self.tetromino_t.copy()
        
        # 复制的对象应该有相同的属性
        self.assertEqual(copy_tetromino.shape_type, self.tetromino_t.shape_type)
        self.assertEqual(copy_tetromino.rotation, self.tetromino_t.rotation)
        self.assertEqual(copy_tetromino.x, self.tetromino_t.x)
        self.assertEqual(copy_tetromino.y, self.tetromino_t.y)
        
        # 但应该是不同的对象
        self.assertIsNot(copy_tetromino, self.tetromino_t)
        
        # 修改复制对象不应该影响原对象
        copy_tetromino.move(1, 1)
        self.assertNotEqual(copy_tetromino.x, self.tetromino_t.x)
    
    def test_bounding_box(self):
        """测试边界框计算"""
        min_x, min_y, width, height = self.tetromino_i.get_bounding_box()
        
        # 边界框应该是合理的值
        self.assertGreaterEqual(width, 1)
        self.assertGreaterEqual(height, 1)
        self.assertGreaterEqual(min_x, 0)
        self.assertGreaterEqual(min_y, 0)
    
    def test_can_rotate(self):
        """测试旋转能力检查"""
        # I形状可以旋转
        self.assertTrue(self.tetromino_i.can_rotate())
        
        # O形状不能旋转
        self.assertFalse(self.tetromino_o.can_rotate())
        
        # T形状可以旋转
        self.assertTrue(self.tetromino_t.can_rotate())
    
    def test_static_methods(self):
        """测试静态方法"""
        # 测试获取所有形状类型
        all_types = Tetromino.get_all_shape_types()
        self.assertEqual(set(all_types), set(TETROMINO_SHAPES.keys()))
        
        # 测试创建随机方块
        random_tetromino = Tetromino.create_random()
        self.assertIsInstance(random_tetromino, Tetromino)
        self.assertIn(random_tetromino.shape_type, TETROMINO_SHAPES.keys())
    
    def test_string_representation(self):
        """测试字符串表示"""
        self.tetromino_i.set_position(3, 5)
        str_repr = str(self.tetromino_i)
        
        # 字符串应该包含关键信息
        self.assertIn('I', str_repr)
        self.assertIn('3', str_repr)
        self.assertIn('5', str_repr)
    
    def test_all_shapes_have_blocks(self):
        """测试所有形状都有方块"""
        for shape_type in TETROMINO_SHAPES.keys():
            tetromino = Tetromino(shape_type)
            blocks = tetromino.get_shape_blocks()
            self.assertGreater(len(blocks), 0, f"形状 {shape_type} 没有方块")
    
    def test_rotation_consistency(self):
        """测试旋转一致性"""
        for shape_type in TETROMINO_SHAPES.keys():
            tetromino = Tetromino(shape_type)
            initial_blocks = tetromino.get_shape_blocks()
            
            # 旋转4次应该回到原始状态（对于可旋转的形状）
            if tetromino.can_rotate():
                for _ in range(4):
                    tetromino.rotate()
                final_blocks = tetromino.get_shape_blocks()
                self.assertEqual(initial_blocks, final_blocks, 
                               f"形状 {shape_type} 旋转4次后没有回到原始状态")

if __name__ == '__main__':
    unittest.main()