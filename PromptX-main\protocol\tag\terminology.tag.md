# DPML术语定义协议 (Terminology Protocol)

> **TL;DR:** DPML术语定义协议提供统一的术语定义和引用框架，支持通过`#术语`形式在提示词中引用明确定义的术语，确保术语在AI理解和计算机执行两个层面的一致性和准确性。

### 目的与功能

DPML术语定义协议用于在协议文件中嵌入标准化的术语定义，并提供统一的引用方式，解决以下关键问题：
- 为特定领域和上下文提供明确、原子化的术语定义
- 确保术语与其适用的协议/上下文紧密绑定
- 统一术语的引用方式，减少歧义
- 便于维护和更新术语定义

### 设计思想

术语定义协议基于以下核心理念：

1. **上下文绑定**：术语与其适用的协议文件紧密结合，确保术语在特定上下文中的含义明确。
2. **结构简洁**：采用最小必要的标签结构，便于维护和理解。
3. **引用明确**：使用特定符号系统引用术语，确保人类和AI都能识别术语引用。
4. **隐式作用域**：术语通过所在文件自动获得作用域，无需显式声明。
5. **自包含性**：协议文件包含其相关术语定义，提高文档完整性。

## 📝 语法定义

```ebnf
(* EBNF形式化定义 *)
terminologies_element ::= '<terminologies>' terminology+ '</terminologies>'
terminology_element ::= '<terminology>' terminology_content '</terminology>'
terminology_content ::= zh_element en_element definition_element examples_element
zh_element ::= '<zh>' text '</zh>'
en_element ::= '<en>' text '</en>'
definition_element ::= '<definition>' markdown_content '</definition>'
examples_element ::= '<examples>' example+ '</examples>'
example_element ::= '<example>' markdown_content '</example>'

text ::= (* 任何文本内容 *)
markdown_content ::= (* 任何有效的Markdown文本 *)
```

## 🧩 语义说明

### 术语定义结构

术语定义使用`<terminologies>`标签包含一组术语，每个术语使用`<terminology>`标签定义：

- **`<terminology>`**：单个术语的定义容器
- **`<zh>`**：术语的中文名称
- **`<en>`**：术语的英文名称
- **`<definition>`**：术语的详细定义，可同时包含AI理解和系统实现层面的解释
- **`<examples>`**：包含一个或多个`<example>`标签，提供使用示例

每个协议文件末尾使用标题"## 🔖 术语定义"引入术语定义部分，将术语定义与协议正文分隔。

### `#` 引用协议

术语定义协议规定了如何使用`#`符号作为统一的术语引用方式，遵循以下核心语法规则：

```ebnf
term_reference ::= '#' term_name [' ']
term_name ::= (* 术语的中文名称或英文名称 *)
```

#### 术语引用

术语引用在术语名称前使用井号标记：

| 语法 | 描述 | 示例 |
|------|------|------|
| `#术语` | 引用上下文中定义的术语 | `#协议绑定` |

基本术语引用在术语名称前使用井号，引用当前文档上下文中定义的术语：
```
#术语名称
```

例如：
- `#协议绑定` - 引用当前上下文中定义的"协议绑定"术语
- `#标签嵌套` - 引用当前上下文中定义的"标签嵌套"术语

推荐在术语后添加空格与其他内容分隔：`#术语 后续内容`，但这不是强制要求。

#### 与 Markdown 的兼容性

为了避免与 Markdown 的标题语法冲突，DPML 采用以下解析规则：

1. **行首 # 符号**：
   - 当 # 出现在行首时，按照 Markdown 语法解析为标题
   - 例如：`# 这是一个标题`

2. **非行首 # 符号**：
   - 当 # 出现在行中或词首时，解析为术语引用
   - 例如：`这里引用了一个 #术语`

3. **行首术语引用**：
   - 如果需要在行首使用术语引用，可以添加空格或其他字符
   - 例如：` #术语` 或 `文本 #术语`

这种设计保持了与 Markdown 的兼容性，同时提供了清晰的术语引用机制。

### 作用域规则

**隐式作用域**：术语自动具有其所在文档和上下文的作用域，这意味着：
- 术语的定义和使用范围由其所在的文档上下文决定
- 同一术语在不同上下文中可能有不同的定义
- 使用术语时应考虑当前所处的上下文环境
