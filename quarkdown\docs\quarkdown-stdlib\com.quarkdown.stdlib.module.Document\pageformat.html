<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>pageformat</title>
<link href="../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer></script>
<link href="../../images/logo-icon.svg">
<link href="../../styles/stylesheet.css" rel="Stylesheet"></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../index.html">
                    quarkdown
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">1.6.3
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":quarkdown-stdlib/main">jvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":quarkdown-stdlib/main" data-filter=":quarkdown-stdlib/main">
                                <span class="checkbox--icon"></span>
                                jvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    quarkdown
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="member" id="content" pageids="quarkdown-stdlib::com.quarkdown.stdlib//pageFormat/#com.quarkdown.core.context.Context#com.quarkdown.core.document.layout.page.PageSizeFormat?#com.quarkdown.core.document.layout.page.PageOrientation#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Sizes?#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?#com.quarkdown.core.document.size.Size?#com.quarkdown.core.misc.color.Color?#kotlin.Int?#com.quarkdown.core.ast.quarkdown.block.Container.TextAlignment?/PointingToDeclaration//742850071">
  <div class="breadcrumbs"><a href="../index.html">quarkdown-stdlib</a><span class="delimiter">/</span><a href="index.html">com.quarkdown.stdlib.module.Document</a><span class="delimiter">/</span><span class="current">pageformat</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>pageformat</span></span></h1>
  </div>
  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">pageformat</span><span class="token punctuation"> </span><span class="token constant">size</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat///PointingToDeclaration/">PageSizeFormat</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">    </span><span class="token punctuation"> </span><span class="token constant">orientation</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageOrientation///PointingToDeclaration/">PageOrientation</span><span class="token operator"> = </span>context.documentInfo.type.preferredOrientation<span class="token punctuation">}</span><br><span class="token punctuation">          </span><span class="token punctuation"> </span><span class="token constant">width</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">height</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">         </span><span class="token punctuation"> </span><span class="token constant">margin</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Sizes///PointingToDeclaration/">Sizes</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">      </span><span class="token punctuation"> </span><span class="token constant">bordertop</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">    </span><span class="token punctuation"> </span><span class="token constant">borderright</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">   </span><span class="token punctuation"> </span><span class="token constant">borderbottom</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">     </span><span class="token punctuation"> </span><span class="token constant">borderleft</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.document.size/Size///PointingToDeclaration/">Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">    </span><span class="token punctuation"> </span><span class="token constant">bordercolor</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.misc.color/Color///PointingToDeclaration/">Color</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">        </span><span class="token punctuation"> </span><span class="token constant">columns</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">      </span><span class="token punctuation"> </span><span class="token constant">alignment</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment///PointingToDeclaration/">Container.TextAlignment</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Void///PointingToDeclaration/">Void</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><p class="paragraph">Sets the format of the document. If a value is unset, the default value supplied by the underlying renderer is used.</p><p class="paragraph">If both <a href="pageformat.html">size</a> and <a href="pageformat.html">width</a> or <a href="pageformat.html">height</a> are set, the latter override the former. If both <a href="pageformat.html">size</a> and <a href="pageformat.html">width</a> or <a href="pageformat.html">height</a> are unset, the default value is used.</p><p class="paragraph">If any of <a href="pageformat.html">bordertop</a>, <a href="pageformat.html">borderright</a>, <a href="pageformat.html">borderbottom</a>, <a href="pageformat.html">borderleft</a> or <a href="pageformat.html">bordercolor</a> is set, the border will be applied around the content area of each page. If only <a href="pageformat.html">bordercolor</a> is set, the border will be applied with a default width to each side. Border is not supported in plain documents.</p><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>size</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">standard size format of each page (overridden by <a href="pageformat.html">width</a> and <a href="pageformat.html">height</a>)</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A0///PointingToDeclaration/"><code class="lang-kotlin">a0</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A1///PointingToDeclaration/"><code class="lang-kotlin">a1</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A2///PointingToDeclaration/"><code class="lang-kotlin">a2</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A3///PointingToDeclaration/"><code class="lang-kotlin">a3</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A4///PointingToDeclaration/"><code class="lang-kotlin">a4</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A5///PointingToDeclaration/"><code class="lang-kotlin">a5</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A6///PointingToDeclaration/"><code class="lang-kotlin">a6</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A7///PointingToDeclaration/"><code class="lang-kotlin">a7</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A8///PointingToDeclaration/"><code class="lang-kotlin">a8</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A9///PointingToDeclaration/"><code class="lang-kotlin">a9</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.A10///PointingToDeclaration/"><code class="lang-kotlin">a10</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.B0///PointingToDeclaration/"><code class="lang-kotlin">b0</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.B1///PointingToDeclaration/"><code class="lang-kotlin">b1</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.B2///PointingToDeclaration/"><code class="lang-kotlin">b2</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.B3///PointingToDeclaration/"><code class="lang-kotlin">b3</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.B4///PointingToDeclaration/"><code class="lang-kotlin">b4</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.B5///PointingToDeclaration/"><code class="lang-kotlin">b5</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.LETTER///PointingToDeclaration/"><code class="lang-kotlin">letter</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.LEGAL///PointingToDeclaration/"><code class="lang-kotlin">legal</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageSizeFormat.LEDGER///PointingToDeclaration/"><code class="lang-kotlin">ledger</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>orientation</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">orientation of each page.     If not specified, the preferred orientation of the document type is used.     Does not take effect if <a href="pageformat.html">size</a> is not specified.</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageOrientation.PORTRAIT///PointingToDeclaration/"><code class="lang-kotlin">portrait</code></span></li><li><span data-unresolved-link="com.quarkdown.core.document.layout.page/PageOrientation.LANDSCAPE///PointingToDeclaration/"><code class="lang-kotlin">landscape</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>width</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">width of each page</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>height</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">height of each page</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>margin</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">blank space around the content of each page. Not supported in slides documents</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>bordertop</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">border width of the top content area of each page</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>borderright</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">border width of the right content area of each page</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>borderbottom</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">border width of the bottom content area of each page</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>borderleft</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">border width of the left content area of each page</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>bordercolor</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">color of the border around the content area of each page</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>columns</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">positive number of columns on each page.     If set and greater than 1, the layout becomes multi-column. If &lt; 1, the value is discarded</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>alignment</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">text alignment of the content on each page</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment.START///PointingToDeclaration/"><code class="lang-kotlin">start</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment.CENTER///PointingToDeclaration/"><code class="lang-kotlin">center</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment.END///PointingToDeclaration/"><code class="lang-kotlin">end</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.block/Container.TextAlignment.JUSTIFY///PointingToDeclaration/"><code class="lang-kotlin">justify</code></span></li></ul></div></div></div></div></div><span class="kdoc-tag"><h4 class="kdoctag">Wiki page</h4><a href="https://github.com/iamgio/quarkdown/wiki/Page+format">Page format</a></span></div></div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Quarkdown</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
