# 俄罗斯方块游戏主程序入口
import sys
import traceback
from game import Game

def main():
    """主程序入口"""
    print("=" * 50)
    print("      可爱俄罗斯方块游戏")
    print("=" * 50)
    print()
    print("控制说明:")
    print("  ← → : 移动方块")
    print("  ↑   : 旋转方块")
    print("  ↓   : 软下落")
    print("  P   : 暂停/恢复")
    print("  R   : 重新开始 (游戏结束时)")
    print("  ESC : 退出游戏")
    print()
    print("游戏特色:")
    print("  • 可爱风格的界面设计")
    print("  • 7种经典俄罗斯方块形状")
    print("  • 等级系统和速度递增")
    print("  • 高分记录保存")
    print("  • 幽灵方块显示")
    print()
    
    try:
        # 创建并运行游戏
        game = Game()
        print("游戏启动中...")
        print()
        
        # 运行游戏主循环
        game.run()
        
    except KeyboardInterrupt:
        print("\\n游戏被用户中断")
    except Exception as e:
        print(f"\\n游戏运行出错: {e}")
        print("错误详情:")
        traceback.print_exc()
    finally:
        print("\\n感谢游玩可爱俄罗斯方块！")
        print("再见！")

if __name__ == "__main__":
    main()