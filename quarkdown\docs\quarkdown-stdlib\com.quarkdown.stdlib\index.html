<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>com.quarkdown.stdlib</title>
<link href="../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer></script>
<link href="../../images/logo-icon.svg">
<link href="../../styles/stylesheet.css" rel="Stylesheet"></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../index.html">
                    quarkdown
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">1.6.3
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":quarkdown-stdlib/main">jvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":quarkdown-stdlib/main" data-filter=":quarkdown-stdlib/main">
                                <span class="checkbox--icon"></span>
                                jvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    quarkdown
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="package" id="content" pageids="quarkdown-stdlib::com.quarkdown.stdlib////PointingToDeclaration//742850071">
  <div class="breadcrumbs"><a href="../index.html">quarkdown-stdlib</a><span class="delimiter">/</span><span class="current">com.quarkdown.stdlib</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Package-level</span></span> <span><span>declarations</span></span></h1>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="TYPE">Types</button><button class="section-tab" data-togglable="PROPERTY,EXTENSION_PROPERTY">Properties</button></div>
    <div class="tabs-section-body">
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="1056879724%2FClasslikes%2F742850071" anchor-label="BibliographyStyle" id="1056879724%2FClasslikes%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-bibliography-style/index.html"><span>Bibliography</span><wbr><span><span>Style</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1056879724%2FClasslikes%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">enum </span><a href="-bibliography-style/index.html">BibliographyStyle</a> : <a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-enum/index.html">Enum</a><span class="token operator">&lt;</span><a href="-bibliography-style/index.html">BibliographyStyle</a><span class="token operator">&gt; </span></div><div class="brief "><p class="paragraph">Bibliography styles supported by <a href="../com.quarkdown.stdlib.module.Bibliography/bibliography.html">bibliography</a>. See <a href="https://www.overleaf.com/learn/latex/Bibtex_bibliography_styles">here</a> for examples of each style.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="908971621%2FClasslikes%2F742850071" anchor-label="Stdlib" id="908971621%2FClasslikes%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-stdlib/index.html"><span><span>Stdlib</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="908971621%2FClasslikes%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">object </span><a href="-stdlib/index.html">Stdlib</a> : <span data-unresolved-link="com.quarkdown.core.function.library/LibraryExporter///PointingToDeclaration/">LibraryExporter</span></div><div class="brief "><p class="paragraph">Exporter of Quarkdown's standard library.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2033482181%2FClasslikes%2F742850071" anchor-label="TableSortOrder" id="2033482181%2FClasslikes%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-table-sort-order/index.html"><span>Table</span><wbr><span>Sort</span><wbr><span><span>Order</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2033482181%2FClasslikes%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">enum </span><a href="-table-sort-order/index.html">TableSortOrder</a> : <a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-enum/index.html">Enum</a><span class="token operator">&lt;</span><a href="-table-sort-order/index.html">TableSortOrder</a><span class="token operator">&gt; </span></div><div class="brief "><p class="paragraph">The sorting order for a table column.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="PROPERTY">
        <h2 class="">Properties</h2>
        <div class="table"><a data-name="343381846%2FProperties%2F742850071" anchor-label="Bibliography" id="343381846%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-bibliography.html"><span><span>Bibliography</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="343381846%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-bibliography.html">Bibliography</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Bibliography</code> stdlib module exporter. This module handles bibliographies and citations.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-2092893606%2FProperties%2F742850071" anchor-label="Collection" id="-2092893606%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-collection.html"><span><span>Collection</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2092893606%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-collection.html">Collection</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Collection</code> stdlib module exporter. This module handles iterable collections.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1753958642%2FProperties%2F742850071" anchor-label="Data" id="-1753958642%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-data.html"><span><span>Data</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1753958642%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-data.html">Data</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Data</code> stdlib module exporter. This module handles content fetched from external resources.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-303767710%2FProperties%2F742850071" anchor-label="Dictionary" id="-303767710%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-dictionary.html"><span><span>Dictionary</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-303767710%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-dictionary.html">Dictionary</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Dictionary</code> stdlib module exporter. This module handles map-like dictionaries.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1096855235%2FProperties%2F742850071" anchor-label="Document" id="-1096855235%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-document.html"><span><span>Document</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1096855235%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-document.html">Document</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Document</code> stdlib module exporter. This module handles document information and details.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1969281054%2FProperties%2F742850071" anchor-label="Ecosystem" id="-1969281054%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-ecosystem.html"><span><span>Ecosystem</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1969281054%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-ecosystem.html">Ecosystem</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Ecosystem</code> stdlib module exporter. This module handles interaction between Quarkdown sources.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1294443018%2FProperties%2F742850071" anchor-label="Flow" id="1294443018%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-flow.html"><span><span>Flow</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1294443018%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-flow.html">Flow</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Flow</code> stdlib module exporter. This module handles the control flow and other statements.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="439937003%2FProperties%2F742850071" anchor-label="Injection" id="439937003%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-injection.html"><span><span>Injection</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="439937003%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-injection.html">Injection</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Injection</code> stdlib module exporter. This module handles code injection of different languages.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1313315022%2FProperties%2F742850071" anchor-label="Layout" id="1313315022%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-layout.html"><span><span>Layout</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1313315022%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-layout.html">Layout</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Layout</code> stdlib module exporter. This module handles position and shape of an element.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1277531705%2FProperties%2F742850071" anchor-label="Library" id="-1277531705%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-library.html"><span><span>Library</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1277531705%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-library.html">Library</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Library</code> stdlib module exporter. This module handles loaded libraries and their functions.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="457789439%2FProperties%2F742850071" anchor-label="Localization" id="457789439%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-localization.html"><span><span>Localization</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="457789439%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-localization.html">Localization</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Localization</code> stdlib module exporter. This module handles localization-related features.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1355742504%2FProperties%2F742850071" anchor-label="Logger" id="1355742504%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-logger.html"><span><span>Logger</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1355742504%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-logger.html">Logger</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Logger</code> stdlib module exporter. This module contains logging utility.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1139399111%2FProperties%2F742850071" anchor-label="Logical" id="-1139399111%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-logical.html"><span><span>Logical</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1139399111%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-logical.html">Logical</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Logical</code> stdlib module exporter.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="406127984%2FProperties%2F742850071" anchor-label="Math" id="406127984%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-math.html"><span><span>Math</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="406127984%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-math.html">Math</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Math</code> stdlib module exporter.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-101358663%2FProperties%2F742850071" anchor-label="Mermaid" id="-101358663%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-mermaid.html"><span><span>Mermaid</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-101358663%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-mermaid.html">Mermaid</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Mermaid</code> stdlib module exporter. This module handles generation of Mermaid diagrams.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1335030444%2FProperties%2F742850071" anchor-label="NOT_FOUND" id="1335030444%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-n-o-t_-f-o-u-n-d.html"><span>NOT_</span><wbr><span>FOUND</span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1335030444%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-n-o-t_-f-o-u-n-d.html">NOT_FOUND</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.value/Any///PointingToDeclaration/">Any</span></div><div class="brief "><p class="paragraph">Fallback value for non-existent elements in collections, dictionaries, and more.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-258343404%2FProperties%2F742850071" anchor-label="Optionality" id="-258343404%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-optionality.html"><span><span>Optionality</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-258343404%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-optionality.html">Optionality</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Optionality</code> stdlib module exporter. This module handles <code class="lang-kotlin">None</code> values to express optional values.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1663859222%2FProperties%2F742850071" anchor-label="Slides" id="1663859222%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-slides.html"><span><span>Slides</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1663859222%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-slides.html">Slides</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Slides</code> stdlib module exporter. This module handles slides properties.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2116007367%2FProperties%2F742850071" anchor-label="String" id="2116007367%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-string.html"><span><span>String</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2116007367%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-string.html">String</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">String</code> stdlib module exporter. This module handles string manipulation.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="309716991%2FProperties%2F742850071" anchor-label="TableComputation" id="309716991%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-table-computation.html"><span>Table</span><wbr><span><span>Computation</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="309716991%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-table-computation.html">TableComputation</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">TableComputation</code> stdlib module exporter. This module provides advanced functionality for tables, enhancing their capabilities beyond basic data representation. It adds dynamic operations like sorting, filtering, calculations.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-132347157%2FProperties%2F742850071" anchor-label="Text" id="-132347157%2FProperties%2F742850071" data-filterable-set=":quarkdown-stdlib/main"></a>
          <div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-text.html"><span><span>Text</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-132347157%2FProperties%2F742850071"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="symbol monospace"><span class="token keyword">val </span><a href="-text.html">Text</a><span class="token operator">: </span><span data-unresolved-link="com.quarkdown.core.function.library.loader/Module///PointingToDeclaration/">Module</span></div><div class="brief "><p class="paragraph"><code class="lang-kotlin">Text</code> stdlib module exporter. This module handles text formatting.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Quarkdown</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
