<terminologies>
  <terminology>
    <zh>角色提示单元</zh>
    <en>Role Prompt Unit</en>
    <definition>
      由<role>标签及其子标签（如personality、principle、knowledge、experience、action）构成的、表达AI角色结构与行为的结构化提示词单元。常简称为"角色单元"，两者等同。
    </definition>
    <examples>
      <example>"所有AI角色定义都应以 #角色提示单元 组织。"</example>
      <example>"每个 #角色单元 都可以独立复用。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>角色单元</zh>
    <en>Role Unit</en>
    <definition>
      "角色提示单元"的简称，含义完全等同。参见"角色提示单元"。
    </definition>
    <examples>
      <example>"请将你的角色设计拆分为多个 #角色单元。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>人格</zh>
    <en>Personality</en>
    <definition>
      在本协议中，#人格 专指 <personality> 标签及其结构单元，定义角色的#思维模式。
    </definition>
    <examples>
      <example>"#人格 决定角色的思考风格。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>原则</zh>
    <en>Principle</en>
    <definition>
      在本协议中，#原则 专指 <principle> 标签及其结构单元，定义角色的#行为模式。
    </definition>
    <examples>
      <example>"#原则 约束角色的行为边界。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>知识</zh>
    <en>Knowledge</en>
    <definition>
      在本协议中，#知识 专指 <knowledge> 标签及其结构单元，定义角色的#先验知识库。
    </definition>
    <examples>
      <example>"#知识 提供角色的专业背景。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>经验</zh>
    <en>Experience</en>
    <definition>
      在本协议中，#经验 专指 <experience> 标签及其结构单元，定义角色的#记忆模式。
    </definition>
    <examples>
      <example>"#经验 影响角色的记忆处理方式。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>激活</zh>
    <en>Action</en>
    <definition>
      在本协议中，#激活 专指 <action> 标签及其结构单元，定义角色的初始化和执行入口。
    </definition>
    <examples>
      <example>"#激活 决定角色的启动流程。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>角色合成</zh>
    <en>Role Composition</en>
    <definition>
      在本协议中，#角色合成 指通过#思维模式、#行为模式、#记忆模式 三大协议组合构建角色的机制。
    </definition>
    <examples>
      <example>"#角色合成 支持灵活定制AI能力。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>角色模式</zh>
    <en>Role Mode</en>
    <definition>
      在本协议中，#角色模式 指角色内部多种模式（如#思维模式、#行为模式、#记忆模式）的组合。
    </definition>
    <examples>
      <example>"不同AI可采用不同 #角色模式。"</example>
    </examples>
  </terminology>
  <terminology>
    <zh>角色初始化</zh>
    <en>Role Initialization</en>
    <definition>
      在本协议中，#角色初始化 指角色从定义到执行的激活过程。
    </definition>
    <examples>
      <example>"#角色初始化 包含资源加载、记忆系统启动等步骤。"</example>
    </examples>
  </terminology>
</terminologies> 