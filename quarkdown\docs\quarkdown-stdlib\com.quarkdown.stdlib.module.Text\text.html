<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>text</title>
<link href="../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../scripts/sourceset_dependencies.js" async></script>
<link href="../../styles/style.css" rel="Stylesheet">
<link href="../../styles/main.css" rel="Stylesheet">
<link href="../../styles/prism.css" rel="Stylesheet">
<link href="../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../scripts/symbol-parameters-wrapper_deferred.js" defer></script>
<link href="../../images/logo-icon.svg">
<link href="../../styles/stylesheet.css" rel="Stylesheet"></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../index.html">
                    quarkdown
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">1.6.3
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":quarkdown-stdlib/main">jvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":quarkdown-stdlib/main" data-filter=":quarkdown-stdlib/main">
                                <span class="checkbox--icon"></span>
                                jvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    quarkdown
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="member" id="content" pageids="quarkdown-stdlib::com.quarkdown.stdlib//text/#com.quarkdown.core.ast.InlineMarkdownContent#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Size?#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Weight?#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Style?#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Decoration?#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Case?#com.quarkdown.core.ast.quarkdown.inline.TextTransformData.Variant?#com.quarkdown.core.misc.color.Color?#kotlin.String?/PointingToDeclaration//742850071">
  <div class="breadcrumbs"><a href="../index.html">quarkdown-stdlib</a><span class="delimiter">/</span><a href="index.html">com.quarkdown.stdlib.module.Text</a><span class="delimiter">/</span><span class="current">text</span></div>
  <div class="cover ">
    <h1 class="cover"><span><span>text</span></span></h1>
  </div>
  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":quarkdown-stdlib/main"><div class="sample-container"><pre><code class="block lang-kotlin" theme="idea"><span class="token punctuation">.</span><span class="token function">text</span><span class="token punctuation"> </span><span class="token punctuation"> </span><span class="token constant">text</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast/InlineMarkdownContent///PointingToDeclaration/">InlineMarkdownContent</span><span class="token punctuation">}</span><br><span class="token punctuation">      </span><span class="token punctuation"> </span><span class="token constant">size</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Size///PointingToDeclaration/">TextTransformData.Size</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">    </span><span class="token punctuation"> </span><span class="token constant">weight</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Weight///PointingToDeclaration/">TextTransformData.Weight</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">     </span><span class="token punctuation"> </span><span class="token constant">style</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Style///PointingToDeclaration/">TextTransformData.Style</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation"> </span><span class="token constant">decoration</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Decoration///PointingToDeclaration/">TextTransformData.Decoration</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">      </span><span class="token punctuation"> </span><span class="token constant">case</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Case///PointingToDeclaration/">TextTransformData.Case</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">   </span><span class="token punctuation"> </span><span class="token constant">variant</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Variant///PointingToDeclaration/">TextTransformData.Variant</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">     </span><span class="token punctuation"> </span><span class="token constant">color</span><span class="token operator">:</span><span class="token punctuation">{</span><span data-unresolved-link="com.quarkdown.core.misc.color/Color///PointingToDeclaration/">Color</span><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token punctuation">       </span><span class="token punctuation"> </span><span class="token constant">url</span><span class="token operator">:</span><span class="token punctuation">{</span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a><span class="token operator">?</span><span class="token operator"> = </span>null<span class="token punctuation">}</span><br><span class="token operator">-&gt; </span><span data-unresolved-link="com.quarkdown.core.function.value/Node///PointingToDeclaration/">Node</span></code></pre><span class="top-right-position"><span class="copy-icon"></span><div class="copy-popup-wrapper popup-to-left"><span class="copy-popup-icon"></span><span>Content copied to clipboard</span></div></span></div><p class="paragraph">Creates an inline text node with specified formatting and transformation.</p><h4 class="">Parameters</h4><div class="table"><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>text</span></span></u></div></span></div><div><div class="title"><p class="paragraph">inline content to transform</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>size</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">font size, or default if not specified</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Size.TINY///PointingToDeclaration/"><code class="lang-kotlin">tiny</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Size.SMALL///PointingToDeclaration/"><code class="lang-kotlin">small</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Size.NORMAL///PointingToDeclaration/"><code class="lang-kotlin">normal</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Size.MEDIUM///PointingToDeclaration/"><code class="lang-kotlin">medium</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Size.LARGER///PointingToDeclaration/"><code class="lang-kotlin">larger</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Size.LARGE///PointingToDeclaration/"><code class="lang-kotlin">large</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Size.HUGE///PointingToDeclaration/"><code class="lang-kotlin">huge</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>weight</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">font weight, or default if not specified</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Weight.NORMAL///PointingToDeclaration/"><code class="lang-kotlin">normal</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Weight.BOLD///PointingToDeclaration/"><code class="lang-kotlin">bold</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>style</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">font style, or default if not specified</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Style.NORMAL///PointingToDeclaration/"><code class="lang-kotlin">normal</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Style.ITALIC///PointingToDeclaration/"><code class="lang-kotlin">italic</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>decoration</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">text decoration, or default if not specified</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Decoration.NONE///PointingToDeclaration/"><code class="lang-kotlin">none</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Decoration.UNDERLINE///PointingToDeclaration/"><code class="lang-kotlin">underline</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Decoration.OVERLINE///PointingToDeclaration/"><code class="lang-kotlin">overline</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Decoration.UNDEROVERLINE///PointingToDeclaration/"><code class="lang-kotlin">underoverline</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Decoration.STRIKETHROUGH///PointingToDeclaration/"><code class="lang-kotlin">strikethrough</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Decoration.ALL///PointingToDeclaration/"><code class="lang-kotlin">all</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>case</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">text case, or default if not specified</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Case.NONE///PointingToDeclaration/"><code class="lang-kotlin">none</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Case.UPPERCASE///PointingToDeclaration/"><code class="lang-kotlin">uppercase</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Case.LOWERCASE///PointingToDeclaration/"><code class="lang-kotlin">lowercase</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Case.CAPITALIZE///PointingToDeclaration/"><code class="lang-kotlin">capitalize</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>variant</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">font variant, or default if not specified</p><h4 class="">Values</h4><ul><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Variant.NORMAL///PointingToDeclaration/"><code class="lang-kotlin">normal</code></span></li><li><span data-unresolved-link="com.quarkdown.core.ast.quarkdown.inline/TextTransformData.Variant.SMALL_CAPS///PointingToDeclaration/"><code class="lang-kotlin">smallcaps</code></span></li></ul></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>color</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">text color, or default if not specified</p></div></div></div></div><div class="table-row" data-filterable-current=":quarkdown-stdlib/main" data-filterable-set=":quarkdown-stdlib/main"><div class="main-subrow keyValue "><div class=""><span class="inline-flex"><div><u><span><span>url</span></span></u></div></span></div><div><div class="title"><dl><ul><li>Optional</li><li>Likely <a href="https://github.com/iamgio/quarkdown/wiki/syntax-of-a-function-call">named</a></li></ul></dl><p class="paragraph">optional URL to link the text to. If empty (but specified), the URL will match the text content.</p></div></div></div></div></div></div></div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Quarkdown</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>
