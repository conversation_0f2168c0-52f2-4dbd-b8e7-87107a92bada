<execution domain="prompt-engineering">
  <process>
    # 执行模式提示词开发流程
    
    ```mermaid
    flowchart TD
      A[明确执行目标] --> B[定义核心流程]
      B --> C[制定指导原则]
      C --> D[设定强制规则]
      D --> E[识别约束条件]
      E --> F[确立评价标准]
      F --> G[整合验证执行模式]
      G --> H{执行模式验证}
      H -->|通过| I[完成执行模式]
      H -->|不通过| J[修改调整]
      J --> B
    ```
    
    ## 核心步骤详解
    
    1. **明确执行目标**
       - 确定执行模式的核心任务和目标
       - 明确执行对象和预期结果
    
    2. **定义核心流程**
       - 通过流程图或有序步骤表达执行路径
       - 包含正常路径和异常处理路径
    
    3. **多维度设计**
       - 流程(Process): 详细的执行步骤和路径
       - 指导原则(Guideline): 建议性的最佳实践
       - 规则(Rule): 强制性的必须遵守的原则
       - 约束(Constraint): 客观存在的限制条件
       - 标准(Criteria): 评价执行结果的标准
    
    4. **整合验证**
       - 确保五大元素之间的一致性和完整性
       - 验证执行模式的可行性和有效性
  </process>
  
  <guideline>
    ### 表达方式建议
    
    - **流程(Process)应使用图形表达**
      - 优先使用流程图或时序图
      - 补充关键步骤的文字说明
      - 示例：
      ```mermaid
      flowchart TD
        A[开始] --> B{条件判断}
        B -->|条件成立| C[执行步骤1]
        B -->|条件不成立| D[执行步骤2]
      ```
    
    - **指导原则(Guideline)适合使用列表表达**
      - 使用无序列表突出建议性质
      - 保持简洁明了，便于理解
      - 示例：
      ```
      - 提供用户友好的错误信息
      - 对敏感操作进行二次确认
      ```
    
    - **规则(Rule)适合使用编号列表表达**
      - 使用编号强调必须遵守的性质
      - 确保表述清晰无歧义
      - 示例：
      ```
      1. 密码必须包含大小写字母、数字和特殊字符
      2. 敏感数据传输必须使用加密通道
      ```
    
    - **约束(Constraint)适合使用分类列表表达**
      - 按约束类型组织内容
      - 明确表达限制条件
      - 示例：
      ```
      技术约束：
      - 服务器内存限制: 16GB
      
      业务约束：
      - 用户年龄限制: >13岁
      ```
    
    - **标准(Criteria)适合使用表格表达**
      - 清晰展示指标和目标值
      - 必要时包含不通过标准
      - 示例：
      ```
      | 指标 | 目标值 | 最低要求 |
      |-----|-------|---------|
      | 响应时间 | <200ms | <500ms |
      ```
    
    ### 组织结构建议
    
    - 按照Process → Guideline → Rule → Constraint → Criteria的顺序组织
    - 元素间保持逻辑一致性，避免矛盾
    - 优先考虑必要元素，不强制使用全部五种子标签
  </guideline>
  
  <rule>
    1. **五元素一致性** - Process、Guideline、Rule、Constraint和Criteria之间必须保持逻辑一致
    2. **Process流程图形化** - 流程部分必须包含至少一个图形化表达
    3. **Rule明确强制性** - 规则必须使用明确的、不含模糊表述的语言
    4. **Constraint客观性** - 约束必须反映客观存在的限制，而非主观设定
    5. **Criteria可度量性** - 评价标准必须可度量，包含明确的指标和目标值
    6. **异常路径完备性** - 流程必须包含正常路径和异常处理路径
    7. **层次结构清晰** - 各元素内部应保持合理的层次结构，避免平铺直叙
    8. **资源必须注册** - 创建新的execution资源后必须在 resource/execution.resource.md 中注册，否则@引用将无法正常工作
  </rule>
  
  <constraint>
    1. **元素复杂度限制** - 单个元素内容不宜过于复杂，保持认知负荷合理
    2. **流程步骤限制** - 主流程步骤建议控制在7±2个，符合人类短期记忆容量
    3. **表达方式限制** - 表达方式受目标环境支持的格式限制
    4. **执行环境限制** - 必须考虑实际执行环境的能力边界
    5. **集成兼容性限制** - 执行模式必须能与其他协议(思考、记忆等)协同工作
  </constraint>
  
  <criteria>
    | 指标 | 通过标准 | 不通过标准 |
    |------|---------|-----------|
    | 流程清晰度 | 执行路径明确无歧义 | 步骤混乱或缺失关键节点 |
    | 规则明确性 | 规则表述精确可执行 | 规则模糊或自相矛盾 |
    | 约束合理性 | 约束反映客观限制 | 约束不合理或过度限制 |
    | 标准可度量性 | 标准包含具体可测量指标 | 标准笼统难以评估 |
    | 结构完整性 | 五大元素协调一致 | 元素间逻辑矛盾或重大缺失 |
    | 异常处理 | 包含完善的异常处理路径 | 缺少异常情况考虑 |
    | 可执行性 | 能够指导实际执行 | 过于理论化难以落地 |
    | 表达适当性 | 各元素使用合适的表达方式 | 表达方式与内容不匹配 |
  </criteria>
</execution> 