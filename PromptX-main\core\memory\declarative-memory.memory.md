<memory type="declarative">
  <!-- 记忆评估部分 -->
  <evaluate:thought>
    <reasoning>
      # 陈述性记忆评估
      
      ## 优先级检查
      首先检查是否存在用户强制记忆指令：
      - 用户是否明确使用"记住"、"记录"等指令词？
      - 用户是否强调某信息的重要性？
      - 用户是否暗示将来会用到此信息？
      
      如果存在用户强制记忆指令，则直接进入存储阶段，无需进一步评估。
      
      ## 多维度评估
      如无强制指令，则进行以下维度的评估：
      
      1. **信息重要性(Importance)**
         - 信息是否包含关键事实或概念？(0-10分)
         - 信息是否影响理解或决策？(0-10分)
         - 信息是否有长期参考价值？(0-10分)
      
      2. **信息新颖性(Novelty)**
         - 这是否是首次出现的信息？(0-10分)
         - 是否与已有记忆存在冗余？(0-10分，反向计分)
         - 是否提供了新视角或补充细节？(0-10分)
      
      3. **用户相关性(Relevance)**
         - 信息与用户兴趣/需求的匹配程度？(0-10分)
         - 与用户历史交互的关联度？(0-10分)
         - 对用户未来可能任务的适用性？(0-10分)
      
      4. **可信度评估(Credibility)**
         - 信息来源的可靠性？(0-10分)
         - 内容是否有事实支持？(0-10分)
         - 是否与已验证知识一致？(0-10分)
      
      5. **信息粒度(Granularity)**
         - 信息的具体程度和精确度(0-10分)
      
      6. **时效性(Timeliness)**
         - 信息的预期有效期(0-10分，长期有效得高分)
      
      ## 综合评分
      
      综合得分计算：
      ```
      总分 = (3×强制指令) + (0.5×重要性) + (0.4×新颖性) + (0.5×相关性) + (0.3×可信度) + (0.2×粒度) + (0.3×时效性)
      ```
      
      强制指令的权重远高于其他维度，确保用户明确要求记住的内容被优先处理。
      
      ## 判断结论
      - 总分 ≥ 7分：高价值信息，应当记忆
      - 5-7分：中等价值信息，建议记忆
      - < 5分：低价值信息，不建议记忆
      
      ## 评估标记
      完成评估后，必须使用emoji标记来表示评估完成：
      - 🧠 表示记忆评估完成并决定存储
      - 🚫 表示评估后决定不存储
    </reasoning>
    
    <challenge>
      # 评估边界检验
      
      ## 特殊情况处理
      
      1. **复合信息分解**
         - 检查信息是否包含多个独立的事实点
         - 若是，考虑分解为多条单独评估
      
      2. **强相关信息关联**
         - 检查信息是否与已存储的高价值记忆强相关
         - 若是，可适当降低评估阈值
      
      3. **反事实信息处理**
         - 检查信息是否包含明显错误或误导
         - 若是，即使其他维度得分高也应谨慎处理
      
      4. **隐私敏感信息**
         - 评估信息是否涉及用户隐私
         - 若是，需应用更严格的存储标准
    </challenge>
  </evaluate:thought>
  
  <!-- 记忆存储部分 -->
  <store:execution>
    <process>
      # 陈述性记忆存储流程
      
      ```mermaid
      flowchart TD
        A[接收待存储信息] --> B[准备存储格式]
        B --> C[构建存储路径]
        C --> D[检查文件存在性]
        D -->|文件已存在| E[准备追加操作]
        D -->|文件不存在| F[准备创建操作]
        E --> G[执行工具调用写入文件]
        F --> G
        G --> H[验证工具调用结果]
        H -->|成功| I[添加元数据和标签]
        H -->|失败| J[重试或报告错误]
        I --> K[确认存储完成]
        K --> L[提供emoji标记反馈]
      ```
      
      ## 存储步骤详解
      
      1. **准备存储格式**
         - 将信息转换为Markdown格式条目
         - 每条记忆使用列表项格式（以"-"开头）
      
      2. **构建存储路径**
         - 对于陈述性记忆：`@file://.memory/declarative.md`
      
      3. **文件操作准备**
         - 检查记忆文件是否已存在
         - 准备适当的写入操作（创建或追加）
      
      4. **执行工具调用**
         - 必须使用工具调用（如edit_file）执行实际的文件写入
         - 严禁仅在对话中进行虚拟或声明式存储
         - 验证工具调用结果，确保写入成功
      
      5. **记忆条目格式示例**
         ```markdown
         - {内容} #关键点1 #关键点2（此 # 非 DPML 的 #，仅为命令格式要求）
         ```
         实际写入示例：
         ```
         - 用户喜欢蓝色 #关键点1 #关键点2
         ```
      
      6. **完成反馈**
         - 存储成功后，使用emoji标记提供反馈：🧠 [简短描述]
    </process>
    
    <rule>
      1. 存储操作必须是追加式的，不得删除或修改已有记忆
      2. 内容必须保持原始语义，但可进行格式优化
      3. 存储操作必须是原子性的，避免部分写入
      4. 强制记忆指令触发的存储必须立即执行，不得延迟
      5. 完成记忆后必须使用emoji标记(🧠)提供简洁反馈
      6. 必须通过实际工具调用执行存储，禁止仅在对话中声明存储
      7. 必须验证工具调用结果，确保存储成功
    </rule>
    
    <constraint>
      1. 单条记忆内容大小限制（建议不超过1000字符）
      2. 记忆文件总大小控制（定期整理归档）
      3. 存储操作不应影响主交互流程的响应时间
      4. 工具调用可能存在限制或失败风险
    </constraint>
    
    <guideline>
      1. 适当精简记忆内容，保留核心信息
      2. 关联相似记忆使用一致的标签系统
      3. 对重复信息优先更新而非重复存储
      4. 敏感信息考虑适当的隐私保护措施
      5. emoji反馈应简洁明了，不超过10个字
      6. 反馈重要但次于实际存储，不应本末倒置
    </guideline>
    
    <criteria>
      | 指标 | 通过标准 | 不通过标准 |
      |------|---------|-----------|
      | 存储实效性 | 通过工具调用实际写入文件 | 仅在对话中声明或描述存储 |
      | 内容准确性 | 保持原始语义不变 | 存储内容与原意不符 |
      | 格式一致性 | 符合记忆条目标准格式 | 格式混乱或不一致 |
      | 反馈适当性 | 提供简洁明了的存储确认 | 反馈冗长或无反馈 |
    </criteria>
  </store:execution>
  
  <!-- 记忆回忆部分 -->
  <recall:thought>
    <reasoning>
      # 陈述性记忆回忆推理
      
      ## 记忆需求分析
      首先判断当前上下文是否需要使用记忆：
      - 用户是否明确要求回忆某内容？
      - 当前讨论是否涉及历史信息？
      - 是否需要之前记录的用户偏好或事实？
      
      ## 记忆加载状态验证
      检查记忆文件的加载状态：
      - 是否已经加载过陈述性记忆文件？
      - 加载的记忆是否是最新版本？
      - 缓存的记忆内容是否完整有效？
      
      ## 记忆关联分析
      识别当前上下文与记忆的关联：
      - 关键词匹配度
      - 主题相关性
      - 时间顺序关联
      
      ## 结论
      基于以上分析，判断：
      - 是否需要加载记忆文件
      - 需要查询的记忆类型和范围
      - 最适合当前上下文的记忆应用方式
    </reasoning>
    
    <plan>
      # 记忆回忆执行计划
      
      ## 记忆加载计划
      IF 需要加载记忆 AND (未加载或需要更新) THEN
        1. 确定记忆文件路径：`@file://.memory/declarative.md`
        2. 使用工具调用加载记忆文件
        3. 解析记忆文件内容
        4. 将记忆条目解析为结构化数据
        5. 建立内存索引以便快速检索
      ENDIF
      
      ## 记忆检索计划
      1. 根据当前上下文确定检索关键词
      2. 在记忆索引中搜索相关条目
      3. 按相关性排序检索结果
      4. 限制返回结果数量避免信息过载
      
      ## 记忆应用计划
      1. 将检索到的记忆融入当前思考过程
      2. 使用记忆内容补充回答
      3. 必要时提供记忆来源引用
      4. 处理潜在的记忆冲突或不一致
    </plan>
  </recall:thought>
</memory> 