[base]
is_cloud = 1
platform = 1
tproductId = CP-S001374-5.0
config_type = 0
config_addr = https://gitlab.hundsun.com/MIT5.0
scm_list = administrator,wanghb,jiangjw11408,yuanlh
product_name = 138

[non_base]
analyse_versionts = {"list":[{"sub_git_dev_status":["8","20","17"],"is_add_ls":"1","other":[{}],"version_repository":"mit-mbp5.0","type_git":"0","rules":"dev:MIT5.0-MBP5.0V202201.06.000.20220929"},{"version_repository":"mit-front","type_git":"0","rules":"dev:MIT5.0-FrontV202201.06.000.20220929","sub_git_dev_status":["8","20","17"],"is_add_ls":"1"},{"type_git":"0","sub_git_dev_status":["8","20","17"],"is_add_ls":"1","version_repository":"mit-mbp","rules":"dev:MIT5.0-MBPV202201.06.000.20220929"}],"sub_no_commit_filetypes":".so,.project,.classpath,.pydevproject,.iml,.settings,.idea,.avi,.mp4,.mpg,.mpeg,.wrf,.npmrc,.yarnrc,.apk,.ipa,.wmv,.exe,.rpm,.war,.rar,.zip,.tar,.7z","sub_except_filetypes":null}
branch_control_info = {"list":[{"other":[{}],"branch_control":"^dev","exclude_branch":"dev-master"}]}

