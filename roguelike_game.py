import pygame
import random
import math
from enum import Enum
from dataclasses import dataclass
from typing import List, Tuple, Optional

# 初始化pygame
pygame.init()

# 游戏设置
CELL_SIZE = 32
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
MAP_WIDTH = 20
MAP_HEIGHT = 15

# 颜色定义
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
WALL_COLOR = (100, 100, 100)
FLOOR_COLOR = (200, 200, 200)
PLAYER_COLOR = (0, 255, 0)
ENEMY_COLOR = (255, 0, 0)
ITEM_COLOR = (255, 255, 0)

class EntityType(Enum):
    EMPTY = 0
    WALL = 1
    PLAYER = 2
    ENEMY = 3
    ITEM = 4
    STAIRS = 5

@dataclass
class Position:
    x: int
    y: int

@dataclass
class Stats:
    hp: int
    max_hp: int
    attack: int
    defense: int

class Entity:
    def __init__(self, position: Position, entity_type: EntityType, stats: Stats = None):
        self.position = position
        self.entity_type = entity_type
        self.stats = stats
        self.symbol = self._get_symbol()
    
    def _get_symbol(self) -> str:
        symbols = {
            EntityType.EMPTY: '.',
            EntityType.WALL: '#',
            EntityType.PLAYER: '@',
            EntityType.ENEMY: 'E',
            EntityType.ITEM: '!',
            EntityType.STAIRS: '>'
        }
        return symbols[self.entity_type]

class MapGenerator:
    def __init__(self, width: int, height: int):
        self.width = width
        self.height = height
        self.map_data = []
    
    def generate_room(self, x: int, y: int, w: int, h: int) -> List[Position]:
        room = []
        for i in range(y, min(y + h, self.height - 1)):
            for j in range(x, min(x + w, self.width - 1)):
                if i > 0 and j > 0:
                    self.map_data[i][j] = EntityType.FLOOR
                    room.append(Position(j, i))
        return room
    
    def generate_dungeon(self) -> List[List[EntityType]]:
        self.map_data = [[EntityType.WALL for _ in range(self.width)] 
                        for _ in range(self.height)]
        
        rooms = []
        for _ in range(5):  # 生成5个房间
            room_w = random.randint(4, 8)
            room_h = random.randint(4, 8)
            room_x = random.randint(1, self.width - room_w - 1)
            room_y = random.randint(1, self.height - room_h - 1)
            
            room = self.generate_room(room_x, room_y, room_w, room_h)
            rooms.append(room)
        
        # 确保至少有一个起点
        start_pos = rooms[0][len(rooms[0])//2]
        self.map_data[start_pos.y][start_pos.x] = EntityType.EMPTY
        
        # 添加楼梯（下一层入口）
        if rooms:
            stair_pos = rooms[-1][len(rooms[-1])//2]
            self.map_data[stair_pos.y][stair_pos.x] = EntityType.STAIRS
        
        return self.map_data

class Player(Entity):
    def __init__(self, position: Position):
        super().__init__(position, EntityType.PLAYER, Stats(100, 100, 15, 5))
        self.experience = 0
        self.level = 1
        self.inventory = []
    
    def move(self, dx: int, dy: int, game_map: List[List[EntityType]]) -> bool:
        new_x = self.position.x + dx
        new_y = self.position.y + dy
        
        if 0 <= new_x < len(game_map[0]) and 0 <= new_y < len(game_map):
            if game_map[new_y][new_x] != EntityType.WALL:
                self.position = Position(new_x, new_y)
                return True
        return False
    
    def gain_experience(self, amount: int):
        self.experience += amount
        if self.experience >= self.level * 50:
            self.level_up()
    
    def level_up(self):
        self.level += 1
        self.experience = 0
        self.stats.max_hp += 10
        self.stats.hp = self.stats.max_hp
        self.stats.attack += 3
        self.stats.defense += 2

class Enemy(Entity):
    def __init__(self, position: Position, difficulty: int = 1):
        hp = random.randint(20, 40) * difficulty
        attack = random.randint(5, 10) * difficulty
        defense = random.randint(1, 3) * difficulty
        super().__init__(position, EntityType.ENEMY, Stats(hp, hp, attack, defense))
        self.difficulty = difficulty
    
    def take_turn(self, player_pos: Position) -> Optional[Position]:
        # 简单的AI：向玩家方向移动
        dx = 0
        dy = 0
        
        if self.position.x < player_pos.x:
            dx = 1
        elif self.position