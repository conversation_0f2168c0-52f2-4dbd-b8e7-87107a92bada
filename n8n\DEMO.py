import pygame
import sys
import numpy as np

# 初始化pygame
pygame.init()

# 游戏常量
WIDTH, HEIGHT = 800, 800
BOARD_SIZE = 15
GRID_SIZE = WIDTH // (BOARD_SIZE + 1)
LINE_COLOR = (0, 0, 0)
BACKGROUND_COLOR = (222, 184, 135)  # 棋盘背景色
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
BUTTON_COLOR = (70, 130, 180)  # 按钮颜色
BUTTON_HOVER_COLOR = (30, 100, 150)  # 鼠标悬停时的按钮颜色
PIECE_RADIUS = GRID_SIZE // 2 - 2

# 创建游戏窗口
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("五子棋")

# 初始化棋盘
board = np.zeros((BOARD_SIZE, BOARD_SIZE), dtype=int)
current_player = 1  # 1表示黑棋，2表示白棋
game_over = False
winner = 0

# 记录落子历史，用于悔棋
move_history = []

# 按钮类
class Button:
    def __init__(self, x, y, width, height, text, font_size=24):
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.font = pygame.font.SysFont(["SimHei", "Microsoft YaHei", None], font_size)
        self.is_hovered = False
    
    def draw(self):
        color = BUTTON_HOVER_COLOR if self.is_hovered else BUTTON_COLOR
        pygame.draw.rect(screen, color, self.rect, border_radius=5)
        pygame.draw.rect(screen, BLACK, self.rect, 2, border_radius=5)
        
        text_surface = self.font.render(self.text, True, WHITE)
        text_rect = text_surface.get_rect(center=self.rect.center)
        screen.blit(text_surface, text_rect)
    
    def check_hover(self, pos):
        self.is_hovered = self.rect.collidepoint(pos)
        return self.is_hovered
    
    def is_clicked(self, pos):
        return self.rect.collidepoint(pos)

# 创建按钮
undo_button = Button(WIDTH - 200, HEIGHT - 70, 80, 40, "悔棋")
restart_button = Button(WIDTH - 100, HEIGHT - 70, 80, 40, "重新开始")

# 绘制棋盘
def draw_board():
    screen.fill(BACKGROUND_COLOR)
    
    # 绘制网格线
    for i in range(BOARD_SIZE):
        # 横线
        pygame.draw.line(screen, LINE_COLOR, 
                        (GRID_SIZE, (i + 1) * GRID_SIZE), 
                        ((BOARD_SIZE) * GRID_SIZE, (i + 1) * GRID_SIZE), 2)
        # 竖线
        pygame.draw.line(screen, LINE_COLOR, 
                        ((i + 1) * GRID_SIZE, GRID_SIZE), 
                        ((i + 1) * GRID_SIZE, (BOARD_SIZE) * GRID_SIZE), 2)
    
    # 绘制中心和星位点
    center = BOARD_SIZE // 2 + 1
    star_points = [(center, center), (4, 4), (4, BOARD_SIZE - 3), 
                  (BOARD_SIZE - 3, 4), (BOARD_SIZE - 3, BOARD_SIZE - 3)]
    
    for point in star_points:
        pygame.draw.circle(screen, BLACK, 
                          (point[0] * GRID_SIZE, point[1] * GRID_SIZE), 
                          5)

# 绘制棋子
def draw_pieces():
    for i in range(BOARD_SIZE):
        for j in range(BOARD_SIZE):
            if board[i][j] == 1:  # 黑棋
                pygame.draw.circle(screen, BLACK, 
                                  ((j + 1) * GRID_SIZE, (i + 1) * GRID_SIZE), 
                                  PIECE_RADIUS)
            elif board[i][j] == 2:  # 白棋
                pygame.draw.circle(screen, WHITE, 
                                  ((j + 1) * GRID_SIZE, (i + 1) * GRID_SIZE), 
                                  PIECE_RADIUS)
                # 为白棋添加黑色边框
                pygame.draw.circle(screen, BLACK, 
                                  ((j + 1) * GRID_SIZE, (i + 1) * GRID_SIZE), 
                                  PIECE_RADIUS, 1)

# 检查是否获胜
def check_win(row, col, player):
    # 检查方向：水平、垂直、对角线、反对角线
    directions = [(0, 1), (1, 0), (1, 1), (1, -1)]
    
    for dr, dc in directions:
        count = 1  # 当前落子算一个
        
        # 正向检查
        r, c = row + dr, col + dc
        while 0 <= r < BOARD_SIZE and 0 <= c < BOARD_SIZE and board[r][c] == player:
            count += 1
            r += dr
            c += dc
        
        # 反向检查
        r, c = row - dr, col - dc
        while 0 <= r < BOARD_SIZE and 0 <= c < BOARD_SIZE and board[r][c] == player:
            count += 1
            r -= dr
            c -= dc
        
        if count >= 5:
            return True
    
    return False

# 绘制游戏状态
def draw_status():
    font = pygame.font.SysFont(None, 36)
    if game_over:
        if winner == 1:
            text = font.render("黑棋胜利!", True, BLACK)
        else:
            text = font.render("白棋胜利!", True, BLACK)
    else:
        if current_player == 1:
            text = font.render("当前: 黑棋", True, BLACK)
        else:
            text = font.render("当前: 白棋", True, BLACK)
    
    screen.blit(text, (20, 20))

# 悔棋功能
def undo_move():
    global current_player, game_over, winner
    
    if move_history and not game_over:
        last_row, last_col = move_history.pop()
        board[last_row][last_col] = 0
        current_player = 3 - current_player  # 切换回上一个玩家

# 重新开始游戏
def restart_game():
    global board, current_player, game_over, winner, move_history
    board = np.zeros((BOARD_SIZE, BOARD_SIZE), dtype=int)
    current_player = 1
    game_over = False
    winner = 0
    move_history = []

# 游戏主循环
while True:
    mouse_pos = pygame.mouse.get_pos()
    
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            pygame.quit()
            sys.exit()
        
        if event.type == pygame.MOUSEMOTION:
            undo_button.check_hover(mouse_pos)
            restart_button.check_hover(mouse_pos)
            
        if event.type == pygame.MOUSEBUTTONDOWN:
            # 检查是否点击了按钮
            if undo_button.is_clicked(mouse_pos):
                undo_move()
            elif restart_button.is_clicked(mouse_pos):
                restart_game()
            # 如果游戏未结束，处理落子
            elif not game_over:
                x, y = event.pos
                col = round(x / GRID_SIZE) - 1
                row = round(y / GRID_SIZE) - 1
                
                # 确保点击在有效范围内
                if 0 <= row < BOARD_SIZE and 0 <= col < BOARD_SIZE and board[row][col] == 0:
                    board[row][col] = current_player
                    move_history.append((row, col))
                    
                    # 检查是否获胜
                    if check_win(row, col, current_player):
                        game_over = True
                        winner = current_player
                    
                    # 切换玩家
                    current_player = 3 - current_player  # 1->2, 2->1
    
    # 绘制游戏
    draw_board()
    draw_pieces()
    draw_status()
    undo_button.draw()
    restart_button.draw()
    
    pygame.display.flip()
