# -*- coding: utf-8 -*-
import logging
import logging.handlers
import os
import configParser as configParser

cfg = configParser.myConfigParser()
try:
    # 日志级别
    logLevel = cfg.getConfigValue("common", "log_level").upper()
    # 日志文件路径
    logFile = cfg.getConfigValue("common", "log_file")
except Exception as e:
    # 默认值
    logLevel = 'DEBUG'
    logFile = 'runlog.log'
# print(logFile)
def get_log_level():

    if(logLevel == 'INFO'):
        return logging.INFO
    elif(logLevel == 'DEBUG'):
        return logging.DEBUG
    elif(logLevel == 'ERROR'):
        return logging.ERROR
    elif(logLevel == 'FATAL'):
        return logging.FATAL
    else:
        return logging.DEBUG
def init_logger(log_file = logFile):
    logLevel = get_log_level()
    if os.path.exists(log_file):
        with open(log_file, 'a+') as p:
            p.write("")
    # 指定logger输出格式
    #formatter = logging.Formatter('%(asctime)-15s
    #  %(filename)s [line:%(lineno)d] %(message)s')
    formatter = logging.Formatter('%(asctime)s - [%(levelname)s] - %(message)s - [%(filename)s:%(lineno)s]')
    # 文件日志，指定日志输出到什么文件以及输出格式
    file_handler = logging.handlers.RotatingFileHandler(log_file, 'a', 100*1024*1024, 10)
    file_handler.setFormatter(formatter)
    # 获取logger实例
    logger = logging.getLogger()
    # 为logger添加日志处理器
    logger.addHandler(file_handler)
    # 设置日志最低数据级别，默认级别为warning，日志级别：NOTSET < DEBUG < INFO < WARNING < ERROR < CRITICAL
    logger.setLevel(logLevel)

def get_my_logger(loggerName):
    return logging.getLogger(loggerName)

if __name__ == 'myLogger':
    init_logger()


#
# if __name__ == '__main__':
#     #init_logger()
#
#     import os
#
#     dir_filedir = 'E:\\csfugailvproxy\\tools\\upload\\O4.5-equityV202201.04.000\\20220811.0803'
#     if (os.path.exists(dir_filedir) == False):
#         os.makedirs(dir_filedir)
