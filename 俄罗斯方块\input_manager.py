# 输入管理器
import pygame
import time
from config import CONTROLS

class InputManager:
    """输入管理器，处理键盘事件和用户控制"""
    
    def __init__(self):
        """初始化输入管理器"""
        self.keys_pressed = set()
        self.keys_just_pressed = set()
        self.keys_just_released = set()
        
        # 按键防抖动设置
        self.key_repeat_delay = 0.15  # 首次重复延迟（秒）
        self.key_repeat_rate = 0.05   # 重复间隔（秒）
        self.key_timers = {}          # 按键计时器
        self.key_last_repeat = {}     # 上次重复时间
        
        # 移动控制设置
        self.move_delay = 0.1         # 移动延迟
        self.last_move_time = 0       # 上次移动时间
        
        # 旋转控制设置
        self.rotation_delay = 0.2     # 旋转延迟
        self.last_rotation_time = 0   # 上次旋转时间
        
        # 下落控制设置
        self.soft_drop_rate = 0.05    # 软下落间隔
        self.last_soft_drop_time = 0  # 上次软下落时间
    
    def update(self, events):
        """更新输入状态
        
        Args:
            events: pygame事件列表
        """
        # 清空本帧的按键状态
        self.keys_just_pressed.clear()
        self.keys_just_released.clear()
        
        # 处理事件
        for event in events:
            if event.type == pygame.KEYDOWN:
                key = pygame.key.name(event.key)
                self.keys_pressed.add(key)
                self.keys_just_pressed.add(key)
                
                # 重置按键计时器
                self.key_timers[key] = time.time()
                self.key_last_repeat[key] = time.time()
                
            elif event.type == pygame.KEYUP:
                key = pygame.key.name(event.key)
                if key in self.keys_pressed:
                    self.keys_pressed.remove(key)
                    self.keys_just_released.add(key)
                
                # 清除按键计时器
                if key in self.key_timers:
                    del self.key_timers[key]
                if key in self.key_last_repeat:
                    del self.key_last_repeat[key]
    
    def is_key_pressed(self, key):
        """检查按键是否被按下
        
        Args:
            key: 按键名称
            
        Returns:
            bool: 是否被按下
        """
        return key in self.keys_pressed
    
    def is_key_just_pressed(self, key):
        """检查按键是否刚被按下
        
        Args:
            key: 按键名称
            
        Returns:
            bool: 是否刚被按下
        """
        return key in self.keys_just_pressed
    
    def is_key_just_released(self, key):
        """检查按键是否刚被释放
        
        Args:
            key: 按键名称
            
        Returns:
            bool: 是否刚被释放
        """
        return key in self.keys_just_released
    
    def should_repeat_key(self, key):
        """检查按键是否应该重复触发
        
        Args:
            key: 按键名称
            
        Returns:
            bool: 是否应该重复
        """
        if key not in self.key_timers:
            return False
        
        current_time = time.time()
        key_down_time = current_time - self.key_timers[key]
        
        # 如果按键时间超过初始延迟
        if key_down_time >= self.key_repeat_delay:
            last_repeat_time = current_time - self.key_last_repeat[key]
            
            # 如果距离上次重复的时间超过重复间隔
            if last_repeat_time >= self.key_repeat_rate:
                self.key_last_repeat[key] = current_time
                return True
        
        return False
    
    def can_move_left(self):
        """检查是否可以向左移动"""
        current_time = time.time()
        
        # 刚按下或重复触发
        if (self.is_key_just_pressed('left') or 
            self.should_repeat_key('left')):
            
            # 检查移动延迟
            if current_time - self.last_move_time >= self.move_delay:
                self.last_move_time = current_time
                return True
        
        return False
    
    def can_move_right(self):
        """检查是否可以向右移动"""
        current_time = time.time()
        
        # 刚按下或重复触发
        if (self.is_key_just_pressed('right') or 
            self.should_repeat_key('right')):
            
            # 检查移动延迟
            if current_time - self.last_move_time >= self.move_delay:
                self.last_move_time = current_time
                return True
        
        return False
    
    def can_rotate(self):
        """检查是否可以旋转"""
        current_time = time.time()
        
        # 只在刚按下时触发旋转（不重复）
        if (self.is_key_just_pressed('up') or 
            self.is_key_just_pressed('space')):
            
            # 检查旋转延迟
            if current_time - self.last_rotation_time >= self.rotation_delay:
                self.last_rotation_time = current_time
                return True
        
        return False
    
    def should_soft_drop(self):
        """检查是否应该软下落"""
        current_time = time.time()
        
        # 按住下键时持续软下落
        if self.is_key_pressed('down'):
            if current_time - self.last_soft_drop_time >= self.soft_drop_rate:
                self.last_soft_drop_time = current_time
                return True
        
        return False
    
    def should_hard_drop(self):
        """检查是否应该硬下落"""
        # 硬下落通常绑定到空格键的特殊组合或其他键
        # 这里简化为Shift+Down的组合
        return (self.is_key_just_pressed('space') and 
                self.is_key_pressed('down'))
    
    def is_pause_pressed(self):
        """检查是否按下暂停键"""
        return self.is_key_just_pressed('p')
    
    def is_restart_pressed(self):
        """检查是否按下重启键"""
        return self.is_key_just_pressed('r')
    
    def is_quit_pressed(self):
        """检查是否按下退出键"""
        return self.is_key_just_pressed('escape')
    
    def get_movement_input(self):
        """获取移动输入状态
        
        Returns:
            dict: 移动输入状态
        """
        return {
            'move_left': self.can_move_left(),
            'move_right': self.can_move_right(),
            'rotate': self.can_rotate(),
            'soft_drop': self.should_soft_drop(),
            'hard_drop': self.should_hard_drop(),
            'pause': self.is_pause_pressed(),
            'restart': self.is_restart_pressed(),
            'quit': self.is_quit_pressed()
        }
    
    def reset_timers(self):
        """重置所有计时器"""
        self.last_move_time = 0
        self.last_rotation_time = 0
        self.last_soft_drop_time = 0
        self.key_timers.clear()
        self.key_last_repeat.clear()
    
    def set_move_delay(self, delay):
        """设置移动延迟
        
        Args:
            delay: 延迟时间（秒）
        """
        self.move_delay = max(0.05, delay)
    
    def set_rotation_delay(self, delay):
        """设置旋转延迟
        
        Args:
            delay: 延迟时间（秒）
        """
        self.rotation_delay = max(0.1, delay)
    
    def set_soft_drop_rate(self, rate):
        """设置软下落速率
        
        Args:
            rate: 下落间隔（秒）
        """
        self.soft_drop_rate = max(0.01, rate)
    
    def get_debug_info(self):
        """获取调试信息
        
        Returns:
            dict: 调试信息
        """
        return {
            'keys_pressed': list(self.keys_pressed),
            'keys_just_pressed': list(self.keys_just_pressed),
            'keys_just_released': list(self.keys_just_released),
            'active_timers': len(self.key_timers),
            'move_delay': self.move_delay,
            'rotation_delay': self.rotation_delay,
            'soft_drop_rate': self.soft_drop_rate
        }
    
    def __str__(self):
        """字符串表示"""
        pressed_keys = ', '.join(self.keys_pressed) if self.keys_pressed else 'None'
        return f"InputManager(pressed: {pressed_keys})"
    
    def __repr__(self):
        """详细字符串表示"""
        return self.__str__()