<execution domain="prompt-engineering">
  <process>
    # 思考模式提示词开发流程
    
    ```mermaid
    flowchart TD
      A[分析思考需求] --> B[确定所需思维模式]
      B --> C{选择适当组件}
      C -->|探索性思维| D[使用exploration标签]
      C -->|推理性思维| E[使用reasoning标签]
      C -->|规划性思维| F[使用plan标签]
      C -->|批判性思维| G[使用challenge标签]
      D --> H[设计思维导图表达]
      E --> I[设计推理图表达]
      F --> J[设计流程图表达]
      G --> K[设计逆向思维导图]
      H --> L[组装完整思考模式]
      I --> L
      J --> L
      K --> L
      L --> M[优化表达方式]
      M --> N[验证思考逻辑]
      N --> O[完成thought组件]
    ```
    
    ## 核心步骤详解
    
    1. **分析思考需求**
       - 明确需要解决的问题类型
       - 确定所需的思考深度和广度
    
    2. **选择适当组件**
       - 根据任务性质选择合适的思维模式组件
       - 不必强制使用全部四种组件，应按需选择
    
    3. **设计图形表达**
       - 为每种思维模式选择最适合的图形表达方式
       - 确保图形能够清晰展示思考逻辑和结构
    
    4. **验证思考逻辑**
       - 检查思维流程是否完整
       - 确保不同思维模式之间的连贯性
  </process>
  
  <guideline>
    ### 图形化表达原则
    
    - 使用图形作为主要表达方式，辅以简洁文字说明
    - 选择最适合特定思维模式的图表类型
    - 保持图表简洁明了，避免过度复杂
    - 确保图表能够独立表达核心思想
    
    ### 思维模式图表选择建议
    
    - **探索性思维(exploration)**
      - 优先使用思维导图(mindmap)
      - 适用于概念发散、头脑风暴
      - 核心问题置于中心，向外扩展可能性
    
    - **推理性思维(reasoning)**
      - 优先使用流程图(graph/flowchart)或时间线
      - 适用于逻辑推导、因果分析
      - 清晰展示前提到结论的推理路径
    
    - **规划性思维(plan)**
      - 优先使用甘特图(gantt)、流程图或序列图
      - 适用于项目规划、决策路径
      - 展示步骤间的依赖关系和时间顺序
    
    - **批判性思维(challenge)**
      - 优先使用逆向思维导图或四象限图
      - 适用于风险探索、假设检验
      - 聚焦于方案的潜在问题和限制条件
    
    ### 混合使用建议
    
    - 复杂问题可组合多种思维模式，按照"探索-批判-推理-计划"的顺序
    - 各思维模式间应有明确的逻辑承接关系
    - 保持风格一致性，便于整体理解
  </guideline>
  
  <rule>
    1. **思考组件必须图形化** - 每种思维模式必须至少包含一个图形化表达
    2. **图表必须符合语义** - 使用的图表类型必须与思维模式的语义匹配
    3. **文字必须精简** - 文字说明应简洁，仅用于补充图表无法表达的内容
    4. **思维模式边界明确** - 不同思维模式之间的职责边界必须清晰
    5. **可执行性保证** - 思考模式必须能够指导AI进行实际的思考过程
    6. **一致的表达风格** - 在同一个thought标签内保持一致的表达风格
    7. **思维全面性** - 确保覆盖关键思考维度，避免重要思考角度的遗漏
    8. **资源必须注册** - 创建新的thought资源后必须在 resource/thought.resource.md 中注册，否则@引用将无法正常工作
  </rule>
  
  <constraint>
    1. **图表复杂度限制** - 单个图表节点和连接数量应控制在可理解范围内
    2. **表达深度限制** - 思维展开不宜超过三层，以保持清晰度
    3. **AI理解能力限制** - 图表必须是AI能够理解的标准格式
    4. **渲染环境限制** - 考虑不同环境下图表渲染的兼容性
    5. **语言限制** - 图表中的文字应简洁明了，避免长句
  </constraint>
  
  <criteria>
    | 指标 | 通过标准 | 不通过标准 |
    |------|---------|-----------|
    | 图形表达清晰度 | 图表能独立表达核心思想 | 图表混乱或需大量文字解释 |
    | 思维模式匹配度 | 图表类型与思维模式匹配 | 图表类型与思维目的不符 |
    | 结构完整性 | 思考逻辑路径完整 | 思考路径有明显断点或跳跃 |
    | 表达简洁性 | 简明扼要，无冗余元素 | 过于复杂或重复表达 |
    | 实用指导性 | 能有效指导实际思考 | 过于抽象，难以应用 |
    | 思维覆盖面 | 覆盖问题的关键维度 | 遗漏重要思考角度 |
    | 视觉组织性 | 视觉层次清晰，重点突出 | 平面化设计，难以区分重点 |
  </criteria>
</execution> 