# 游戏板类
from config import GAME_CONFIG, CUTE_COLORS

class Board:
    """游戏板类，管理游戏区域和已放置的方块"""
    
    def __init__(self, width=None, height=None):
        """初始化游戏板
        
        Args:
            width: 游戏板宽度（格子数）
            height: 游戏板高度（格子数）
        """
        self.width = width or GAME_CONFIG['BOARD_WIDTH']
        self.height = height or GAME_CONFIG['BOARD_HEIGHT']
        self.grid = [[None for _ in range(self.width)] for _ in range(self.height)]
    
    def is_valid_position(self, tetromino, x, y, check_rotation=False):
        """检查方块在指定位置是否有效
        
        Args:
            tetromino: Tetromino对象
            x: X坐标
            y: Y坐标
            check_rotation: 是否检查旋转后的位置
            
        Returns:
            bool: 位置是否有效
        """
        if check_rotation:
            blocks = tetromino.get_rotated_blocks()
        else:
            blocks = tetromino.get_shape_blocks()
        
        for block_x, block_y in blocks:
            new_x = x + block_x
            new_y = y + block_y
            
            # 检查左右边界
            if new_x < 0 or new_x >= self.width:
                return False
            
            # 检查底部边界
            if new_y >= self.height:
                return False
            
            # 检查是否与已有方块冲突（允许在顶部上方）
            if new_y >= 0 and self.grid[new_y][new_x] is not None:
                return False
        
        return True
    
    def can_move_left(self, tetromino, x, y):
        """检查方块是否可以向左移动"""
        return self.is_valid_position(tetromino, x - 1, y)
    
    def can_move_right(self, tetromino, x, y):
        """检查方块是否可以向右移动"""
        return self.is_valid_position(tetromino, x + 1, y)
    
    def can_move_down(self, tetromino, x, y):
        """检查方块是否可以向下移动"""
        return self.is_valid_position(tetromino, x, y + 1)
    
    def can_rotate(self, tetromino, x, y):
        """检查方块是否可以旋转"""
        if not tetromino.can_rotate():
            return False
        return self.is_valid_position(tetromino, x, y, check_rotation=True)
    
    def try_wall_kick(self, tetromino, x, y):
        """尝试踢墙算法来完成旋转
        
        Args:
            tetromino: Tetromino对象
            x: 当前X坐标
            y: 当前Y坐标
            
        Returns:
            tuple: (是否成功, 新的x坐标, 新的y坐标)
        """
        if not tetromino.can_rotate():
            return False, x, y
        
        # 基本的踢墙尝试偏移
        kick_offsets = [(0, 0), (-1, 0), (1, 0), (0, -1), (-1, -1), (1, -1)]
        
        for dx, dy in kick_offsets:
            new_x, new_y = x + dx, y + dy
            if self.is_valid_position(tetromino, new_x, new_y, check_rotation=True):
                return True, new_x, new_y
        
        return False, x, y
    
    def place_tetromino(self, tetromino, x, y):
        """将方块放置到游戏板上
        
        Args:
            tetromino: Tetromino对象
            x: X坐标
            y: Y坐标
            
        Returns:
            bool: 是否成功放置
        """
        if not self.is_valid_position(tetromino, x, y):
            return False
        
        blocks = tetromino.get_shape_blocks()
        
        for block_x, block_y in blocks:
            new_x = x + block_x
            new_y = y + block_y
            
            if 0 <= new_y < self.height and 0 <= new_x < self.width:
                self.grid[new_y][new_x] = tetromino.get_color()
        
        return True
    
    def get_drop_position(self, tetromino, x, y):
        """获取方块的最终下落位置
        
        Args:
            tetromino: Tetromino对象
            x: 当前X坐标
            y: 当前Y坐标
            
        Returns:
            int: 最终Y坐标
        """
        drop_y = y
        while self.is_valid_position(tetromino, x, drop_y + 1):
            drop_y += 1
        return drop_y
    
    def clear_lines(self):
        """清除完整的行并返回清除的行数和被清除的行号列表"""
        lines_cleared = 0
        cleared_lines = []
        
        # 先找出所有完整的行
        full_lines = []
        for y in range(self.height):
            if self.is_line_full(y):
                full_lines.append(y)
        
        # 记录原始行号
        cleared_lines = full_lines[:]
        
        # 从下到上删除完整的行，并在删除的位置插入空行
        for y in sorted(full_lines, reverse=True):
            del self.grid[y]
            lines_cleared += 1
        
        # 在顶部添加相应数量的空行
        for _ in range(lines_cleared):
            self.grid.insert(0, [None for _ in range(self.width)])
        
        return lines_cleared, cleared_lines
    
    def get_full_lines(self):
        """获取所有完整行的行号列表"""
        full_lines = []
        for y in range(self.height):
            if self.is_line_full(y):
                full_lines.append(y)
        return full_lines
    
    def clear_specific_lines(self, line_numbers):
        """清除指定的行号列表
        
        Args:
            line_numbers: 要清除的行号列表
            
        Returns:
            int: 实际清除的行数
        """
        # 按从下到上的顺序排序，避免行号变化的问题
        sorted_lines = sorted(line_numbers, reverse=True)
        cleared_count = 0
        
        for line_num in sorted_lines:
            if 0 <= line_num < self.height and self.is_line_full(line_num):
                # 移除指定行
                del self.grid[line_num]
                # 在顶部添加新的空行
                self.grid.insert(0, [None for _ in range(self.width)])
                cleared_count += 1
        
        return cleared_count
    
    def simulate_line_clear(self):
        """模拟行消除，返回会被清除的行但不实际清除
        
        Returns:
            list: 会被清除的行号列表
        """
        return self.get_full_lines()
    
    def get_line_clear_animation_data(self):
        """获取行消除动画所需的数据
        
        Returns:
            dict: 包含动画数据的字典
        """
        full_lines = self.get_full_lines()
        animation_data = {
            'full_lines': full_lines,
            'line_count': len(full_lines),
            'is_tetris': len(full_lines) == 4,  # 四行消除
            'affected_blocks': []
        }
        
        # 收集被影响的方块信息
        for line_num in full_lines:
            line_blocks = []
            for x in range(self.width):
                if self.grid[line_num][x] is not None:
                    line_blocks.append({
                        'x': x,
                        'y': line_num,
                        'color': self.grid[line_num][x]
                    })
            animation_data['affected_blocks'].append(line_blocks)
        
        return animation_data
    
    def is_line_full(self, line):
        """检查指定行是否完整"""
        if line < 0 or line >= self.height:
            return False
        return all(cell is not None for cell in self.grid[line])
    
    def is_line_empty(self, line):
        """检查指定行是否为空"""
        if line < 0 or line >= self.height:
            return True
        return all(cell is None for cell in self.grid[line])
    
    def get_board_state(self):
        """获取游戏板状态"""
        return [row[:] for row in self.grid]  # 返回副本
    
    def get_filled_cells_count(self):
        """获取已填充的格子数量"""
        count = 0
        for row in self.grid:
            for cell in row:
                if cell is not None:
                    count += 1
        return count
    
    def get_height_profile(self):
        """获取每列的高度轮廓"""
        profile = []
        for x in range(self.width):
            height = 0
            for y in range(self.height):
                if self.grid[y][x] is not None:
                    height = self.height - y
                    break
            profile.append(height)
        return profile
    
    def is_game_over(self, tetromino=None, x=None, y=None):
        """检查游戏是否结束
        
        Args:
            tetromino: 要检查的方块（可选）
            x: 方块X坐标（可选）
            y: 方块Y坐标（可选）
            
        Returns:
            bool: 游戏是否结束
        """
        # 如果提供了方块，检查是否可以放置在起始位置
        if tetromino is not None and x is not None and y is not None:
            return not self.is_valid_position(tetromino, x, y)
        
        # 否则检查顶部几行是否有方块
        for y in range(min(2, self.height)):
            if any(cell is not None for cell in self.grid[y]):
                return True
        
        return False
    
    def clear_board(self):
        """清空游戏板"""
        self.grid = [[None for _ in range(self.width)] for _ in range(self.height)]
    
    def get_cell(self, x, y):
        """获取指定位置的格子内容
        
        Args:
            x: X坐标
            y: Y坐标
            
        Returns:
            颜色值或None
        """
        if 0 <= x < self.width and 0 <= y < self.height:
            return self.grid[y][x]
        return None
    
    def set_cell(self, x, y, color):
        """设置指定位置的格子内容
        
        Args:
            x: X坐标
            y: Y坐标
            color: 颜色值
        """
        if 0 <= x < self.width and 0 <= y < self.height:
            self.grid[y][x] = color
    
    def copy(self):
        """创建游戏板的副本"""
        new_board = Board(self.width, self.height)
        new_board.grid = [row[:] for row in self.grid]
        return new_board
    
    def __str__(self):
        """字符串表示（用于调试）"""
        result = []
        for row in self.grid:
            row_str = ""
            for cell in row:
                row_str += "█" if cell is not None else "·"
            result.append(row_str)
        return "\n".join(result)
    
    def __repr__(self):
        """详细字符串表示"""
        return f"Board({self.width}x{self.height}, filled={self.get_filled_cells_count()})"