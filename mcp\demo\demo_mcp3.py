import fastmcp
from sse_starlette.sse import EventSourceResponse
import asyncio

app = fastmcp.FastMCP()

class MathOperations:
    @app.tool(name="add")
    def add(self, a: float, b: float) -> float:
        """Add two numbers."""
        return a + b

    @app.tool(name="subtract")
    def subtract(self, a: float, b: float) -> float:
        """Subtract the second number from the first."""
        return a - b

    @app.tool(name="multiply")
    def multiply(self, a: float, b: float) -> float:
        """Multiply two numbers."""
        return a * b

    @app.tool(name="divide")
    def divide(self, a: float, b: float) -> float:
        """Divide the first number by the second."""
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return a / b

async def event_generator():
    while True:
        math_ops = MathOperations()
        result = "1 + 2 result is {}".format(math_ops.add(1, 2))
        yield {"event": "message", "data": result}
        await asyncio.sleep(1)

# 定义资源类（补充 name 和 uri 属性）
class SseResource:
    name = "SseResource"  # 关键修改：添加 name 属性
    uri = "/sse"          # 已有 uri 属性

    async def get(self):  # 定义 GET 请求处理方法
        return EventSourceResponse(event_generator())

app.add_resource(SseResource())  # 注册资源实例

if __name__ == "__main__":
    app.run()  # 移除 host 和 port 参数