# 主游戏类
import pygame
import sys
import time
from enum import Enum
from config import GAME_CONFIG
from board import Board
from tetromino import Tetromino
from score_manager import ScoreManager
from ui import UI
from input_manager import InputManager
from fall_manager import FallManager

class GameState(Enum):
    """游戏状态枚举"""
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"
    GAME_OVER = "game_over"
    RESTARTING = "restarting"

class Game:
    """主游戏控制器，管理游戏状态和主循环"""
    
    def __init__(self):
        """初始化游戏"""
        # 初始化pygame
        pygame.init()
        
        # 创建屏幕
        self.screen = pygame.display.set_mode((GAME_CONFIG['WINDOW_WIDTH'], GAME_CONFIG['WINDOW_HEIGHT']))
        pygame.display.set_caption("可爱俄罗斯方块")
        
        # 创建游戏组件
        self.board = Board()
        self.score_manager = ScoreManager()
        self.ui = UI(self.screen)
        self.input_manager = InputManager()
        self.fall_manager = FallManager(self.score_manager)
        
        # 游戏状态
        self.state = GameState.PLAYING
        self.running = True
        
        # 当前方块
        self.current_piece = Tetromino()
        self.current_x = GAME_CONFIG['BOARD_WIDTH'] // 2 - 2
        self.current_y = 0
        
        # 下一个方块
        self.next_piece = Tetromino()
        
        # 游戏时钟
        self.clock = pygame.time.Clock()
        
        # 游戏统计
        self.game_start_time = time.time()
        self.total_pieces_dropped = 0
        
        print("游戏初始化完成")
    
    def run(self):
        """运行游戏主循环"""
        print("游戏开始运行")
        
        while self.running:
            # 获取事件
            events = pygame.event.get()
            
            # 处理事件
            self.handle_events(events)
            
            # 更新游戏状态
            self.update()
            
            # 渲染游戏
            self.render()
            
            # 控制帧率
            self.clock.tick(GAME_CONFIG['FPS'])
        
        # 游戏结束清理
        self.cleanup()
    
    def handle_events(self, events):
        """处理游戏事件
        
        Args:
            events: pygame事件列表
        """
        # 处理退出事件
        for event in events:
            if event.type == pygame.QUIT:
                self.running = False
                return
        
        # 更新输入管理器
        self.input_manager.update(events)
        
        # 获取输入状态
        movement = self.input_manager.get_movement_input()
        
        # 处理全局输入
        if movement['quit']:
            self.running = False
            return
        
        # 根据游戏状态处理输入
        if self.state == GameState.PLAYING:
            self.handle_playing_input(movement)
        elif self.state == GameState.PAUSED:
            self.handle_paused_input(movement)
        elif self.state == GameState.GAME_OVER:
            self.handle_game_over_input(movement)
    
    def handle_playing_input(self, movement):
        """处理游戏中的输入
        
        Args:
            movement: 移动输入状态
        """
        # 处理暂停
        if movement['pause']:
            self.pause_game()
            return
        
        # 处理方块移动
        if movement['move_left']:
            if self.board.can_move_left(self.current_piece, self.current_x, self.current_y):
                self.current_x -= 1
                if self.fall_manager.is_locking:
                    self.fall_manager.add_lock_move()
        
        if movement['move_right']:
            if self.board.can_move_right(self.current_piece, self.current_x, self.current_y):
                self.current_x += 1
                if self.fall_manager.is_locking:
                    self.fall_manager.add_lock_move()
        
        # 处理方块旋转
        if movement['rotate']:
            if self.board.can_rotate(self.current_piece, self.current_x, self.current_y):
                self.current_piece.rotate()
                if self.fall_manager.is_locking:
                    self.fall_manager.add_lock_move()
            else:
                # 尝试踢墙
                success, new_x, new_y = self.board.try_wall_kick(self.current_piece, self.current_x, self.current_y)
                if success:
                    self.current_piece.rotate()
                    self.current_x, self.current_y = new_x, new_y
                    if self.fall_manager.is_locking:
                        self.fall_manager.add_lock_move()
        
        # 处理软下落
        if movement['soft_drop']:
            if self.board.can_move_down(self.current_piece, self.current_x, self.current_y):
                self.current_y += 1
                self.score_manager.add_drop_score(1, is_hard_drop=False)
                self.fall_manager.force_fall()
        
        # 处理硬下落
        if movement['hard_drop']:
            drop_distance = 0
            while self.board.can_move_down(self.current_piece, self.current_x, self.current_y):
                self.current_y += 1
                drop_distance += 1
            
            if drop_distance > 0:
                self.score_manager.add_drop_score(drop_distance, is_hard_drop=True)
                self.fall_manager.force_fall()
                # 硬下落后立即锁定
                self.lock_current_piece()
    
    def handle_paused_input(self, movement):
        """处理暂停状态的输入
        
        Args:
            movement: 移动输入状态
        """
        if movement['pause']:
            self.resume_game()
    
    def handle_game_over_input(self, movement):
        """处理游戏结束状态的输入
        
        Args:
            movement: 移动输入状态
        """
        if movement['restart']:
            self.restart_game()
    
    def update(self):
        """更新游戏状态"""
        if self.state == GameState.PLAYING:
            self.update_playing()
        elif self.state == GameState.RESTARTING:
            self.complete_restart()
    
    def update_playing(self):
        """更新游戏中的状态"""
        # 更新下落速度
        self.fall_manager.update_fall_speed()
        
        # 检查自动下落
        if self.fall_manager.should_fall():
            self.handle_natural_fall()
    
    def handle_natural_fall(self):
        """处理自然下落"""
        # 使用下落管理器处理下落逻辑
        new_y, should_lock, lock_reason = self.fall_manager.handle_fall_logic(
            self.board, self.current_piece, self.current_x, self.current_y
        )
        
        if should_lock:
            # 锁定当前方块
            self.lock_current_piece()
        else:
            # 继续下落
            self.current_y = new_y
    
    def lock_current_piece(self):
        """锁定当前方块，并播放放置动画"""
        # 放置方块到游戏板
        if self.board.place_tetromino(self.current_piece, self.current_x, self.current_y):
            # 添加放置奖励
            self.score_manager.add_placement_bonus()
            self.total_pieces_dropped += 1
            
            # 播放方块放置动画
            self.play_place_animation()
            
            # 检查行消除
            lines_cleared, cleared_lines = self.board.clear_lines()
            if lines_cleared > 0:
                earned_score = self.score_manager.add_line_score(lines_cleared)
                print(f"消除 {lines_cleared} 行，获得 {earned_score} 分")
                
                # 播放行消除动画
                self.play_line_clear_animation(cleared_lines)
            
            # 生成新方块
            self.spawn_new_piece()
            
            # 重置下落管理器
            self.fall_manager.reset_lock_delay()
    
    def play_place_animation(self):
        """播放方块放置动画"""
        # 创建动画数据
        animation_data = {
            'type': 'place',
            'progress': 0.0,
            'tetromino': self.current_piece,
            'x': self.current_x,
            'y': self.current_y
        }
        
        # 动画持续时间（毫秒）
        animation_duration = 300
        start_time = pygame.time.get_ticks()
        
        # 播放动画
        while True:
            current_time = pygame.time.get_ticks()
            elapsed = current_time - start_time
            
            # 计算动画进度
            if elapsed >= animation_duration:
                break
            
            animation_data['progress'] = elapsed / animation_duration
            
            # 绘制游戏状态
            self.draw(animation_data=animation_data)
            
            # 控制帧率
            pygame.time.delay(16)  # 约60FPS
    
    def play_line_clear_animation(self, cleared_lines):
        """播放行消除动画
        
        Args:
            cleared_lines: 被消除的行索引列表
        """
        if not cleared_lines:
            return
        
        # 创建动画数据
        animation_data = {
            'type': 'line_clear',
            'full_lines': cleared_lines,
            'progress': 0.0
        }
        
        # 动画持续时间（毫秒）
        animation_duration = 500
        start_time = pygame.time.get_ticks()
        
        # 播放动画
        while True:
            current_time = pygame.time.get_ticks()
            elapsed = current_time - start_time
            
            # 计算动画进度
            if elapsed >= animation_duration:
                break
            
            animation_data['progress'] = elapsed / animation_duration
            
            # 处理输入（允许退出游戏）
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    return
            
            # 绘制游戏状态（使用新的draw方法）
            self.ui.draw_background()
            self.ui.draw_board(self.board)
            self.ui.draw_line_clear_animation(animation_data, animation_data['progress'])
            self.ui.draw_score_panel(self.score_manager)
            self.ui.draw_next_piece(self.next_piece)
            pygame.display.flip()
            
            # 控制帧率
            pygame.time.delay(16)  # 约60FPS
        else:
            # 无法放置方块，游戏结束
            self.game_over()
    
    def spawn_new_piece(self):
        """生成新方块"""
        self.current_piece = self.next_piece
        self.next_piece = Tetromino()
        self.current_x = GAME_CONFIG['BOARD_WIDTH'] // 2 - 2
        self.current_y = 0
        
        # 检查游戏结束
        if self.board.is_game_over(self.current_piece, self.current_x, self.current_y):
            self.game_over()
    
    def pause_game(self):
        """暂停游戏"""
        if self.state == GameState.PLAYING:
            self.state = GameState.PAUSED
            self.fall_manager.pause()
            print("游戏暂停")
    
    def resume_game(self):
        """恢复游戏"""
        if self.state == GameState.PAUSED:
            self.state = GameState.PLAYING
            self.fall_manager.resume()
            print("游戏恢复")
    
    def game_over(self):
        """游戏结束"""
        self.state = GameState.GAME_OVER
        self.fall_manager.pause()
        
        # 保存高分
        if self.score_manager.save_high_score():
            print("新的最高分！")
        
        print(f"游戏结束！最终分数: {self.score_manager.get_current_score()}")
    
    def restart_game(self):
        """重新开始游戏"""
        self.state = GameState.RESTARTING
        print("重新开始游戏")
    
    def complete_restart(self):
        """完成游戏重启"""
        # 重置所有组件
        self.board.clear_board()
        self.score_manager.reset()
        self.fall_manager.reset()
        self.input_manager.reset_timers()
        
        # 重置游戏状态
        self.current_piece = Tetromino()
        self.next_piece = Tetromino()
        self.current_x = GAME_CONFIG['BOARD_WIDTH'] // 2 - 2
        self.current_y = 0
        
        # 重置统计
        self.game_start_time = time.time()
        self.total_pieces_dropped = 0
        
        # 切换到游戏状态
        self.state = GameState.PLAYING
        print("游戏重启完成")
    
    def draw(self, animation_data=None):
        """绘制游戏状态
        
        Args:
            animation_data: 动画数据，包含类型和进度
        """
        # 绘制背景
        self.ui.draw_background()
        
        # 绘制标题
        self.ui.draw_title()
        
        # 绘制游戏板
        self.ui.draw_board(self.board)
        
        # 绘制当前方块（如果不是游戏结束状态）
        if self.state != GameState.GAME_OVER:
            # 检查是否有方块放置动画
            if animation_data and animation_data.get('type') == 'place':
                # 使用动画数据绘制方块
                self.ui.draw_tetromino(
                    animation_data['tetromino'], 
                    animation_data['x'], 
                    animation_data['y'],
                    animation_data
                )
            else:
                # 正常绘制当前方块
                self.ui.draw_tetromino(self.current_piece, self.current_x, self.current_y)
                self.ui.draw_ghost_piece(self.current_piece, self.current_x, self.current_y, self.board)
        
        # 绘制UI元素
        self.ui.draw_score_panel(self.score_manager)
        self.ui.draw_level_progress_bar(self.score_manager)
        self.ui.draw_next_piece(self.next_piece)
        self.ui.draw_controls_help()
        
        # 根据状态绘制覆盖界面
        if self.state == GameState.PAUSED:
            self.ui.draw_pause_screen()
        elif self.state == GameState.GAME_OVER:
            self.ui.draw_game_over_screen(self.score_manager)
        
        # 更新显示
        pygame.display.flip()
    
    def render(self):
        """渲染游戏"""
        self.draw()
    
    def get_game_stats(self):
        """获取游戏统计信息
        
        Returns:
            dict: 游戏统计
        """
        current_time = time.time()
        game_duration = current_time - self.game_start_time
        
        return {
            'state': self.state.value,
            'game_duration': game_duration,
            'total_pieces_dropped': self.total_pieces_dropped,
            'current_piece': self.current_piece.get_shape_name(),
            'next_piece': self.next_piece.get_shape_name(),
            'current_position': (self.current_x, self.current_y),
            'score_stats': self.score_manager.get_statistics(),
            'fall_stats': self.fall_manager.get_fall_stats(),
            'board_filled_cells': self.board.get_filled_cells_count()
        }
    
    def is_running(self):
        """检查游戏是否正在运行
        
        Returns:
            bool: 游戏是否运行中
        """
        return self.running
    
    def is_playing(self):
        """检查是否在游戏中
        
        Returns:
            bool: 是否在游戏中
        """
        return self.state == GameState.PLAYING
    
    def is_paused(self):
        """检查是否暂停
        
        Returns:
            bool: 是否暂停
        """
        return self.state == GameState.PAUSED
    
    def is_game_over(self):
        """检查是否游戏结束
        
        Returns:
            bool: 是否游戏结束
        """
        return self.state == GameState.GAME_OVER
    
    def cleanup(self):
        """游戏结束清理"""
        # 保存最终分数
        self.score_manager.save_high_score()
        
        # 退出pygame
        pygame.quit()
        
        print("游戏清理完成")
    
    def __str__(self):
        """字符串表示"""
        return f"Game(state={self.state.value}, score={self.score_manager.get_current_score()})"
    
    def __repr__(self):
        """详细字符串表示"""
        return self.__str__()