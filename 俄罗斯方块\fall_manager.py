# 方块自动下落管理器
import time
from config import GAME_CONFIG

class FallManager:
    """方块自动下落管理器，处理基于时间的自动下落机制"""
    
    def __init__(self, score_manager=None):
        """初始化下落管理器
        
        Args:
            score_manager: ScoreManager对象，用于获取等级相关的下落速度
        """
        self.score_manager = score_manager
        self.last_fall_time = time.time()
        self.fall_interval = GAME_CONFIG['INITIAL_FALL_SPEED'] / 1000.0  # 转换为秒
        self.is_paused = False
        self.lock_delay = 0.5  # 锁定延迟（秒）
        self.lock_start_time = None  # 锁定开始时间
        self.is_locking = False  # 是否正在锁定过程中
        self.lock_moves = 0  # 锁定期间的移动次数
        self.max_lock_moves = 15  # 最大锁定移动次数
        
        # 下落统计
        self.total_falls = 0
        self.forced_falls = 0  # 强制下落次数
        self.natural_falls = 0  # 自然下落次数
    
    def update_fall_speed(self):
        """根据等级更新下落速度"""
        if self.score_manager:
            speed_ms = self.score_manager.get_fall_speed()
            self.fall_interval = speed_ms / 1000.0
        else:
            # 使用默认速度
            self.fall_interval = GAME_CONFIG['INITIAL_FALL_SPEED'] / 1000.0
    
    def should_fall(self):
        """检查方块是否应该自动下落
        
        Returns:
            bool: 是否应该下落
        """
        if self.is_paused:
            return False
        
        current_time = time.time()
        time_since_last_fall = current_time - self.last_fall_time
        
        if time_since_last_fall >= self.fall_interval:
            self.last_fall_time = current_time
            self.total_falls += 1
            self.natural_falls += 1
            return True
        
        return False
    
    def force_fall(self):
        """强制方块下落（用于软下落）"""
        self.last_fall_time = time.time()
        self.total_falls += 1
        self.forced_falls += 1
    
    def start_lock_delay(self):
        """开始锁定延迟"""
        if not self.is_locking:
            self.is_locking = True
            self.lock_start_time = time.time()
            self.lock_moves = 0
    
    def reset_lock_delay(self):
        """重置锁定延迟（当方块能够继续下落时）"""
        self.is_locking = False
        self.lock_start_time = None
        self.lock_moves = 0
    
    def add_lock_move(self):
        """添加锁定期间的移动"""
        if self.is_locking:
            self.lock_moves += 1
    
    def should_lock(self):
        """检查方块是否应该被锁定
        
        Returns:
            bool: 是否应该锁定
        """
        if not self.is_locking:
            return False
        
        current_time = time.time()
        
        # 检查时间延迟
        if current_time - self.lock_start_time >= self.lock_delay:
            return True
        
        # 检查移动次数限制
        if self.lock_moves >= self.max_lock_moves:
            return True
        
        return False
    
    def can_continue_falling(self, board, tetromino, x, y):
        """检查方块是否可以继续下落
        
        Args:
            board: Board对象
            tetromino: Tetromino对象
            x: 当前X坐标
            y: 当前Y坐标
            
        Returns:
            bool: 是否可以继续下落
        """
        return board.can_move_down(tetromino, x, y)
    
    def handle_fall_logic(self, board, tetromino, x, y):
        """处理下落逻辑
        
        Args:
            board: Board对象
            tetromino: Tetromino对象
            x: 当前X坐标
            y: 当前Y坐标
            
        Returns:
            tuple: (new_y, should_lock, lock_reason)
        """
        # 检查是否可以下落
        if self.can_continue_falling(board, tetromino, x, y):
            # 可以下落，重置锁定延迟
            self.reset_lock_delay()
            return y + 1, False, None
        else:
            # 不能下落，开始或继续锁定过程
            if not self.is_locking:
                self.start_lock_delay()
            
            # 检查是否应该锁定
            if self.should_lock():
                lock_reason = "time" if time.time() - self.lock_start_time >= self.lock_delay else "moves"
                return y, True, lock_reason
            else:
                return y, False, None
    
    def pause(self):
        """暂停下落"""
        self.is_paused = True
    
    def resume(self):
        """恢复下落"""
        self.is_paused = False
        # 重置时间以避免立即下落
        self.last_fall_time = time.time()
    
    def reset(self):
        """重置下落管理器"""
        self.last_fall_time = time.time()
        self.is_paused = False
        self.reset_lock_delay()
        self.total_falls = 0
        self.forced_falls = 0
        self.natural_falls = 0
    
    def set_fall_interval(self, interval_ms):
        """设置下落间隔
        
        Args:
            interval_ms: 下落间隔（毫秒）
        """
        self.fall_interval = max(50, interval_ms) / 1000.0
    
    def set_lock_delay(self, delay_seconds):
        """设置锁定延迟
        
        Args:
            delay_seconds: 锁定延迟（秒）
        """
        self.lock_delay = max(0.1, delay_seconds)
    
    def set_max_lock_moves(self, max_moves):
        """设置最大锁定移动次数
        
        Args:
            max_moves: 最大移动次数
        """
        self.max_lock_moves = max(1, max_moves)
    
    def get_fall_stats(self):
        """获取下落统计信息
        
        Returns:
            dict: 下落统计
        """
        return {
            'total_falls': self.total_falls,
            'natural_falls': self.natural_falls,
            'forced_falls': self.forced_falls,
            'current_interval': self.fall_interval,
            'is_paused': self.is_paused,
            'is_locking': self.is_locking,
            'lock_moves': self.lock_moves,
            'lock_time_remaining': max(0, self.lock_delay - (time.time() - self.lock_start_time)) if self.is_locking else 0
        }
    
    def get_time_to_next_fall(self):
        """获取距离下次下落的时间
        
        Returns:
            float: 剩余时间（秒）
        """
        if self.is_paused:
            return float('inf')
        
        current_time = time.time()
        time_since_last_fall = current_time - self.last_fall_time
        return max(0, self.fall_interval - time_since_last_fall)
    
    def get_fall_progress(self):
        """获取下落进度（0.0到1.0）
        
        Returns:
            float: 下落进度
        """
        if self.is_paused:
            return 0.0
        
        current_time = time.time()
        time_since_last_fall = current_time - self.last_fall_time
        return min(1.0, time_since_last_fall / self.fall_interval)
    
    def predict_next_fall_time(self):
        """预测下次下落的时间
        
        Returns:
            float: 下次下落的时间戳
        """
        if self.is_paused:
            return float('inf')
        
        return self.last_fall_time + self.fall_interval
    
    def is_fall_imminent(self, threshold=0.1):
        """检查下落是否即将发生
        
        Args:
            threshold: 时间阈值（秒）
            
        Returns:
            bool: 下落是否即将发生
        """
        return self.get_time_to_next_fall() <= threshold
    
    def get_debug_info(self):
        """获取调试信息
        
        Returns:
            dict: 调试信息
        """
        current_time = time.time()
        return {
            'fall_interval': self.fall_interval,
            'time_since_last_fall': current_time - self.last_fall_time,
            'time_to_next_fall': self.get_time_to_next_fall(),
            'fall_progress': self.get_fall_progress(),
            'is_paused': self.is_paused,
            'is_locking': self.is_locking,
            'lock_delay': self.lock_delay,
            'lock_moves': self.lock_moves,
            'max_lock_moves': self.max_lock_moves,
            'stats': self.get_fall_stats()
        }
    
    def __str__(self):
        """字符串表示"""
        status = "paused" if self.is_paused else "active"
        locking = " (locking)" if self.is_locking else ""
        return f"FallManager({status}, interval={self.fall_interval:.3f}s{locking})"
    
    def __repr__(self):
        """详细字符串表示"""
        return self.__str__()