# 测试用例生成工具使用说明

## 1. 功能概述

测试用例生成工具是一个基于大语言模型的自动化测试用例生成工具，可以根据需求文档快速生成结构化的测试用例。本工具支持多种模型选择，提供灵活的提示词配置，并支持多种格式的测试用例导出。

## 2. 界面布局

工具界面分为左右两个主要区域：

### 2.1 左侧区域（输入区）

- **需求文档部分**
  - 文档输入框：可直接输入或粘贴需求文档内容
  - "打开文档..."按钮：支持打开Word文档（.docx格式）
  - "清空"按钮：快速清除当前文档内容

- **提示词设置**
  - 系统提示词：设置AI的角色和任务定位
  - 用户提示词：指定测试用例的生成要求
  - 快速模板：提供多种预设的测试用例格式模板
    * 标准格式：包含ID/描述/步骤/预期结果
    * 简化格式：包含场景/步骤/结果
    * 详细格式：包含优先级和前置条件
    * 自定义格式：可自定义提示词

- **API配置**
  - API密钥：配置访问大语言模型的密钥
  - API URL：设置API服务器地址
  - 模型选择：支持多种模型
    * GPT-4 Turbo
    * GPT-3.5 Turbo
    * Claude 3 Opus
    * Claude 3 Sonnet
    * Deepseek Chat/Coder/Math
    * 通义千问系列（Max/Plus/Turbo）
  - 温度参数：调节生成内容的创造性

### 2.2 右侧区域（输出区）

- **测试用例预览**
  - 原始文本：显示Markdown格式的原始测试用例
  - 渲染预览：实时预览格式化后的测试用例效果

- **保存选项**
  - 支持多种导出格式：
    * Markdown格式 (.md)
    * Word文档 (.docx)
    * PDF文档 (.pdf)
    * Excel文档 (.xlsx)

## 3. 使用步骤

1. **准备需求文档**
   - 直接在输入框中输入/粘贴需求文档
   - 或通过"打开文档..."按钮导入Word文档

2. **配置生成参数**
   - 选择合适的提示词模板或自定义提示词
   - 配置API密钥和选择合适的模型
   - 根据需要调整温度参数

3. **生成测试用例**
   - 点击"生成测试用例"按钮开始生成
   - 在进度条查看生成进度
   - 可随时通过"取消"按钮终止生成过程

4. **预览和保存**
   - 在预览区查看生成的测试用例
   - 选择需要的导出格式
   - 点击"保存测试用例"按钮导出

## 4. 注意事项

1. **API配置**
   - 首次使用需要配置有效的API密钥
   - 确保网络能够正常访问API服务器

2. **模型选择建议**
   - GPT-4 Turbo：适用于复杂场景，生成质量最高
   - GPT-3.5 Turbo：适合一般场景，性价比较高
   - Claude系列：擅长长文本理解和结构化输出
   - Deepseek系列：适合特定领域的测试用例生成
   - 通义千问系列：中文场景表现优秀

3. **文档要求**
   - 需求文档应清晰描述功能和业务逻辑
   - 支持中英文混合输入
   - Word文档应为.docx格式

4. **保存格式说明**
   - Markdown：保持原始格式，适合后续编辑
   - Word：自动处理标题层级，适合文档共享
   - PDF：固定格式，适合正式场合使用
   - Excel：表格化展示，适合测试执行跟踪

## 5. 常见问题

1. **生成失败**
   - 检查API密钥是否正确
   - 确认网络连接是否正常
   - 验证需求文档是否为空

2. **保存失败**
   - 确保目标目录有写入权限
   - 检查文件是否被其他程序占用
   - 验证文件名是否合法

3. **界面卡顿**
   - 大量文本生成时可能出现短暂卡顿，属于正常现象
   - 可通过取消按钮随时终止生成过程

## 6. 更新记录

### v1.0.0
- 初始版本发布
- 支持多种模型和导出格式
- 提供基础的测试用例生成功能 