[common]
server_port = 16789
log_level = info
log_file = runlog.log
#定时计划，每天早上6点出数据
#exec_time=06:00
#hs域账号信息，用于爬数据
hs_account=xiayk10729
hs_account_passwd=xxxxxx

#数据库相关配置
[DB]
db_oracle_ip = ************
db_oracle_port = 1521
db_oracle_SID = ORCL
db_oracle_user = o45_auto
db_oracle_passwd = o45_auto

[JIRA]
#查询周期，单位月，默认查询更新历史1年的数据(含当月)
#jira_query_month = 1


#blade相关配置
[blade]
#测试云平台的项目授权码，注意不是个人授权码
blade_access_code = vTy85AYI7F27iqAmbV3ZDKoVT8UmEPIzXK5F41W4PsYuURxgAt-kvYMk5nFHVKss-ptERIXZTR7eYidiq97Brw

;单点登录url，无需修改
cas_url=https://hs-cas.hundsun.com/cas/login?service=https://blade.hundsun.com/system/shiro-cas
;域账号和密码
;是否定时运行，1：是，0：否
blade_istime=0

;需要统计测试用例的项目（填写下方配置的项目名称），多个项目用','分隔，如（O45,O32）
query_project=UC3.0

dailyquery_project=O4.5,O4.5-01.04,O4.5-01.03,O4.5-01.01,O4.5,O3.2,UFX,UC3.0,GAMS3.0,FC

[O4.5]
blade_project=O4.5
projectID=7f8781e5a1594452855202a7bc14289f
vId=fc1c8e8764
;case_tree_parent_id=72da217fd55e469599c704cc09a992f6
;根目录
case_tree_parent_id=9093f83e2d6941e8acc54bfb9a4786fa
interface_tree_parent_ids=d3dba8ac19164bffa9cef5bafb8e2a2b,9e8a3b57b29d49af86b2eada9bd152cf
ignore_case_tree=K-开发用例,Z-工具类,待删除,O4.5-作废，不要再往这里加用例,1-系统测试-解决方案层
only_case_tree=
;only_case_tree=07资管-UI自动化T+0,09银信-UI自动化T+0
branchid=
;用例目录层级
caselevel=3
;测试任务目录增加模板，按顺序添加，用逗号分割
tasktemplate=01解决方案测试,02E2E,03集成,04性能,05可靠性
#可靠性的场景，都按自动化用例附加
special_attach_count=165
;是否启用用例状态为启用才可以执行，启用为1，不启用为0
is_status=1

[O4.5-01.04]
blade_project=O4.5
projectID=7f8781e5a1594452855202a7bc14289f
vId=fc1c8e8764
;case_tree_parent_id=72da217fd55e469599c704cc09a992f6
;根目录
case_tree_parent_id=9093f83e2d6941e8acc54bfb9a4786fa
interface_tree_parent_ids=d3dba8ac19164bffa9cef5bafb8e2a2b,9e8a3b57b29d49af86b2eada9bd152cf
ignore_case_tree=K-开发用例,Z-工具类,待删除,O4.5-作废，不要再往这里加用例,1-系统测试-解决方案层
only_case_tree=
;only_case_tree=07资管-UI自动化T+0,09银信-UI自动化T+0
branchid=89470839f3
;用例目录层级
caselevel=1
;测试任务目录增加模板，按顺序添加，用逗号分割
tasktemplate=01解决方案测试,02E2E,03集成,04性能,05可靠性
;是否启用用例状态为启用才可以执行，启用为1，不启用为0
is_status=0


[O4.5-01.03]
blade_project=O4.5
projectID=7f8781e5a1594452855202a7bc14289f
vId=fc1c8e8764
;case_tree_parent_id=72da217fd55e469599c704cc09a992f6
;根目录
case_tree_parent_id=9093f83e2d6941e8acc54bfb9a4786fa
interface_tree_parent_ids=d3dba8ac19164bffa9cef5bafb8e2a2b,9e8a3b57b29d49af86b2eada9bd152cf
ignore_case_tree=K-开发用例,Z-工具类,待删除,O4.5-作废，不要再往这里加用例,1-系统测试-解决方案层
only_case_tree=
;only_case_tree=07资管-UI自动化T+0,09银信-UI自动化T+0
branchid=7a8ddd0b3d
;用例目录层级
caselevel=1
;测试任务目录增加模板，按顺序添加，用逗号分割
tasktemplate=01解决方案测试,02E2E,03集成,04性能,05可靠性
;是否启用用例状态为启用才可以执行，启用为1，不启用为0
is_status=0


[O4.5-01.01]
blade_project=O4.5
projectID=7f8781e5a1594452855202a7bc14289f
vId=fc1c8e8764
;case_tree_parent_id=72da217fd55e469599c704cc09a992f6
;根目录
case_tree_parent_id=9093f83e2d6941e8acc54bfb9a4786fa
interface_tree_parent_ids=d3dba8ac19164bffa9cef5bafb8e2a2b,9e8a3b57b29d49af86b2eada9bd152cf
ignore_case_tree=K-开发用例,Z-工具类,待删除,O4.5-作废，不要再往这里加用例,1-系统测试-解决方案层
only_case_tree=
;only_case_tree=07资管-UI自动化T+0,09银信-UI自动化T+0
branchid=f314fc16e2
;用例目录层级
caselevel=1
;测试任务目录增加模板，按顺序添加，用逗号分割
tasktemplate=01解决方案测试,02E2E,03集成,04性能,05可靠性
;是否启用用例状态为启用才可以执行，启用为1，不启用为0
is_status=0



[O3.2]
blade_project=O3.2
projectID=126d3035d2244a88bb35407afb94f5c8
vId=126d3035d2
case_tree_parent_id=2130ca68988045d4b99c31f95fa4b965
ignore_case_tree=1000 废弃用例
;用例目录层级
caselevel=1

[UFX]
blade_project=UFX
projectID=817c301e951e44d7a9ab70cccebba708
vId=
case_tree_parent_id=b29365344c904aa39cb73c277d30a399
ignore_case_tree=
;用例目录层级
caselevel=1


[UC3.0]
blade_project=UC3.0
projectID=53ac607071f545f5ba6f8872f86bad02
vId=53ac607071
;case_tree_parent_id=b29365344c904aa39cb73c277d30a399
case_tree_parent_id=2fc0870265374e588ffc767e71e2fc65
ignore_case_tree=
;用例目录层级
caselevel=1

[GAMS3.0]
blade_project=GAMS3.0
projectID=99e69ba5f04a47799d40c9e4e180ba3b
vId=
case_tree_parent_id=c63b6e3bf77e4630a888bdeab613a960
ignore_case_tree=00分支补丁版本用例,01功能测试,02性能测试,03可靠性测试,04安全测试,05易用性测试,06兼容性测试,07维护性测试,08UI自动化测试,09修改单相关用例-历史待删除
;用例目录层级
caselevel=1

[FC]
blade_project=FC
projectID=3ee8e5c035564e2ead943dfca7f3820e
vId=3ee8e5c035
;case_tree_parent_id=b29365344c904aa39cb73c277d30a399
case_tree_parent_id=bc0d943ab55e4e619dbbf6f0072a9aea
ignore_case_tree=
;用例目录层级
caselevel=3
